"""
Multi-Agent Reinforcement Learning for Federated Learning.
Each client is an independent agent learning to optimize its contribution.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical, Normal
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from sklearn.base import BaseEstimator, RegressorMixin
import copy
import time

logger = logging.getLogger(__name__)


class ClientAgent(nn.Module):
    """Individual client agent in multi-agent federated learning."""

    def __init__(self, state_dim: int, action_dim: int, agent_id: int,
                 hidden_dims: List[int] = [128, 64]):
        super(ClientAgent, self).__init__()

        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim

        # Policy network
        policy_layers = []
        prev_dim = state_dim

        for hidden_dim in hidden_dims:
            policy_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.<PERSON>LU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim

        self.policy_layers = nn.Sequential(*policy_layers)

        # Action heads for different action types
        self.participation_head = nn.Linear(prev_dim, 2)  # Participate or not
        self.effort_head = nn.Linear(prev_dim, 1)         # Training effort (continuous)
        self.sharing_head = nn.Linear(prev_dim, 1)        # Data sharing ratio (continuous)

        # Value network
        value_layers = []
        prev_dim = state_dim

        for hidden_dim in hidden_dims:
            value_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim

        value_layers.append(nn.Linear(prev_dim, 1))
        self.value_network = nn.Sequential(*value_layers)

    def forward(self, state):
        """Forward pass through the networks."""
        # Policy
        policy_features = self.policy_layers(state)

        # Participation (discrete)
        participation_logits = self.participation_head(policy_features)
        participation_probs = F.softmax(participation_logits, dim=-1)

        # Effort and sharing (continuous, normalized to [0, 1])
        effort = torch.sigmoid(self.effort_head(policy_features))
        sharing = torch.sigmoid(self.sharing_head(policy_features))

        # Value
        value = self.value_network(state)

        return participation_probs, effort, sharing, value

    def get_action(self, state, training: bool = True):
        """Sample action from policy."""
        participation_probs, effort, sharing, value = self.forward(state)

        # Sample participation
        participation_dist = Categorical(participation_probs)
        participation = participation_dist.sample()
        participation_log_prob = participation_dist.log_prob(participation)

        if training:
            # Add noise for exploration
            effort_noise = torch.normal(0, 0.1, effort.shape).clamp(-0.2, 0.2)
            sharing_noise = torch.normal(0, 0.1, sharing.shape).clamp(-0.2, 0.2)
            effort = (effort + effort_noise).clamp(0, 1)
            sharing = (sharing + sharing_noise).clamp(0, 1)

        return {
            'participation': participation,
            'effort': effort,
            'sharing': sharing,
            'participation_log_prob': participation_log_prob,
            'value': value
        }

    def evaluate_action(self, state, actions):
        """Evaluate actions under current policy."""
        participation_probs, effort, sharing, value = self.forward(state)

        # Participation log probability
        participation_dist = Categorical(participation_probs)
        participation_log_prob = participation_dist.log_prob(actions['participation'])
        participation_entropy = participation_dist.entropy()

        # For continuous actions, we use the predicted values
        # In practice, you might want to model these as distributions too

        return {
            'participation_log_prob': participation_log_prob,
            'participation_entropy': participation_entropy,
            'value': value
        }


class MARLCoordinator:
    """Coordinator for multi-agent federated learning."""

    def __init__(self, n_agents: int, state_dim: int, action_dim: int,
                 lr: float = 3e-4, gamma: float = 0.99,
                 device: torch.device = None):
        """
        Initialize MARL coordinator.

        Args:
            n_agents: Number of client agents
            state_dim: State dimension for each agent
            action_dim: Action dimension for each agent
            lr: Learning rate
            gamma: Discount factor
            device: Device to use
        """
        self.n_agents = n_agents
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.device = device if device is not None else torch.device('cpu')

        # Create agents
        self.agents = []
        self.optimizers = []

        for i in range(n_agents):
            agent = ClientAgent(state_dim, action_dim, i).to(self.device)
            optimizer = optim.Adam(agent.parameters(), lr=lr)

            self.agents.append(agent)
            self.optimizers.append(optimizer)

        # Global state tracking
        self.global_state_history = []
        self.agent_rewards = [[] for _ in range(n_agents)]

    def get_global_state(self, local_states: List[np.ndarray],
                        round_num: int, total_rounds: int):
        """Compute global state from local states."""
        # Aggregate local information
        avg_local_state = np.mean(local_states, axis=0)

        # Add global information
        global_features = np.array([
            round_num / total_rounds,  # Progress
            len(local_states),         # Number of active agents
            np.std([np.mean(state) for state in local_states]),  # Diversity
        ])

        global_state = np.concatenate([avg_local_state, global_features])

        # Pad or truncate to match state_dim
        if len(global_state) > self.state_dim:
            global_state = global_state[:self.state_dim]
        elif len(global_state) < self.state_dim:
            padding = np.zeros(self.state_dim - len(global_state))
            global_state = np.concatenate([global_state, padding])

        return global_state

    def coordinate_round(self, local_states: List[np.ndarray],
                        round_num: int, total_rounds: int):
        """Coordinate one round of federated learning."""
        # Get global state
        global_state = self.get_global_state(local_states, round_num, total_rounds)
        global_state_tensor = torch.tensor(global_state, dtype=torch.float32, device=self.device)

        # Get actions from all agents
        agent_actions = []
        agent_values = []

        for i, agent in enumerate(self.agents):
            # Each agent sees global state + its local state
            if i < len(local_states):
                combined_state = global_state_tensor  # Simplified: just use global state
                action_dict = agent.get_action(combined_state.unsqueeze(0))
                agent_actions.append(action_dict)
                agent_values.append(action_dict['value'])
            else:
                # Inactive agent
                agent_actions.append(None)
                agent_values.append(None)

        return agent_actions, agent_values, global_state

    def compute_rewards(self, agent_actions: List[Dict],
                       global_performance: float,
                       individual_performances: List[float]):
        """Compute rewards for each agent based on their actions and outcomes."""
        rewards = []

        for i, action_dict in enumerate(agent_actions):
            if action_dict is None:
                rewards.append(0.0)
                continue

            # Base reward from global performance
            base_reward = global_performance

            # Individual performance bonus
            if i < len(individual_performances):
                individual_bonus = individual_performances[i] * 0.3
            else:
                individual_bonus = 0.0

            # Participation bonus/penalty
            participation = action_dict['participation'].item()
            if participation == 1:  # Participated
                participation_bonus = 0.1
            else:  # Did not participate
                participation_bonus = -0.05

            # Effort bonus (reward high effort)
            effort = action_dict['effort'].item()
            effort_bonus = effort * 0.2

            # Sharing bonus (reward data sharing)
            sharing = action_dict['sharing'].item()
            sharing_bonus = sharing * 0.1

            # Cooperation bonus (reward when many agents participate)
            active_agents = sum(1 for a in agent_actions if a is not None and a['participation'].item() == 1)
            cooperation_bonus = (active_agents / self.n_agents) * 0.1

            total_reward = (base_reward + individual_bonus + participation_bonus +
                          effort_bonus + sharing_bonus + cooperation_bonus)

            rewards.append(total_reward)

        return rewards

    def update_agents(self, states: List[torch.Tensor],
                     actions: List[Dict],
                     rewards: List[float],
                     next_states: List[torch.Tensor],
                     dones: List[bool]):
        """Update all agents using their experiences."""
        for i, (agent, optimizer) in enumerate(zip(self.agents, self.optimizers)):
            if actions[i] is None or states[i] is None:
                continue

            # Compute advantage
            with torch.no_grad():
                next_value = agent(next_states[i].unsqueeze(0))[3] if not dones[i] else torch.tensor(0.0)
                target = rewards[i] + self.gamma * next_value
                advantage = target - actions[i]['value']

            # Policy loss
            action_eval = agent.evaluate_action(states[i].unsqueeze(0), actions[i])
            policy_loss = -action_eval['participation_log_prob'] * advantage

            # Value loss
            value_loss = F.mse_loss(actions[i]['value'], target)

            # Entropy bonus
            entropy_loss = -action_eval['participation_entropy'] * 0.01

            # Total loss
            total_loss = policy_loss + 0.5 * value_loss + entropy_loss

            # Update
            optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(agent.parameters(), 0.5)
            optimizer.step()

            # Record reward
            self.agent_rewards[i].append(rewards[i])


class MARLFederatedLearning(BaseEstimator, RegressorMixin):
    """Multi-Agent Reinforcement Learning for Federated Learning."""

    def __init__(self, base_model, n_clients: int = 5, client_data: List = None,
                 n_rounds: int = 20, local_epochs: int = 1,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize MARL federated learning.

        Args:
            base_model: Base model for clients
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of federated rounds
            local_epochs: Local training epochs per round
            device: Device to use
            random_state: Random seed
        """
        self.base_model = base_model
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state

        # MARL coordinator
        self.state_dim = 10  # State features for each agent
        self.action_dim = 3   # Participation, effort, sharing

        self.coordinator = MARLCoordinator(
            n_agents=n_clients,
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            device=self.device
        )

        # Client models
        self.client_models = [copy.deepcopy(base_model) for _ in range(n_clients)]
        self.global_model = copy.deepcopy(base_model)

        # Training history
        self.history = {
            'round_rewards': [],
            'agent_actions': [],
            'global_performance': []
        }

        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)

    def _get_local_state(self, client_idx: int, round_num: int):
        """Get local state for a client."""
        if self.client_data is None or client_idx >= len(self.client_data):
            return np.zeros(self.state_dim - 3)  # Reserve 3 for global features

        X_client, y_client = self.client_data[client_idx]

        # Compute local statistics
        local_features = np.array([
            len(X_client),  # Data size
            X_client.shape[1] if len(X_client) > 0 else 0,  # Feature count
            np.mean(X_client.values if hasattr(X_client, 'values') else X_client) if len(X_client) > 0 else 0,
            np.std(X_client.values if hasattr(X_client, 'values') else X_client) if len(X_client) > 0 else 0,
            np.mean(y_client.values if hasattr(y_client, 'values') else y_client) if len(y_client) > 0 else 0,
            np.std(y_client.values if hasattr(y_client, 'values') else y_client) if len(y_client) > 0 else 0,
            client_idx / self.n_clients,  # Client ID normalized
        ])

        # Pad or truncate to fit state dimension (minus global features)
        target_size = self.state_dim - 3
        if len(local_features) > target_size:
            local_features = local_features[:target_size]
        elif len(local_features) < target_size:
            padding = np.zeros(target_size - len(local_features))
            local_features = np.concatenate([local_features, padding])

        return local_features

    def fit(self, X_val: np.ndarray, y_val: np.ndarray):
        """Train the MARL federated learning system."""
        logger.info(f"Starting MARL federated learning for {self.n_rounds} rounds")

        for round_num in range(self.n_rounds):
            round_start_time = time.time()

            # Get local states for all clients
            local_states = []
            for client_idx in range(self.n_clients):
                local_state = self._get_local_state(client_idx, round_num)
                local_states.append(local_state)

            # Coordinate round
            agent_actions, agent_values, global_state = self.coordinator.coordinate_round(
                local_states, round_num, self.n_rounds
            )

            # Execute federated learning round based on agent decisions
            participating_clients = []
            individual_performances = []

            for client_idx, action_dict in enumerate(agent_actions):
                if action_dict is None:
                    individual_performances.append(0.0)
                    continue

                # Check if client participates
                if action_dict['participation'].item() == 1:
                    participating_clients.append(client_idx)

                    # Train client model with effort-based epochs
                    effort = action_dict['effort'].item()
                    actual_epochs = max(1, int(self.local_epochs * effort))

                    try:
                        if self.client_data and client_idx < len(self.client_data):
                            X_client, y_client = self.client_data[client_idx]

                            # Apply data sharing ratio
                            sharing_ratio = action_dict['sharing'].item()
                            n_samples = int(len(X_client) * sharing_ratio)
                            if n_samples > 0:
                                X_shared = X_client.iloc[:n_samples] if hasattr(X_client, 'iloc') else X_client[:n_samples]
                                y_shared = y_client.iloc[:n_samples] if hasattr(y_client, 'iloc') else y_client[:n_samples]

                                # Train model
                                if hasattr(self.client_models[client_idx], 'fit'):
                                    if 'epochs' in self.client_models[client_idx].__dict__:
                                        self.client_models[client_idx].epochs = actual_epochs
                                    self.client_models[client_idx].fit(X_shared, y_shared)

                                # Evaluate performance
                                pred = self.client_models[client_idx].predict(X_val)
                                performance = -np.mean(np.abs(y_val - pred))
                                individual_performances.append(performance)
                            else:
                                individual_performances.append(0.0)
                        else:
                            individual_performances.append(0.0)
                    except Exception as e:
                        logger.warning(f"Error training client {client_idx}: {e}")
                        individual_performances.append(0.0)
                else:
                    individual_performances.append(0.0)

            # Aggregate models (simplified averaging)
            if participating_clients:
                # For neural networks, aggregate parameters
                if hasattr(self.global_model, 'model') and hasattr(self.global_model.model, 'state_dict'):
                    global_state_dict = {}

                    # Initialize with zeros
                    for key in self.client_models[participating_clients[0]].model.state_dict().keys():
                        global_state_dict[key] = torch.zeros_like(
                            self.client_models[participating_clients[0]].model.state_dict()[key]
                        )

                    # Average client parameters
                    for client_idx in participating_clients:
                        client_state_dict = self.client_models[client_idx].model.state_dict()
                        for key in global_state_dict.keys():
                            global_state_dict[key] += client_state_dict[key] / len(participating_clients)

                    # Update global model
                    self.global_model.model.load_state_dict(global_state_dict)

                # Evaluate global model
                try:
                    global_pred = self.global_model.predict(X_val)
                    global_performance = -np.mean(np.abs(y_val - global_pred))
                except:
                    global_performance = np.mean(individual_performances) if individual_performances else 0.0
            else:
                global_performance = -1.0  # Penalty for no participation

            # Compute rewards
            rewards = self.coordinator.compute_rewards(
                agent_actions, global_performance, individual_performances
            )

            # Update agents
            states = [torch.tensor(local_states[i], dtype=torch.float32, device=self.device)
                     for i in range(self.n_clients)]
            next_states = states  # Simplified: same state for next step
            dones = [False] * self.n_clients

            self.coordinator.update_agents(states, agent_actions, rewards, next_states, dones)

            # Record history
            self.history['round_rewards'].append(rewards)
            self.history['agent_actions'].append(agent_actions)
            self.history['global_performance'].append(global_performance)

            round_time = time.time() - round_start_time

            if (round_num + 1) % 5 == 0:
                avg_reward = np.mean([r for r in rewards if r != 0])
                logger.info(f"Round {round_num + 1}/{self.n_rounds}, "
                           f"Global Performance: {global_performance:.6f}, "
                           f"Avg Agent Reward: {avg_reward:.6f}, "
                           f"Participating Clients: {len(participating_clients)}, "
                           f"Time: {round_time:.2f}s")

        logger.info("MARL federated learning completed")
        return self

    def predict(self, X):
        """Make predictions using the global model."""
        return self.global_model.predict(X)

    @property
    def feature_importances_(self):
        """Get feature importances from global model."""
        if hasattr(self.global_model, 'feature_importances_'):
            return self.global_model.feature_importances_
        else:
            return np.ones(10) / 10  # Placeholder
