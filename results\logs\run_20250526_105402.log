2025-05-26 10:54:08,501 - utils.gpu_utils - INFO - Using GPU: NVIDIA GeForce GTX 1050
2025-05-26 10:54:08,502 - utils.gpu_utils - INFO - GPU Memory: 4.3 GB
2025-05-26 10:54:08,502 - utils.gpu_utils - INFO - Applying Windows-specific GPU optimizations
2025-05-26 10:54:08,502 - utils.gpu_utils - INFO - Enabled Flash SDP for Windows
2025-05-26 10:54:08,503 - utils.gpu_utils - INFO - Mixed precision training enabled
2025-05-26 10:54:08,503 - utils.gpu_utils - INFO - CUDNN benchmark mode enabled
2025-05-26 10:54:08,503 - utils.gpu_utils - INFO - TF32 enabled for Ampere GPUs
2025-05-26 10:54:08,503 - __main__ - INFO - Using GPU 0: cuda:0
2025-05-26 10:54:08,504 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.00GB, Free: 4.29GB, Utilization: 0.0%
2025-05-26 10:54:08,505 - __main__ - INFO - Loading data...
2025-05-26 10:54:09,928 - utils.data_utils - INFO - Loaded primary data from logS_des.csv: 14594 samples, 199 features
2025-05-26 10:54:09,930 - __main__ - INFO - Splitting data...
2025-05-26 10:54:10,031 - utils.data_utils - INFO - Data split: train=9340, val=2335, test=2919
2025-05-26 10:54:10,033 - __main__ - INFO - Preprocessing data on GPU...
2025-05-26 10:54:10,033 - utils.data_utils - INFO - Preprocessing data on cuda:0
2025-05-26 10:54:10,427 - utils.data_utils - INFO - Data standardized on GPU
2025-05-26 10:54:10,438 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.04GB, Free: 4.25GB, Utilization: 1.0%
2025-05-26 10:54:10,440 - __main__ - INFO - Creating client data for 3 clients...
2025-05-26 10:54:10,504 - utils.data_utils - INFO - Client 1: 3114 samples, mean(y)=-2.8155, std(y)=2.1633
2025-05-26 10:54:10,507 - utils.data_utils - INFO - Client 2: 3113 samples, mean(y)=-2.8533, std(y)=2.2124
2025-05-26 10:54:10,510 - utils.data_utils - INFO - Client 3: 3113 samples, mean(y)=-2.9316, std(y)=2.2317
2025-05-26 10:54:13,750 - utils.visualization - INFO - Plot saved to results/plots\client_data_distribution.png
2025-05-26 10:54:13,752 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 10:54:13,769 - strategies.federated.fedavg - INFO - Starting FedAvg training with 3 clients and 10 rounds
2025-05-26 10:54:13,789 - strategies.federated.fedavg - INFO - Round 1/10
2025-05-26 10:54:13,790 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 10:54:29,401 - utils.neural_models - INFO - Epoch 10/100, Train Loss: nan
2025-05-26 10:54:31,864 - utils.neural_models - INFO - Epoch 20/100, Train Loss: nan
2025-05-26 10:54:34,317 - utils.neural_models - INFO - Epoch 30/100, Train Loss: nan
2025-05-26 10:54:36,752 - utils.neural_models - INFO - Epoch 40/100, Train Loss: nan
2025-05-26 10:54:39,152 - utils.neural_models - INFO - Epoch 50/100, Train Loss: nan
2025-05-26 10:54:41,760 - utils.neural_models - INFO - Epoch 60/100, Train Loss: nan
2025-05-26 10:54:44,176 - utils.neural_models - INFO - Epoch 70/100, Train Loss: nan
2025-05-26 10:54:46,594 - utils.neural_models - INFO - Epoch 80/100, Train Loss: nan
2025-05-26 10:54:49,178 - utils.neural_models - INFO - Epoch 90/100, Train Loss: nan
2025-05-26 10:54:51,840 - utils.neural_models - INFO - Epoch 100/100, Train Loss: nan
2025-05-26 10:54:51,841 - utils.neural_models - INFO - Neural network training completed in 38.05s
2025-05-26 10:54:51,872 - utils.neural_models - WARNING - NaN values detected in predictions, replacing with zeros
2025-05-26 10:54:52,055 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 10:54:54,840 - utils.neural_models - INFO - Epoch 10/100, Train Loss: nan
2025-05-26 10:54:57,447 - utils.neural_models - INFO - Epoch 20/100, Train Loss: nan
2025-05-26 10:55:00,316 - utils.neural_models - INFO - Epoch 30/100, Train Loss: nan
2025-05-26 10:55:03,202 - utils.neural_models - INFO - Epoch 40/100, Train Loss: nan
2025-05-26 10:55:05,849 - utils.neural_models - INFO - Epoch 50/100, Train Loss: nan
2025-05-26 10:55:08,720 - utils.neural_models - INFO - Epoch 60/100, Train Loss: nan
2025-05-26 10:55:11,491 - utils.neural_models - INFO - Epoch 70/100, Train Loss: nan
2025-05-26 10:55:14,248 - utils.neural_models - INFO - Epoch 80/100, Train Loss: nan
2025-05-26 10:55:16,869 - utils.neural_models - INFO - Epoch 90/100, Train Loss: nan
2025-05-26 10:55:19,661 - utils.neural_models - INFO - Epoch 100/100, Train Loss: nan
2025-05-26 10:55:19,661 - utils.neural_models - INFO - Neural network training completed in 27.61s
2025-05-26 10:55:19,665 - utils.neural_models - WARNING - NaN values detected in predictions, replacing with zeros
2025-05-26 10:55:19,666 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 10:55:22,370 - utils.neural_models - INFO - Epoch 10/100, Train Loss: nan
2025-05-26 10:55:24,883 - utils.neural_models - INFO - Epoch 20/100, Train Loss: nan
2025-05-26 10:55:27,393 - utils.neural_models - INFO - Epoch 30/100, Train Loss: nan
2025-05-26 10:55:29,983 - utils.neural_models - INFO - Epoch 40/100, Train Loss: nan
2025-05-26 10:55:32,827 - utils.neural_models - INFO - Epoch 50/100, Train Loss: nan
2025-05-26 10:55:35,665 - utils.neural_models - INFO - Epoch 60/100, Train Loss: nan
