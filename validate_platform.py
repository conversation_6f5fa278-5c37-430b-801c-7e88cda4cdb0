#!/usr/bin/env python3
"""
Quick Platform Validation Script
================================

This script performs a quick validation of the FLKDDrug platform to ensure
all components are working correctly after the comprehensive review.
"""

import sys
import os
import traceback
import time

def test_imports():
    """Test all critical imports."""
    print("Testing imports...")
    
    try:
        # Basic imports
        import pandas as pd
        import numpy as np
        import sklearn
        print("✓ Basic scientific libraries imported")
        
        # Project imports
        import config
        print("✓ Config imported")
        
        from utils.data_utils import load_data
        print("✓ Data utils imported")
        
        from utils.gpu_utils import GPUManager
        print("✓ GPU utils imported")
        
        # Strategy imports
        from strategies.federated.fedavg import FedAvg
        print("✓ FedAvg strategy imported")
        
        from strategies.distillation.vanilla_kd import VanillaKD
        print("✓ Vanilla KD strategy imported")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        traceback.print_exc()
        return False

def test_data_loading():
    """Test data loading functionality."""
    print("\nTesting data loading...")
    
    try:
        from utils.data_utils import load_data
        
        # Check if data file exists
        if not os.path.exists('logS.csv'):
            print("✗ Data file 'logS.csv' not found")
            return False
        
        # Load data
        X, y = load_data()
        print(f"✓ Data loaded successfully: X={X.shape}, y={y.shape}")
        
        # Basic validation
        if X.shape[0] == y.shape[0]:
            print("✓ Data shapes are consistent")
        else:
            print("✗ Data shapes are inconsistent")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        traceback.print_exc()
        return False

def test_reference_files():
    """Test that all reference files exist and are valid."""
    print("\nTesting reference files...")
    
    reference_files = [
        'original-model_save.py',
        'original-model_CVtest.py',
        'model_save_fl_kd.py',
        'model_CVtest_fl_kd.py'
    ]
    
    all_exist = True
    for file in reference_files:
        if os.path.exists(file):
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
            all_exist = False
    
    return all_exist

def test_strategy_structure():
    """Test strategy module structure."""
    print("\nTesting strategy structure...")
    
    try:
        # Test strategy directories
        strategy_dirs = ['federated', 'distillation', 'contrastive', 'transfer']
        
        for strategy_dir in strategy_dirs:
            path = f'strategies/{strategy_dir}'
            if os.path.exists(path):
                print(f"✓ {strategy_dir} directory exists")
                
                # Check __init__.py
                init_file = f'{path}/__init__.py'
                if os.path.exists(init_file):
                    print(f"✓ {strategy_dir}/__init__.py exists")
                else:
                    print(f"✗ {strategy_dir}/__init__.py missing")
                    return False
            else:
                print(f"✗ {strategy_dir} directory missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Strategy structure test failed: {e}")
        return False

def test_gpu_availability():
    """Test GPU availability."""
    print("\nTesting GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            print(f"✓ CUDA available with {torch.cuda.device_count()} GPU(s)")
            for i in range(torch.cuda.device_count()):
                print(f"  - GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("! CUDA not available - will use CPU mode")
        
        return True
        
    except ImportError:
        print("! PyTorch not installed - GPU testing skipped")
        return True
    except Exception as e:
        print(f"✗ GPU test failed: {e}")
        return False

def test_config():
    """Test configuration."""
    print("\nTesting configuration...")
    
    try:
        import config
        
        # Check required config sections
        required_configs = ['GPU_CONFIG', 'MODEL_CONFIG', 'FL_CONFIG', 'KD_CONFIG']
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                print(f"✓ {config_name} exists")
            else:
                print(f"✗ {config_name} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def test_testing_scripts():
    """Test that testing scripts exist."""
    print("\nTesting script availability...")
    
    testing_scripts = [
        'test_system.py',
        'test_strategies.py',
        'monitor_performance.py',
        'test_all_windows.bat',
        'Test-FLKDDrug.ps1'
    ]
    
    all_exist = True
    for script in testing_scripts:
        if os.path.exists(script):
            print(f"✓ {script} exists")
        else:
            print(f"✗ {script} missing")
            all_exist = False
    
    return all_exist

def main():
    """Main validation function."""
    print("="*60)
    print(" FLKDDrug Platform Validation")
    print("="*60)
    
    start_time = time.time()
    
    tests = [
        ("Import Tests", test_imports),
        ("Data Loading", test_data_loading),
        ("Reference Files", test_reference_files),
        ("Strategy Structure", test_strategy_structure),
        ("GPU Availability", test_gpu_availability),
        ("Configuration", test_config),
        ("Testing Scripts", test_testing_scripts)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    # Final report
    print("\n" + "="*60)
    print(" VALIDATION SUMMARY")
    print("="*60)
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    print(f"Total Time: {time.time() - start_time:.1f} seconds")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! Platform is ready for use.")
        print("\nNext steps:")
        print("1. Run: python original-model_save.py")
        print("2. Run: python test_system.py")
        print("3. Run: python test_strategies.py --timeout 300")
        return True
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
