#!/usr/bin/env python3
"""
Comprehensive Strategy Testing Script for FLKDDrug Platform
Tests all strategies with minimal parameters to ensure functionality.
"""

import os
import sys
import time
import logging
import subprocess
import json
from datetime import datetime
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'strategy_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StrategyTester:
    """Comprehensive strategy testing class."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        
        # Define all strategies to test
        self.strategies = {
            'baseline': [
                {'name': 'original', 'cmd': ['--original', '--epochs', '3']}
            ],
            'federated_learning': [
                {'name': 'fedavg', 'cmd': ['--fl_strategy', 'fedavg', '--n_rounds', '3', '--n_clients', '2']},
                {'name': 'fedprox', 'cmd': ['--fl_strategy', 'fedprox', '--n_rounds', '3', '--n_clients', '2']},
                {'name': 'scaffold', 'cmd': ['--fl_strategy', 'scaffold', '--n_rounds', '3', '--n_clients', '2']},
                {'name': 'personalized_fl', 'cmd': ['--fl_strategy', 'personalized_fl', '--n_rounds', '3', '--n_clients', '2']},
            ],
            'knowledge_distillation': [
                {'name': 'vanilla_kd', 'cmd': ['--kd_strategy', 'vanilla_kd', '--epochs', '3']},
                {'name': 'ensemble_kd', 'cmd': ['--kd_strategy', 'ensemble_kd', '--epochs', '3']},
                {'name': 'progressive_kd', 'cmd': ['--kd_strategy', 'progressive_kd', '--epochs', '3']},
                {'name': 'attention_kd', 'cmd': ['--kd_strategy', 'attention_kd', '--epochs', '3']},
            ],
            'combined_strategies': [
                {'name': 'fedavg_vanilla_kd', 'cmd': ['--fl_strategy', 'fedavg', '--kd_strategy', 'vanilla_kd', '--n_rounds', '2', '--epochs', '2']},
                {'name': 'fedprox_ensemble_kd', 'cmd': ['--fl_strategy', 'fedprox', '--kd_strategy', 'ensemble_kd', '--n_rounds', '2', '--epochs', '2']},
            ]
        }
    
    def test_strategy(self, strategy_name, cmd_args, timeout=600):
        """Test a single strategy."""
        logger.info(f"Testing strategy: {strategy_name}")
        
        # Build command
        cmd = [sys.executable, 'run_strategy.py'] + cmd_args + ['--gpu', '0', '--seed', '42']
        
        start_time = time.time()
        try:
            # Run the strategy
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                cwd=os.getcwd()
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                logger.info(f"✓ {strategy_name} completed successfully in {execution_time:.1f}s")
                
                # Try to extract metrics from output
                metrics = self._extract_metrics(result.stdout)
                
                return {
                    'status': 'success',
                    'execution_time': execution_time,
                    'metrics': metrics,
                    'stdout': result.stdout[-1000:],  # Last 1000 chars
                    'stderr': result.stderr
                }
            else:
                logger.error(f"✗ {strategy_name} failed with return code {result.returncode}")
                logger.error(f"Error output: {result.stderr}")
                
                return {
                    'status': 'failed',
                    'execution_time': execution_time,
                    'return_code': result.returncode,
                    'stdout': result.stdout[-1000:],
                    'stderr': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error(f"✗ {strategy_name} timed out after {timeout}s")
            return {
                'status': 'timeout',
                'execution_time': timeout,
                'error': f'Timeout after {timeout}s'
            }
        except Exception as e:
            logger.error(f"✗ {strategy_name} failed with exception: {e}")
            return {
                'status': 'error',
                'execution_time': time.time() - start_time,
                'error': str(e)
            }
    
    def _extract_metrics(self, output):
        """Extract performance metrics from output."""
        metrics = {}
        
        # Look for common metric patterns
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            
            # Extract test metrics
            if 'test_mae:' in line.lower():
                try:
                    metrics['test_mae'] = float(line.split(':')[-1].strip())
                except:
                    pass
            elif 'test_mse:' in line.lower():
                try:
                    metrics['test_mse'] = float(line.split(':')[-1].strip())
                except:
                    pass
            elif 'test_r2:' in line.lower():
                try:
                    metrics['test_r2'] = float(line.split(':')[-1].strip())
                except:
                    pass
        
        return metrics
    
    def test_category(self, category_name, strategies):
        """Test all strategies in a category."""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing {category_name.upper()} Strategies")
        logger.info(f"{'='*60}")
        
        category_results = {}
        
        for strategy in strategies:
            result = self.test_strategy(strategy['name'], strategy['cmd'])
            category_results[strategy['name']] = result
            
            # Brief pause between tests
            time.sleep(2)
        
        return category_results
    
    def run_all_tests(self):
        """Run all strategy tests."""
        logger.info("Starting Comprehensive Strategy Testing...")
        logger.info(f"Total strategies to test: {sum(len(strategies) for strategies in self.strategies.values())}")
        
        all_results = {}
        
        for category, strategies in self.strategies.items():
            category_results = self.test_category(category, strategies)
            all_results[category] = category_results
        
        # Generate summary report
        self._generate_summary_report(all_results)
        
        return all_results
    
    def _generate_summary_report(self, results):
        """Generate a summary report of all tests."""
        logger.info("\n" + "="*60)
        logger.info("STRATEGY TESTING SUMMARY REPORT")
        logger.info("="*60)
        
        total_strategies = 0
        successful_strategies = 0
        failed_strategies = 0
        timeout_strategies = 0
        
        summary_data = []
        
        for category, category_results in results.items():
            logger.info(f"\n{category.upper()}:")
            
            for strategy_name, result in category_results.items():
                total_strategies += 1
                status = result['status']
                exec_time = result.get('execution_time', 0)
                
                if status == 'success':
                    successful_strategies += 1
                    status_symbol = "✓"
                elif status == 'timeout':
                    timeout_strategies += 1
                    status_symbol = "⏱"
                else:
                    failed_strategies += 1
                    status_symbol = "✗"
                
                logger.info(f"  {status_symbol} {strategy_name:<20} - {status:<8} ({exec_time:.1f}s)")
                
                # Collect data for CSV report
                summary_data.append({
                    'category': category,
                    'strategy': strategy_name,
                    'status': status,
                    'execution_time': exec_time,
                    'test_mae': result.get('metrics', {}).get('test_mae', ''),
                    'test_mse': result.get('metrics', {}).get('test_mse', ''),
                    'test_r2': result.get('metrics', {}).get('test_r2', ''),
                })
        
        # Overall statistics
        logger.info(f"\nOVERALL STATISTICS:")
        logger.info(f"Total Strategies: {total_strategies}")
        logger.info(f"Successful: {successful_strategies}")
        logger.info(f"Failed: {failed_strategies}")
        logger.info(f"Timeout: {timeout_strategies}")
        logger.info(f"Success Rate: {successful_strategies/total_strategies*100:.1f}%")
        logger.info(f"Total Time: {time.time() - self.start_time:.1f} seconds")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save JSON report
        json_filename = f"strategy_test_results_{timestamp}.json"
        with open(json_filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        logger.info(f"Detailed results saved to {json_filename}")
        
        # Save CSV summary
        csv_filename = f"strategy_test_summary_{timestamp}.csv"
        df = pd.DataFrame(summary_data)
        df.to_csv(csv_filename, index=False)
        logger.info(f"Summary report saved to {csv_filename}")
        
        # Final verdict
        if successful_strategies == total_strategies:
            logger.info("🎉 ALL STRATEGIES PASSED! Platform is fully functional.")
        elif successful_strategies > total_strategies * 0.8:
            logger.info("✅ Most strategies passed. Platform is largely functional.")
        else:
            logger.warning("⚠️ Many strategies failed. Check individual logs for details.")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test all FLKDDrug strategies')
    parser.add_argument('--category', type=str, choices=['baseline', 'federated_learning', 'knowledge_distillation', 'combined_strategies'],
                       help='Test only specific category')
    parser.add_argument('--timeout', type=int, default=600, help='Timeout per strategy in seconds')
    
    args = parser.parse_args()
    
    tester = StrategyTester()
    
    if args.category:
        # Test only specific category
        if args.category in tester.strategies:
            results = {args.category: tester.test_category(args.category, tester.strategies[args.category])}
            tester._generate_summary_report(results)
        else:
            logger.error(f"Unknown category: {args.category}")
            sys.exit(1)
    else:
        # Test all strategies
        tester.run_all_tests()

if __name__ == "__main__":
    main()
