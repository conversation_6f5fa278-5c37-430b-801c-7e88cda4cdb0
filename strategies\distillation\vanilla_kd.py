"""
Implementation of Vanilla Knowledge Distillation.

Reference:
<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, J. (2015).
Distilling the knowledge in a neural network.
arXiv preprint arXiv:1503.02531.
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time

logger = logging.getLogger(__name__)

class VanillaKD:
    """
    Vanilla Knowledge Distillation for regression models.
    
    This implementation distills knowledge from a teacher model (or ensemble)
    to a student model by training the student on soft targets from the teacher.
    """
    
    def __init__(self, teacher_model=None, student_model=None, 
                 alpha=0.5, temperature=1.0, random_state=42):
        """
        Initialize VanillaKD.
        
        Args:
            teacher_model: Teacher model or ensemble
            student_model: Student model (default: smaller LGBMRegressor)
            alpha: Weight of soft targets (0-1)
            temperature: Temperature for softening targets
            random_state: Random seed
        """
        self.teacher_model = teacher_model
        
        if student_model is None:
            # Default: smaller LightGBM model
            self.student_model = LGBMRegressor(
                n_estimators=100,
                num_leaves=31,
                learning_rate=0.05,
                random_state=random_state
            )
        else:
            self.student_model = student_model
            
        self.alpha = alpha
        self.temperature = temperature
        self.random_state = random_state
        self.history = {
            'train_loss': [],
            'val_loss': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_train, y_train, X_val=None, y_val=None, epochs=1):
        """
        Train the student model with knowledge distillation.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            epochs: Number of training epochs
            
        Returns:
            self: Trained model
        """
        if self.teacher_model is None:
            raise ValueError("Teacher model is not provided")
        
        start_time = time.time()
        logger.info(f"Starting Vanilla KD training with alpha={self.alpha}, temperature={self.temperature}")
        
        # Get soft targets from teacher
        soft_targets = self.teacher_model.predict(X_train)
        
        # For regression, we don't apply softmax with temperature
        # Instead, we use the temperature to scale the targets
        # This is similar to adding noise to the targets
        if self.temperature != 1.0:
            # Add noise proportional to temperature
            noise = np.random.normal(0, self.temperature - 1.0, soft_targets.shape)
            soft_targets = soft_targets + noise
        
        # Training loop
        for epoch in range(epochs):
            epoch_start = time.time()
            
            # Combine hard and soft targets using alpha
            if self.alpha < 1.0:
                # Use sample weights to implement the alpha weighting
                # Higher weight where soft and hard targets differ more
                target_diff = np.abs(y_train - soft_targets)
                sample_weight = 1.0 + self.alpha * target_diff
                
                # Train student on hard targets with weighted samples
                self.student_model.fit(X_train, y_train, sample_weight=sample_weight)
            else:
                # Train student directly on soft targets
                self.student_model.fit(X_train, soft_targets)
            
            # Evaluate
            if X_val is not None and y_val is not None:
                y_pred = self.student_model.predict(X_val)
                val_loss = np.mean(np.abs(y_val - y_pred))
                self.history['val_loss'].append(val_loss)
                
                # Also evaluate on training set
                y_pred_train = self.student_model.predict(X_train)
                train_loss = np.mean(np.abs(y_train - y_pred_train))
                self.history['train_loss'].append(train_loss)
                
                epoch_time = time.time() - epoch_start
                logger.info(f"Epoch {epoch+1}/{epochs} completed in {epoch_time:.2f}s. "
                           f"Train loss: {train_loss:.4f}, Val loss: {val_loss:.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"Vanilla KD training completed in {total_time:.2f}s")
        
        return self
    
    def predict(self, X):
        """
        Make predictions using the student model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        return self.student_model.predict(X)
    
    def __getattr__(self, name):
        """
        Forward attribute access to the student model.
        
        Args:
            name: Attribute name
            
        Returns:
            Attribute value
        """
        return getattr(self.student_model, name)
