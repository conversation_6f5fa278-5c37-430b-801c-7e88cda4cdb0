2025-05-26 22:09:41,199 - utils.gpu_utils - INFO - Using GPU: NVIDIA GeForce GTX 1050
2025-05-26 22:09:41,199 - utils.gpu_utils - INFO - GPU Memory: 4.3 GB
2025-05-26 22:09:41,199 - utils.gpu_utils - INFO - Applying Windows-specific GPU optimizations
2025-05-26 22:09:41,200 - utils.gpu_utils - INFO - Enabled Flash SDP for Windows
2025-05-26 22:09:41,200 - utils.gpu_utils - INFO - Mixed precision training enabled
2025-05-26 22:09:41,200 - utils.gpu_utils - INFO - CUDNN benchmark mode enabled
2025-05-26 22:09:41,207 - utils.gpu_utils - INFO - TF32 enabled for Ampere GPUs
2025-05-26 22:09:41,208 - __main__ - INFO - Using GPU 0: cuda:0
2025-05-26 22:09:41,209 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.00GB, Free: 4.29GB, Utilization: 0.0%
2025-05-26 22:09:41,326 - __main__ - INFO - Loading data...
2025-05-26 22:09:44,854 - utils.data_utils - INFO - Loaded primary data from logS_des.csv: 14594 samples, 199 features
2025-05-26 22:09:44,857 - __main__ - INFO - Splitting data...
2025-05-26 22:09:44,952 - utils.data_utils - INFO - Data split: train=9340, val=2335, test=2919
2025-05-26 22:09:44,956 - __main__ - INFO - Preprocessing data...
2025-05-26 22:09:45,042 - utils.data_utils - INFO - Found 62 NaN values in training data
2025-05-26 22:09:45,238 - utils.data_utils - INFO - NaN values filled with median values
2025-05-26 22:09:45,473 - utils.data_utils - INFO - Data scaled using RobustScaler
2025-05-26 22:09:45,476 - __main__ - INFO - Creating client data for 2 clients...
2025-05-26 22:09:45,560 - utils.data_utils - INFO - Client 1: 4670 samples, mean(y)=-2.8065, std(y)=2.1641
2025-05-26 22:09:45,561 - utils.data_utils - INFO - Client 2: 4670 samples, mean(y)=-2.9271, std(y)=2.2397
2025-05-26 22:10:08,243 - utils.visualization - INFO - Plot saved to results/plots\client_data_distribution.png
2025-05-26 22:10:08,248 - __main__ - INFO - Running original LightGBM model with Bayesian optimization (replicating original-model_save.py)...
