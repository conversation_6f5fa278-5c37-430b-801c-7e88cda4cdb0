"""
Domain Adaptation for Drug Solubility Prediction.

Implements domain adaptation techniques to transfer knowledge from
related molecular property prediction tasks to solubility prediction.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.metrics import mean_squared_error, r2_score
import logging
import time

logger = logging.getLogger(__name__)


class GradientReversalLayer(torch.autograd.Function):
    """Gradient Reversal Layer for domain adaptation."""
    
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.alpha = alpha
        return x.view_as(x)
    
    @staticmethod
    def backward(ctx, grad_output):
        output = grad_output.neg() * ctx.alpha
        return output, None


class DomainAdaptationNetwork(nn.Module):
    """Neural network with domain adaptation capabilities."""
    
    def __init__(self, input_dim, hidden_dims=[256, 128], num_domains=2):
        super().__init__()
        
        # Feature extractor (shared between domains)
        layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Task predictor (solubility prediction)
        self.task_predictor = nn.Sequential(
            nn.Linear(prev_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 1)
        )
        
        # Domain classifier
        self.domain_classifier = nn.Sequential(
            nn.Linear(prev_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, num_domains)
        )
    
    def forward(self, x, alpha=1.0, return_domain_logits=False):
        features = self.feature_extractor(x)
        
        # Task prediction
        task_output = self.task_predictor(features)
        
        if return_domain_logits:
            # Domain classification with gradient reversal
            reversed_features = GradientReversalLayer.apply(features, alpha)
            domain_logits = self.domain_classifier(reversed_features)
            return task_output, domain_logits
        
        return task_output


class DomainAdaptation(BaseEstimator, RegressorMixin):
    """
    Domain Adaptation for molecular property prediction.
    
    Learns domain-invariant features that work well across different
    molecular datasets while maintaining task-specific performance.
    """
    
    def __init__(self, hidden_dims=[256, 128], learning_rate=0.001, 
                 batch_size=64, epochs=100, domain_weight=0.1,
                 adaptation_schedule='linear', device=None):
        self.hidden_dims = hidden_dims
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs
        self.domain_weight = domain_weight
        self.adaptation_schedule = adaptation_schedule
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model = None
        self.history = {'task_loss': [], 'domain_loss': [], 'total_loss': []}
    
    def _get_adaptation_alpha(self, epoch):
        """Get adaptation strength based on schedule."""
        if self.adaptation_schedule == 'linear':
            return 2.0 / (1.0 + np.exp(-10 * epoch / self.epochs)) - 1.0
        elif self.adaptation_schedule == 'constant':
            return 1.0
        else:
            return min(1.0, epoch / (self.epochs * 0.5))
    
    def fit(self, X, y, X_source=None, y_source=None):
        """
        Fit domain adaptation model.
        
        Args:
            X, y: Target domain data (solubility)
            X_source, y_source: Source domain data (optional, for simulation)
        """
        start_time = time.time()
        logger.info(f"Training Domain Adaptation on {self.device}")
        
        # Convert to tensors
        if isinstance(X, pd.DataFrame):
            X = X.values
        if isinstance(y, pd.Series):
            y = y.values
            
        X = torch.FloatTensor(X).to(self.device)
        y = torch.FloatTensor(y).to(self.device)
        
        # If no source data provided, create synthetic source domain
        if X_source is None:
            X_source, y_source = self._create_synthetic_source(X, y)
        else:
            if isinstance(X_source, pd.DataFrame):
                X_source = X_source.values
            if isinstance(y_source, pd.Series):
                y_source = y_source.values
            X_source = torch.FloatTensor(X_source).to(self.device)
            y_source = torch.FloatTensor(y_source).to(self.device)
        
        # Initialize model
        input_dim = X.shape[1]
        self.model = DomainAdaptationNetwork(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            num_domains=2
        ).to(self.device)
        
        # Optimizers
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        # Create combined dataset
        X_combined = torch.cat([X, X_source], dim=0)
        y_combined = torch.cat([y, y_source], dim=0)
        domain_labels = torch.cat([
            torch.zeros(X.shape[0]),  # Target domain = 0
            torch.ones(X_source.shape[0])  # Source domain = 1
        ]).long().to(self.device)
        
        dataset = torch.utils.data.TensorDataset(X_combined, y_combined, domain_labels)
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True
        )
        
        # Training loop
        self.model.train()
        for epoch in range(self.epochs):
            total_task_loss = 0.0
            total_domain_loss = 0.0
            total_loss = 0.0
            
            alpha = self._get_adaptation_alpha(epoch)
            
            for batch_X, batch_y, batch_domains in dataloader:
                optimizer.zero_grad()
                
                # Forward pass
                task_output, domain_logits = self.model(
                    batch_X, alpha=alpha, return_domain_logits=True
                )
                
                # Task loss (regression)
                task_loss = F.mse_loss(task_output.squeeze(), batch_y)
                
                # Domain loss (classification)
                domain_loss = F.cross_entropy(domain_logits, batch_domains)
                
                # Combined loss
                loss = task_loss + self.domain_weight * domain_loss
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_task_loss += task_loss.item()
                total_domain_loss += domain_loss.item()
                total_loss += loss.item()
            
            # Record history
            avg_task_loss = total_task_loss / len(dataloader)
            avg_domain_loss = total_domain_loss / len(dataloader)
            avg_total_loss = total_loss / len(dataloader)
            
            self.history['task_loss'].append(avg_task_loss)
            self.history['domain_loss'].append(avg_domain_loss)
            self.history['total_loss'].append(avg_total_loss)
            
            if epoch % 20 == 0:
                logger.info(f"Epoch {epoch}/{self.epochs}, "
                          f"Task Loss: {avg_task_loss:.4f}, "
                          f"Domain Loss: {avg_domain_loss:.4f}, "
                          f"Alpha: {alpha:.3f}")
        
        total_time = time.time() - start_time
        logger.info(f"Domain Adaptation training completed in {total_time:.2f}s")
        
        return self
    
    def _create_synthetic_source(self, X_target, y_target):
        """Create synthetic source domain data for demonstration."""
        # Add noise and shift to create different domain
        noise = torch.randn_like(X_target) * 0.1
        shift = torch.randn(1, X_target.shape[1]).to(self.device) * 0.2
        
        X_source = X_target + noise + shift
        
        # Add some bias to target values
        y_source = y_target + torch.randn_like(y_target) * 0.1 + 0.2
        
        return X_source, y_source
    
    def predict(self, X):
        """Make predictions."""
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        self.model.eval()
        
        if isinstance(X, pd.DataFrame):
            X = X.values
        X = torch.FloatTensor(X).to(self.device)
        
        with torch.no_grad():
            predictions = self.model(X)
            predictions = predictions.cpu().numpy().flatten()
        
        return predictions
    
    @property
    def feature_importances_(self):
        """Compute feature importances using gradient-based method."""
        if self.model is None:
            return np.ones(199) / 199
        
        # Use gradient of first layer weights as importance
        first_layer = self.model.feature_extractor[0]
        if hasattr(first_layer, 'weight'):
            importances = torch.norm(first_layer.weight, dim=0).detach().cpu().numpy()
            return importances / np.sum(importances)
        else:
            return np.ones(199) / 199
