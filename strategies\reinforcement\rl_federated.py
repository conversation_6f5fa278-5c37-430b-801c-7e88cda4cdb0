"""
Reinforcement Learning enhanced Federated Learning strategies.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
from sklearn.base import BaseEstimator, RegressorMixin
import copy
import time

logger = logging.getLogger(__name__)


class ClientSelectionAgent(nn.Module):
    """RL agent for intelligent client selection in federated learning."""
    
    def __init__(self, state_dim: int, n_clients: int, hidden_dim: int = 128):
        super(ClientSelectionAgent, self).__init__()
        
        self.state_dim = state_dim
        self.n_clients = n_clients
        
        # Policy network for client selection
        self.policy_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, n_clients),
            nn.Sigmoid()  # Output probabilities for each client
        )
        
        # Value network for advantage estimation
        self.value_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, state):
        """Forward pass to get client selection probabilities and value."""
        client_probs = self.policy_net(state)
        value = self.value_net(state)
        return client_probs, value
    
    def select_clients(self, state, n_select: int, training: bool = True):
        """Select clients based on current policy."""
        client_probs, value = self.forward(state)
        
        if training:
            # Sample clients based on probabilities
            selected = torch.multinomial(client_probs, n_select, replacement=False)
        else:
            # Select top clients deterministically
            _, selected = torch.topk(client_probs, n_select)
        
        return selected, client_probs, value


class AdaptiveAggregationAgent(nn.Module):
    """RL agent for adaptive model aggregation weights."""
    
    def __init__(self, state_dim: int, max_clients: int, hidden_dim: int = 128):
        super(AdaptiveAggregationAgent, self).__init__()
        
        self.state_dim = state_dim
        self.max_clients = max_clients
        
        # Network to predict aggregation weights
        self.weight_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, max_clients),
            nn.Softmax(dim=-1)  # Ensure weights sum to 1
        )
        
    def forward(self, state):
        """Forward pass to get aggregation weights."""
        return self.weight_net(state)
    
    def get_weights(self, state, n_clients: int):
        """Get aggregation weights for current clients."""
        all_weights = self.forward(state)
        return all_weights[:n_clients] / all_weights[:n_clients].sum()


class RLFederatedLearning(BaseEstimator, RegressorMixin):
    """Reinforcement Learning enhanced Federated Learning."""
    
    def __init__(self, base_model, n_clients: int = 5, client_data: List = None,
                 n_rounds: int = 10, local_epochs: int = 1,
                 client_sample_ratio: float = 1.0, use_client_selection: bool = True,
                 use_adaptive_aggregation: bool = True, lr_rl: float = 1e-3,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize RL-enhanced Federated Learning.
        
        Args:
            base_model: Base model for clients
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of federated rounds
            local_epochs: Local training epochs per round
            client_sample_ratio: Ratio of clients to sample each round
            use_client_selection: Whether to use RL for client selection
            use_adaptive_aggregation: Whether to use RL for adaptive aggregation
            lr_rl: Learning rate for RL agents
            device: Device to use
            random_state: Random seed
        """
        self.base_model = base_model
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.client_sample_ratio = client_sample_ratio
        self.use_client_selection = use_client_selection
        self.use_adaptive_aggregation = use_adaptive_aggregation
        self.lr_rl = lr_rl
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        # Initialize client models
        self.client_models = [copy.deepcopy(base_model) for _ in range(n_clients)]
        self.global_model = copy.deepcopy(base_model)
        
        # RL agents
        self.state_dim = 10  # State features
        if self.use_client_selection:
            self.client_selector = ClientSelectionAgent(
                state_dim=self.state_dim,
                n_clients=n_clients
            ).to(self.device)
            self.client_selector_optimizer = optim.Adam(
                self.client_selector.parameters(), lr=lr_rl
            )
        
        if self.use_adaptive_aggregation:
            self.aggregation_agent = AdaptiveAggregationAgent(
                state_dim=self.state_dim,
                max_clients=n_clients
            ).to(self.device)
            self.aggregation_optimizer = optim.Adam(
                self.aggregation_agent.parameters(), lr=lr_rl
            )
        
        # Training history
        self.history = {
            'round_losses': [],
            'client_selections': [],
            'aggregation_weights': []
        }
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
    
    def _get_state(self, round_num: int, client_losses: List[float] = None):
        """Get state representation for RL agents."""
        if client_losses is None:
            client_losses = [0.0] * self.n_clients
        
        # Pad or truncate to fixed size
        client_losses = (client_losses + [0.0] * self.n_clients)[:self.n_clients]
        
        state = np.array([
            round_num / self.n_rounds,  # Normalized round number
            np.mean(client_losses),     # Average client loss
            np.std(client_losses),      # Client loss variance
            np.min(client_losses),      # Best client performance
            np.max(client_losses),      # Worst client performance
            len([l for l in client_losses if l > 0]) / self.n_clients,  # Active clients ratio
            np.median(client_losses),   # Median client loss
            np.percentile(client_losses, 25),  # 25th percentile
            np.percentile(client_losses, 75),  # 75th percentile
            np.sum(client_losses)       # Total loss
        ])
        
        return torch.tensor(state, dtype=torch.float32, device=self.device).unsqueeze(0)
    
    def _train_client(self, client_idx: int, X_val: np.ndarray, y_val: np.ndarray):
        """Train a single client model."""
        if self.client_data is None or client_idx >= len(self.client_data):
            return 0.0
        
        X_client, y_client = self.client_data[client_idx]
        
        # Train client model
        try:
            if hasattr(self.client_models[client_idx], 'fit'):
                if 'X_val' in self.client_models[client_idx].fit.__code__.co_varnames:
                    # Neural network with validation
                    self.client_models[client_idx].fit(
                        X_client, y_client, 
                        X_val=X_val, y_val=y_val
                    )
                else:
                    # Traditional model
                    self.client_models[client_idx].fit(X_client, y_client)
            
            # Evaluate client performance
            if hasattr(self.client_models[client_idx], 'predict'):
                y_pred = self.client_models[client_idx].predict(X_val)
                loss = np.mean((y_val - y_pred) ** 2)
                return loss
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"Error training client {client_idx}: {e}")
            return float('inf')
    
    def _aggregate_models(self, selected_clients: List[int], weights: Optional[np.ndarray] = None):
        """Aggregate client models into global model."""
        if weights is None:
            weights = np.ones(len(selected_clients)) / len(selected_clients)
        
        # For neural networks, aggregate parameters
        if hasattr(self.global_model, 'model') and hasattr(self.global_model.model, 'state_dict'):
            global_state_dict = {}
            
            # Initialize with zeros
            for key in self.client_models[selected_clients[0]].model.state_dict().keys():
                global_state_dict[key] = torch.zeros_like(
                    self.client_models[selected_clients[0]].model.state_dict()[key]
                )
            
            # Weighted average of client parameters
            for i, client_idx in enumerate(selected_clients):
                client_state_dict = self.client_models[client_idx].model.state_dict()
                for key in global_state_dict.keys():
                    global_state_dict[key] += weights[i] * client_state_dict[key]
            
            # Update global model
            self.global_model.model.load_state_dict(global_state_dict)
        
        # Copy global model to all clients
        for client_idx in range(self.n_clients):
            if hasattr(self.global_model, 'model') and hasattr(self.client_models[client_idx], 'model'):
                self.client_models[client_idx].model.load_state_dict(
                    self.global_model.model.state_dict()
                )
    
    def fit(self, X_val: np.ndarray, y_val: np.ndarray):
        """Train the RL-enhanced federated learning system."""
        logger.info(f"Starting RL-enhanced federated learning for {self.n_rounds} rounds")
        
        for round_num in range(self.n_rounds):
            round_start_time = time.time()
            
            # Get current state
            prev_losses = self.history['round_losses'][-1] if self.history['round_losses'] else [0.0] * self.n_clients
            state = self._get_state(round_num, prev_losses)
            
            # Client selection
            if self.use_client_selection:
                n_select = max(1, int(self.client_sample_ratio * self.n_clients))
                selected_clients, client_probs, value = self.client_selector.select_clients(
                    state, n_select, training=True
                )
                selected_clients = selected_clients.cpu().numpy().tolist()
            else:
                # Random client selection
                n_select = max(1, int(self.client_sample_ratio * self.n_clients))
                selected_clients = np.random.choice(
                    self.n_clients, n_select, replace=False
                ).tolist()
            
            # Train selected clients
            client_losses = []
            for client_idx in selected_clients:
                loss = self._train_client(client_idx, X_val, y_val)
                client_losses.append(loss)
            
            # Adaptive aggregation
            if self.use_adaptive_aggregation and len(selected_clients) > 1:
                agg_weights = self.aggregation_agent.get_weights(state, len(selected_clients))
                agg_weights = agg_weights.detach().cpu().numpy()
            else:
                agg_weights = None
            
            # Aggregate models
            self._aggregate_models(selected_clients, agg_weights)
            
            # Evaluate global model
            try:
                global_pred = self.global_model.predict(X_val)
                global_loss = np.mean((y_val - global_pred) ** 2)
            except:
                global_loss = np.mean(client_losses) if client_losses else 0.0
            
            # Update RL agents
            reward = -global_loss  # Negative loss as reward
            
            if self.use_client_selection:
                # Update client selection agent
                advantage = reward - value.item()
                
                # Policy gradient loss
                log_probs = torch.log(client_probs + 1e-8)
                selected_log_probs = log_probs[0, selected_clients].sum()
                policy_loss = -selected_log_probs * advantage
                
                # Value loss
                value_loss = (reward - value) ** 2
                
                # Total loss
                total_loss = policy_loss + 0.5 * value_loss
                
                self.client_selector_optimizer.zero_grad()
                total_loss.backward()
                self.client_selector_optimizer.step()
            
            if self.use_adaptive_aggregation and len(selected_clients) > 1:
                # Update aggregation agent (simplified)
                # In practice, you might want a more sophisticated reward signal
                agg_loss = torch.tensor(global_loss, device=self.device, requires_grad=True)
                
                self.aggregation_optimizer.zero_grad()
                agg_loss.backward()
                self.aggregation_optimizer.step()
            
            # Record history
            round_losses = [0.0] * self.n_clients
            for i, client_idx in enumerate(selected_clients):
                round_losses[client_idx] = client_losses[i]
            
            self.history['round_losses'].append(round_losses)
            self.history['client_selections'].append(selected_clients)
            if self.use_adaptive_aggregation and agg_weights is not None:
                self.history['aggregation_weights'].append(agg_weights.tolist())
            
            round_time = time.time() - round_start_time
            
            if (round_num + 1) % 5 == 0:
                logger.info(f"Round {round_num + 1}/{self.n_rounds}, "
                           f"Global Loss: {global_loss:.6f}, "
                           f"Selected Clients: {selected_clients}, "
                           f"Time: {round_time:.2f}s")
        
        logger.info("RL-enhanced federated learning completed")
        return self
    
    def predict(self, X):
        """Make predictions using the global model."""
        return self.global_model.predict(X)
    
    @property
    def feature_importances_(self):
        """Get feature importances from global model."""
        if hasattr(self.global_model, 'feature_importances_'):
            return self.global_model.feature_importances_
        else:
            return None
