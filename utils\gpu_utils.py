"""
GPU utilities for efficient GPU usage in the FLKDDrug project.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
import logging
import gc
from typing import Optional, Tuple, Union, List
import warnings

logger = logging.getLogger(__name__)

class GPUManager:
    """
    Manages GPU resources and provides utilities for GPU-accelerated operations.
    """
    
    def __init__(self, gpu_config: dict):
        """
        Initialize GPU manager.
        
        Args:
            gpu_config: GPU configuration dictionary
        """
        self.config = gpu_config
        self.device = None
        self.scaler = None
        self._setup_device()
        self._setup_optimizations()
    
    def _setup_device(self):
        """Setup device and check GPU availability."""
        if self.config.get('use_gpu', True) and torch.cuda.is_available():
            gpu_id = self.config.get('gpu_id', 0)
            if gpu_id < torch.cuda.device_count():
                self.device = torch.device(f'cuda:{gpu_id}')
                torch.cuda.set_device(self.device)
                
                # Set memory fraction if specified
                if 'memory_fraction' in self.config:
                    memory_fraction = self.config['memory_fraction']
                    torch.cuda.set_per_process_memory_fraction(memory_fraction, device=self.device)
                
                logger.info(f"Using GPU: {torch.cuda.get_device_name(self.device)}")
                logger.info(f"GPU Memory: {torch.cuda.get_device_properties(self.device).total_memory / 1e9:.1f} GB")
            else:
                logger.warning(f"GPU {gpu_id} not available. Using CPU.")
                self.device = torch.device('cpu')
        else:
            self.device = torch.device('cpu')
            logger.info("Using CPU")
        
        # Update config with actual device
        self.config['device'] = self.device
    
    def _setup_optimizations(self):
        """Setup GPU optimizations."""
        if self.device.type == 'cuda':
            # Enable mixed precision training
            if self.config.get('enable_mixed_precision', True):
                self.scaler = torch.cuda.amp.GradScaler()
                logger.info("Mixed precision training enabled")
            
            # Set benchmark mode for consistent input sizes
            if self.config.get('benchmark', True):
                torch.backends.cudnn.benchmark = True
                logger.info("CUDNN benchmark mode enabled")
            
            # Set deterministic mode if required
            if self.config.get('deterministic', False):
                torch.backends.cudnn.deterministic = True
                torch.backends.cudnn.benchmark = False
                logger.info("Deterministic mode enabled")
            
            # Enable TF32 on Ampere GPUs
            if self.config.get('allow_tf32', True):
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                logger.info("TF32 enabled for Ampere GPUs")
    
    def to_device(self, data: Union[torch.Tensor, np.ndarray, pd.DataFrame, pd.Series], 
                  non_blocking: bool = None) -> torch.Tensor:
        """
        Move data to GPU device.
        
        Args:
            data: Data to move to device
            non_blocking: Whether to use non-blocking transfer
            
        Returns:
            torch.Tensor: Data on device
        """
        if non_blocking is None:
            non_blocking = self.config.get('non_blocking', True)
        
        if isinstance(data, (pd.DataFrame, pd.Series)):
            data = data.values
        
        if isinstance(data, np.ndarray):
            data = torch.from_numpy(data).float()
        
        if isinstance(data, torch.Tensor):
            return data.to(self.device, non_blocking=non_blocking)
        
        raise ValueError(f"Unsupported data type: {type(data)}")
    
    def create_dataloader(self, X: Union[torch.Tensor, np.ndarray, pd.DataFrame], 
                         y: Union[torch.Tensor, np.ndarray, pd.Series] = None,
                         batch_size: int = 64, shuffle: bool = True, 
                         pin_memory: bool = None) -> DataLoader:
        """
        Create GPU-optimized DataLoader.
        
        Args:
            X: Features
            y: Targets (optional)
            batch_size: Batch size
            shuffle: Whether to shuffle data
            pin_memory: Whether to pin memory
            
        Returns:
            DataLoader: GPU-optimized data loader
        """
        if pin_memory is None:
            pin_memory = self.config.get('pin_memory', True) and self.device.type == 'cuda'
        
        # Convert to tensors
        X_tensor = self.to_device(X)
        
        if y is not None:
            y_tensor = self.to_device(y)
            dataset = TensorDataset(X_tensor, y_tensor)
        else:
            dataset = TensorDataset(X_tensor)
        
        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            pin_memory=pin_memory,
            num_workers=0 if self.device.type == 'cuda' else 2
        )
    
    def clear_cache(self):
        """Clear GPU cache to free memory."""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            gc.collect()
            logger.debug("GPU cache cleared")
    
    def get_memory_info(self) -> dict:
        """
        Get GPU memory information.
        
        Returns:
            dict: Memory information
        """
        if self.device.type == 'cuda':
            allocated = torch.cuda.memory_allocated(self.device) / 1e9
            cached = torch.cuda.memory_reserved(self.device) / 1e9
            total = torch.cuda.get_device_properties(self.device).total_memory / 1e9
            
            return {
                'allocated_gb': allocated,
                'cached_gb': cached,
                'total_gb': total,
                'free_gb': total - cached,
                'utilization': cached / total * 100
            }
        else:
            return {'device': 'cpu'}
    
    def log_memory_usage(self):
        """Log current GPU memory usage."""
        if self.device.type == 'cuda':
            info = self.get_memory_info()
            logger.info(f"GPU Memory - Allocated: {info['allocated_gb']:.2f}GB, "
                       f"Cached: {info['cached_gb']:.2f}GB, "
                       f"Free: {info['free_gb']:.2f}GB, "
                       f"Utilization: {info['utilization']:.1f}%")


def preprocess_data_gpu(X_train: pd.DataFrame, X_val: pd.DataFrame, X_test: pd.DataFrame,
                       y_train: pd.Series, y_val: pd.Series, y_test: pd.Series,
                       gpu_manager: GPUManager, scale: bool = True) -> Tuple[torch.Tensor, ...]:
    """
    Preprocess data on GPU for faster processing.
    
    Args:
        X_train, X_val, X_test: Feature datasets
        y_train, y_val, y_test: Target datasets
        gpu_manager: GPU manager instance
        scale: Whether to scale the data
        
    Returns:
        Tuple of GPU tensors: (X_train, X_val, X_test, y_train, y_val, y_test)
    """
    logger.info("Preprocessing data on GPU...")
    
    # Convert to GPU tensors
    X_train_gpu = gpu_manager.to_device(X_train)
    X_val_gpu = gpu_manager.to_device(X_val)
    X_test_gpu = gpu_manager.to_device(X_test)
    y_train_gpu = gpu_manager.to_device(y_train)
    y_val_gpu = gpu_manager.to_device(y_val)
    y_test_gpu = gpu_manager.to_device(y_test)
    
    if scale:
        # Compute mean and std on training data
        mean = X_train_gpu.mean(dim=0, keepdim=True)
        std = X_train_gpu.std(dim=0, keepdim=True)
        
        # Avoid division by zero
        std = torch.where(std == 0, torch.ones_like(std), std)
        
        # Apply standardization
        X_train_gpu = (X_train_gpu - mean) / std
        X_val_gpu = (X_val_gpu - mean) / std
        X_test_gpu = (X_test_gpu - mean) / std
        
        logger.info("Data standardized on GPU")
    
    return X_train_gpu, X_val_gpu, X_test_gpu, y_train_gpu, y_val_gpu, y_test_gpu


def handle_gpu_errors(func):
    """
    Decorator to handle GPU errors gracefully.
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except RuntimeError as e:
            if "out of memory" in str(e).lower():
                logger.error(f"GPU out of memory in {func.__name__}. Clearing cache and retrying...")
                torch.cuda.empty_cache()
                gc.collect()
                # Try with smaller batch size or fall back to CPU
                raise MemoryError(f"GPU out of memory in {func.__name__}")
            else:
                raise e
    return wrapper
