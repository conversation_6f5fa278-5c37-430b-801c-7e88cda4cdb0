"""
Configuration parameters for the FLKDDrug project.
"""

import os
import logging
from datetime import datetime

# Data configuration
DATA_CONFIG = {
    'primary_file': 'logS_des.csv',
    'secondary_file': None,  # Set to 'logS.csv' if you want to use it
    'combine_data': True,
    'test_size': 0.2,
    'val_size': 0.2,
    'random_state': 42,
    'scale_features': True,
    'handle_outliers': True
}

# Federated Learning configuration
FL_CONFIG = {
    'n_clients': 3,
    'n_rounds': 10,
    'local_epochs': 1,
    'client_sample_ratio': 1.0,
    'client_split_method': 'split',  # 'split', 'dirichlet', 'shard'
    'dirichlet_alpha': 0.5,
    'random_state': 42
}

# Knowledge Distillation configuration
KD_CONFIG = {
    'alpha': 0.5,
    'temperature': 1.0,
    'n_stages': 3,
    'epochs': 1,
    'attention_threshold': 0.8,
    'random_state': 42
}

# Model configuration
MODEL_CONFIG = {
    'default_model_type': 'neural_net',  # Default to neural networks for GPU acceleration

    # Neural Network configurations (GPU-accelerated)
    'neural_net_teacher': {
        'hidden_dims': [256, 128, 64],
        'dropout_rate': 0.2,
        'activation': 'relu',
        'batch_norm': True,
        'learning_rate': 0.0005,  # Reduced learning rate to prevent NaN
        'weight_decay': 1e-4,
        'batch_size': 64,
        'epochs': 100,
        'early_stopping_patience': 10,
        'lr_scheduler': 'cosine',
        'optimizer': 'adamw'
    },
    'neural_net_student': {
        'hidden_dims': [256, 128, 64],
        'dropout_rate': 0.3,
        'activation': 'relu',
        'batch_norm': True,
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 80,
        'early_stopping_patience': 8,
        'lr_scheduler': 'cosine',
        'optimizer': 'adamw'
    },

    # Advanced Neural Network configurations
    'transformer_teacher': {
        'hidden_dims': [512, 256, 128],
        'model_type': 'transformer',
        'num_heads': 8,
        'num_layers': 3,
        'dropout_rate': 0.1,
        'learning_rate': 0.0005,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 120,
        'early_stopping_patience': 15,
        'lr_scheduler': 'cosine',
        'optimizer': 'adamw'
    },
    'transformer_student': {
        'hidden_dims': [256, 128, 64],
        'model_type': 'transformer',
        'num_heads': 4,
        'num_layers': 2,
        'dropout_rate': 0.15,
        'learning_rate': 0.0005,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 100,
        'early_stopping_patience': 12,
        'lr_scheduler': 'cosine',
        'optimizer': 'adamw'
    },
    'resnet_teacher': {
        'hidden_dims': [512, 256, 128],
        'model_type': 'resnet',
        'num_layers': 4,
        'dropout_rate': 0.1,
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 120,
        'early_stopping_patience': 15,
        'lr_scheduler': 'cosine',
        'optimizer': 'adamw'
    },
    'ensemble_teacher': {
        'hidden_dims': [256, 128, 64],
        'model_type': 'ensemble',
        'num_models': 5,
        'dropout_rate': 0.2,
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 100,
        'early_stopping_patience': 12,
        'lr_scheduler': 'cosine',
        'optimizer': 'adamw'
    },

    # LightGBM configurations (with GPU support)
    'lgbm_teacher': {
        'n_estimators': 500,
        'learning_rate': 0.05,
        'num_leaves': 31,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1,
        'random_state': 42,
        'device': 'gpu',  # Enable GPU acceleration
        'gpu_platform_id': 0,
        'gpu_device_id': 0
    },
    'lgbm_student': {
        'n_estimators': 100,
        'learning_rate': 0.05,
        'num_leaves': 15,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1,
        'random_state': 42,
        'device': 'gpu',  # Enable GPU acceleration
        'gpu_platform_id': 0,
        'gpu_device_id': 0
    },

    # Random Forest configurations (CPU-based, kept for compatibility)
    'rf_teacher': {
        'n_estimators': 500,
        'max_depth': 10,
        'min_samples_split': 2,
        'min_samples_leaf': 1,
        'random_state': 42
    },
    'rf_student': {
        'n_estimators': 100,
        'max_depth': 8,
        'min_samples_split': 2,
        'min_samples_leaf': 1,
        'random_state': 42
    }
}

# Hyperparameter optimization configuration
HYPEROPT_CONFIG = {
    'init_points': 10,
    'n_iter': 50,
    'random_state': 42
}

# Evaluation configuration
EVAL_CONFIG = {
    'cv_folds': 5,
    'cv_shuffle': True,
    'cv_random_state': 42
}

# Reinforcement Learning configuration
RL_CONFIG = {
    'use_rl_optimization': True,
    'ppo_episodes': 50,
    'dqn_episodes': 100,
    'rl_learning_rate': 3e-4,
    'gamma': 0.99,
    'epsilon': 1.0,
    'epsilon_decay': 0.995,
    'epsilon_min': 0.01,
    'buffer_size': 10000,
    'batch_size': 32,
    'target_update': 100,
    'use_client_selection': True,
    'use_adaptive_aggregation': True,
    'use_feature_selection': True,
    'max_features': None,  # Will be set based on data
    'param_bounds': {
        'learning_rate': (1e-5, 1e-2),
        'weight_decay': (1e-6, 1e-3),
        'dropout_rate': (0.0, 0.5),
        'batch_size': (16, 128),
        'epochs': (50, 200)
    }
}

# Strategies to run
STRATEGIES = [
    # Federated Learning strategies
    {
        'name': 'fedavg',
        'fl_strategy': 'fedavg',
        'kd_strategy': None,
        'enabled': True
    },
    {
        'name': 'fedprox',
        'fl_strategy': 'fedprox',
        'kd_strategy': None,
        'enabled': True
    },
    {
        'name': 'scaffold',
        'fl_strategy': 'scaffold',
        'kd_strategy': None,
        'enabled': True
    },
    {
        'name': 'personalized_fl',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': None,
        'enabled': True
    },

    # Knowledge Distillation strategies
    {
        'name': 'vanilla_kd',
        'fl_strategy': None,
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'ensemble_kd',
        'fl_strategy': None,
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'progressive_kd',
        'fl_strategy': None,
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'attention_kd',
        'fl_strategy': None,
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # Combined FL+KD strategies
    # FedAvg combinations
    {
        'name': 'fedavg_vanilla_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'fedavg_ensemble_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'fedavg_progressive_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'fedavg_attention_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # FedProx combinations
    {
        'name': 'fedprox_vanilla_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'fedprox_ensemble_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'fedprox_progressive_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'fedprox_attention_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # SCAFFOLD combinations
    {
        'name': 'scaffold_vanilla_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'scaffold_ensemble_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'scaffold_progressive_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'scaffold_attention_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # Personalized FL combinations
    {
        'name': 'personalized_fl_vanilla_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'personalized_fl_ensemble_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'personalized_fl_progressive_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'personalized_fl_attention_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # Contrastive Learning strategies
    {
        'name': 'simclr',
        'fl_strategy': None,
        'kd_strategy': None,
        'contrastive_strategy': 'simclr',
        'enabled': True
    },
    {
        'name': 'simclr_fedavg',
        'fl_strategy': 'fedavg',
        'kd_strategy': None,
        'contrastive_strategy': 'simclr',
        'enabled': True
    },
    {
        'name': 'simclr_vanilla_kd',
        'fl_strategy': None,
        'kd_strategy': 'vanilla_kd',
        'contrastive_strategy': 'simclr',
        'enabled': True
    },

    # Transfer Learning strategies
    {
        'name': 'domain_adaptation',
        'fl_strategy': None,
        'kd_strategy': None,
        'transfer_strategy': 'domain_adaptation',
        'enabled': True
    },
    {
        'name': 'domain_adaptation_fedavg',
        'fl_strategy': 'fedavg',
        'kd_strategy': None,
        'transfer_strategy': 'domain_adaptation',
        'enabled': True
    },
    {
        'name': 'domain_adaptation_vanilla_kd',
        'fl_strategy': None,
        'kd_strategy': 'vanilla_kd',
        'transfer_strategy': 'domain_adaptation',
        'enabled': True
    },

    # Advanced Federated Learning strategies
    {
        'name': 'fedopt_adam',
        'fl_strategy': 'fedopt',
        'kd_strategy': None,
        'server_optimizer': 'adam',
        'enabled': True
    },
    {
        'name': 'fedopt_yogi',
        'fl_strategy': 'fedopt',
        'kd_strategy': None,
        'server_optimizer': 'yogi',
        'enabled': True
    },
    {
        'name': 'fedopt_adagrad',
        'fl_strategy': 'fedopt',
        'kd_strategy': None,
        'server_optimizer': 'adagrad',
        'enabled': True
    },
    {
        'name': 'fedopt_adam_vanilla_kd',
        'fl_strategy': 'fedopt',
        'kd_strategy': 'vanilla_kd',
        'server_optimizer': 'adam',
        'enabled': True
    }
]

# Original model strategy (baseline)
ORIGINAL_STRATEGY = {
    'name': 'original',
    'enabled': True
}

# Output configuration
OUTPUT_CONFIG = {
    'results_dir': 'results',
    'logs_dir': 'results/logs',
    'models_dir': 'results/models',
    'plots_dir': 'results/plots',
    'log_level': logging.INFO,
    'save_models': True,
    'save_plots': True
}

# GPU configuration
GPU_CONFIG = {
    'use_gpu': True,
    'gpu_id': 0,
    'device': None,  # Will be set automatically
    'memory_fraction': 0.9,  # Use 90% of GPU memory
    'enable_mixed_precision': True,  # Use mixed precision training
    'pin_memory': True,  # Pin memory for faster data transfer
    'non_blocking': True,  # Non-blocking data transfer
    'benchmark': True,  # Enable cudnn benchmark for consistent input sizes
    'deterministic': False,  # Set to True for reproducible results (slower)
    'allow_tf32': True,  # Allow TF32 on Ampere GPUs
}

# Create timestamp for this run
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# Create directories
for directory in [OUTPUT_CONFIG['results_dir'],
                 OUTPUT_CONFIG['logs_dir'],
                 OUTPUT_CONFIG['models_dir'],
                 OUTPUT_CONFIG['plots_dir']]:
    os.makedirs(directory, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=OUTPUT_CONFIG['log_level'],
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_CONFIG['logs_dir'], f'run_{TIMESTAMP}.log')),
        logging.StreamHandler()
    ]
)

# Logger
logger = logging.getLogger(__name__)
