"""
Configuration parameters for the FLKDDrug project.
"""

import os
import logging
from datetime import datetime

# Data configuration
DATA_CONFIG = {
    'primary_file': 'logS_des.csv',
    'secondary_file': None,  # Set to 'logS.csv' if you want to use it
    'combine_data': True,
    'test_size': 0.2,
    'val_size': 0.2,
    'random_state': 42,
    'scale_features': True,
    'handle_outliers': True
}

# Federated Learning configuration
FL_CONFIG = {
    'n_clients': 3,
    'n_rounds': 10,
    'local_epochs': 1,
    'client_sample_ratio': 1.0,
    'client_split_method': 'split',  # 'split', 'dirichlet', 'shard'
    'dirichlet_alpha': 0.5,
    'random_state': 42
}

# Knowledge Distillation configuration
KD_CONFIG = {
    'alpha': 0.5,
    'temperature': 1.0,
    'n_stages': 3,
    'epochs': 1,
    'attention_threshold': 0.8,
    'random_state': 42
}

# Model configuration
MODEL_CONFIG = {
    'lgbm_teacher': {
        'n_estimators': 500,
        'learning_rate': 0.05,
        'num_leaves': 31,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1,
        'random_state': 42
    },
    'lgbm_student': {
        'n_estimators': 100,
        'learning_rate': 0.05,
        'num_leaves': 15,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1,
        'random_state': 42
    },
    'rf_teacher': {
        'n_estimators': 500,
        'max_depth': 10,
        'min_samples_split': 2,
        'min_samples_leaf': 1,
        'random_state': 42
    },
    'rf_student': {
        'n_estimators': 100,
        'max_depth': 8,
        'min_samples_split': 2,
        'min_samples_leaf': 1,
        'random_state': 42
    }
}

# Hyperparameter optimization configuration
HYPEROPT_CONFIG = {
    'init_points': 10,
    'n_iter': 50,
    'random_state': 42
}

# Evaluation configuration
EVAL_CONFIG = {
    'cv_folds': 5,
    'cv_shuffle': True,
    'cv_random_state': 42
}

# Strategies to run
STRATEGIES = [
    # Federated Learning strategies
    {
        'name': 'fedavg',
        'fl_strategy': 'fedavg',
        'kd_strategy': None,
        'enabled': True
    },
    {
        'name': 'fedprox',
        'fl_strategy': 'fedprox',
        'kd_strategy': None,
        'enabled': True
    },
    {
        'name': 'scaffold',
        'fl_strategy': 'scaffold',
        'kd_strategy': None,
        'enabled': True
    },
    {
        'name': 'personalized_fl',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': None,
        'enabled': True
    },

    # Knowledge Distillation strategies
    {
        'name': 'vanilla_kd',
        'fl_strategy': None,
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'ensemble_kd',
        'fl_strategy': None,
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'progressive_kd',
        'fl_strategy': None,
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'attention_kd',
        'fl_strategy': None,
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # Combined FL+KD strategies
    # FedAvg combinations
    {
        'name': 'fedavg_vanilla_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'fedavg_ensemble_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'fedavg_progressive_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'fedavg_attention_kd',
        'fl_strategy': 'fedavg',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # FedProx combinations
    {
        'name': 'fedprox_vanilla_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'fedprox_ensemble_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'fedprox_progressive_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'fedprox_attention_kd',
        'fl_strategy': 'fedprox',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # SCAFFOLD combinations
    {
        'name': 'scaffold_vanilla_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'scaffold_ensemble_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'scaffold_progressive_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'scaffold_attention_kd',
        'fl_strategy': 'scaffold',
        'kd_strategy': 'attention_kd',
        'enabled': True
    },

    # Personalized FL combinations
    {
        'name': 'personalized_fl_vanilla_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'vanilla_kd',
        'enabled': True
    },
    {
        'name': 'personalized_fl_ensemble_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'ensemble_kd',
        'enabled': True
    },
    {
        'name': 'personalized_fl_progressive_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'progressive_kd',
        'enabled': True
    },
    {
        'name': 'personalized_fl_attention_kd',
        'fl_strategy': 'personalized_fl',
        'kd_strategy': 'attention_kd',
        'enabled': True
    }
]

# Original model strategy (baseline)
ORIGINAL_STRATEGY = {
    'name': 'original',
    'enabled': True
}

# Output configuration
OUTPUT_CONFIG = {
    'results_dir': 'results',
    'logs_dir': 'results/logs',
    'models_dir': 'results/models',
    'plots_dir': 'results/plots',
    'log_level': logging.INFO,
    'save_models': True,
    'save_plots': True
}

# GPU configuration
GPU_CONFIG = {
    'use_gpu': True,
    'gpu_id': 0
}

# Create timestamp for this run
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# Create directories
for directory in [OUTPUT_CONFIG['results_dir'],
                 OUTPUT_CONFIG['logs_dir'],
                 OUTPUT_CONFIG['models_dir'],
                 OUTPUT_CONFIG['plots_dir']]:
    os.makedirs(directory, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=OUTPUT_CONFIG['log_level'],
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_CONFIG['logs_dir'], f'run_{TIMESTAMP}.log')),
        logging.StreamHandler()
    ]
)

# Logger
logger = logging.getLogger(__name__)
