# %%
from bayes_opt import BayesianOptimization
import pandas as pd
from sklearn.model_selection import train_test_split
from lightgbm import LGBMRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import numpy as np
from sklearn.model_selection import train_test_split, KFold
import json
import os
from datetime import datetime

# Create results directory with timestamp to avoid mixing logs
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
results_dir = f"results/original_CVtest_{timestamp}"
os.makedirs(results_dir, exist_ok=True)

print(f"Cross-validation results will be saved to: {results_dir}")

# %%
def evaluate_performance(best_model, X, y_true):

    y_pred = best_model.predict(X)

    mae = mean_absolute_error(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)

    performance = {
        'MAE': mae,
        'MSE': mse,
        'R2': r2,
    }
    return performance

# %%
# Load data
data = pd.read_csv("logS_des.csv")
X = data.drop(columns=['value'])
y = data['value']

# Initialize StratifiedKFold
skf = KFold(n_splits=5, shuffle=True, random_state=0)

# Initialize lists to store performance metrics
train_performance = []
val_performance = []
test_performance = []

# Loop through each fold
for train_index, test_index in skf.split(X, y):
    print("\nNew round")
    # Split data into train and test sets
    X_remain, X_test = X.iloc[train_index], X.iloc[test_index]
    y_remain, y_test = y.iloc[train_index], y.iloc[test_index]

    # Further split train data into train and validation sets
    X_train, X_val, y_train, y_val = train_test_split(X_remain, y_remain, test_size=0.2, random_state=42)

    # Define function to optimize
    def rf(n_estimators,learning_rate,num_leaves,subsample,subsample_freq,colsample_bytree,colsample_bynode,reg_alpha,reg_lambda):
        rf = LGBMRegressor(
                                    n_estimators=int(n_estimators),
                                    learning_rate=learning_rate,
                                    num_leaves=int(num_leaves),
                                    subsample=subsample,
                                    subsample_freq=int(subsample_freq),
                                    colsample_bytree=colsample_bytree,
                                    colsample_bynode=colsample_bynode,
                                    reg_alpha=reg_alpha,
                                    reg_lambda=reg_lambda,
                                    random_state=42)
        rf.fit(X_train, y_train)
        y_pred = rf.predict(X_val)
        neg_mae=-mean_absolute_error(y_val, y_pred)
        return neg_mae

    # Define parameter bounds for Bayesian optimization
    pbounds = {
    'n_estimators': (100, 1000),
    'learning_rate': (0.001, 0.1),
    'num_leaves': (2, 50),
    'subsample': (0.5, 1.0),
    'subsample_freq': (1, 5),
    'colsample_bytree': (0.1, 1.0),
    'colsample_bynode': (0.1, 1.0),
    'reg_alpha': (0.0, 1.0),
    'reg_lambda': (0.0, 2.0)
            }

    # Run Bayesian optimization
    optimizer = BayesianOptimization(
        f=rf,
        pbounds=pbounds,
        random_state=42,
    )

    optimizer.maximize(init_points=10, n_iter=100)
    params = optimizer.max['params']
    print("Best score: {:.3f}".format(optimizer.max['target']))

    params = optimizer.max['params']
    best_model=LGBMRegressor(
                                    n_estimators=int(params['n_estimators']),
                                    learning_rate=params['learning_rate'],
                                    num_leaves=int(params['num_leaves']),
                                    subsample=params['subsample'],
                                    subsample_freq=int(params['subsample_freq']),
                                    colsample_bytree=params['colsample_bytree'],
                                    colsample_bynode=params['colsample_bynode'],
                                    reg_alpha=params['reg_alpha'],
                                    reg_lambda=params['reg_lambda'], random_state=42
                                    )

    best_model_train=best_model.fit(X_train, y_train)

    performance_train = evaluate_performance(best_model_train, X_train, y_train)
    performance_val = evaluate_performance(best_model_train, X_val, y_val)

    best_model_remain=best_model.fit(X_remain, y_remain)

    performance_test = evaluate_performance(best_model_remain, X_test, y_test)

    print("performance_train=")
    print(performance_train)
    print("performance_val=")
    print(performance_val)
    print("performance_test=")
    print(performance_test)

    train_performance.append(performance_train)
    val_performance.append(performance_val)
    test_performance.append(performance_test)

# Calculate average performance metrics
avg_train_performance = pd.DataFrame(train_performance).mean()
avg_val_performance = pd.DataFrame(val_performance).mean()
avg_test_performance = pd.DataFrame(test_performance).mean()
std_train_performance = pd.DataFrame(train_performance).std()
std_val_performance = pd.DataFrame(val_performance).std()
std_test_performance = pd.DataFrame(test_performance).std()

# Print average performance metrics
print("\n" +"Average Train Performance:" + str(avg_train_performance) + "+/-" + str(std_train_performance))
print("\n" +"Average Validation Performance:", avg_val_performance, "+/-", std_val_performance)
print("\n" +"Average Test Performance:", avg_test_performance, "+/-", std_test_performance)

# Save detailed CV results with timestamp
cv_results = {
    'timestamp': timestamp,
    'n_folds': 5,
    'individual_fold_results': {
        'train_performance': train_performance,
        'val_performance': val_performance,
        'test_performance': test_performance
    },
    'average_performance': {
        'train': avg_train_performance.to_dict(),
        'val': avg_val_performance.to_dict(),
        'test': avg_test_performance.to_dict()
    },
    'std_performance': {
        'train': std_train_performance.to_dict(),
        'val': std_val_performance.to_dict(),
        'test': std_test_performance.to_dict()
    }
}

# Save results to JSON file
with open(f'{results_dir}/original_CVtest_results.json', 'w') as f:
    json.dump(cv_results, f, indent=2, default=str)

print(f"\nCross-validation results saved to {results_dir}/original_CVtest_results.json")
print("Original model cross-validation completed successfully!")
