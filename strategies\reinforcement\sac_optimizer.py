"""
SAC (Soft Actor-Critic) for adaptive federated learning optimization.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from collections import deque
import random
from sklearn.base import BaseEstimator, RegressorMixin
import time

logger = logging.getLogger(__name__)


class SACActorNetwork(nn.Module):
    """Actor network for SAC."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 256]):
        super(SACActorNetwork, self).__init__()
        
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*layers)
        
        # Mean and log std heads
        self.mean_head = nn.Linear(prev_dim, action_dim)
        self.log_std_head = nn.Linear(prev_dim, action_dim)
        
        # Action bounds
        self.action_scale = 1.0
        self.action_bias = 0.0
        
    def forward(self, state):
        """Forward pass."""
        shared = self.shared_layers(state)
        mean = self.mean_head(shared)
        log_std = self.log_std_head(shared)
        log_std = torch.clamp(log_std, min=-20, max=2)
        
        return mean, log_std
    
    def sample(self, state):
        """Sample action from policy."""
        mean, log_std = self.forward(state)
        std = log_std.exp()
        normal = Normal(mean, std)
        
        # Reparameterization trick
        x_t = normal.rsample()
        y_t = torch.tanh(x_t)
        action = y_t * self.action_scale + self.action_bias
        
        # Compute log probability
        log_prob = normal.log_prob(x_t)
        # Enforcing action bounds
        log_prob -= torch.log(self.action_scale * (1 - y_t.pow(2)) + 1e-6)
        log_prob = log_prob.sum(1, keepdim=True)
        
        mean = torch.tanh(mean) * self.action_scale + self.action_bias
        
        return action, log_prob, mean


class SACCriticNetwork(nn.Module):
    """Critic network for SAC."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 256]):
        super(SACCriticNetwork, self).__init__()
        
        # Q1 network
        q1_layers = []
        prev_dim = state_dim + action_dim
        
        for hidden_dim in hidden_dims:
            q1_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        
        q1_layers.append(nn.Linear(prev_dim, 1))
        self.q1_network = nn.Sequential(*q1_layers)
        
        # Q2 network
        q2_layers = []
        prev_dim = state_dim + action_dim
        
        for hidden_dim in hidden_dims:
            q2_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        
        q2_layers.append(nn.Linear(prev_dim, 1))
        self.q2_network = nn.Sequential(*q2_layers)
        
    def forward(self, state, action):
        """Forward pass."""
        sa = torch.cat([state, action], 1)
        q1 = self.q1_network(sa)
        q2 = self.q2_network(sa)
        return q1, q2


class SACReplayBuffer:
    """Replay buffer for SAC."""
    
    def __init__(self, capacity: int = 100000):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """Add experience to buffer."""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int):
        """Sample batch from buffer."""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)


class SACAgent:
    """SAC agent for continuous control."""
    
    def __init__(self, state_dim: int, action_dim: int, 
                 lr: float = 3e-4, gamma: float = 0.99, tau: float = 0.005,
                 alpha: float = 0.2, automatic_entropy_tuning: bool = True,
                 buffer_size: int = 100000, batch_size: int = 256,
                 device: torch.device = None):
        """
        Initialize SAC agent.
        
        Args:
            state_dim: State dimension
            action_dim: Action dimension
            lr: Learning rate
            gamma: Discount factor
            tau: Soft update coefficient
            alpha: Entropy regularization coefficient
            automatic_entropy_tuning: Whether to automatically tune alpha
            buffer_size: Replay buffer size
            batch_size: Training batch size
            device: Device to use
        """
        self.device = device if device is not None else torch.device('cpu')
        self.gamma = gamma
        self.tau = tau
        self.alpha = alpha
        self.batch_size = batch_size
        
        # Networks
        self.actor = SACActorNetwork(state_dim, action_dim).to(self.device)
        self.critic = SACCriticNetwork(state_dim, action_dim).to(self.device)
        self.critic_target = SACCriticNetwork(state_dim, action_dim).to(self.device)
        
        # Copy parameters to target network
        for target_param, param in zip(self.critic_target.parameters(), self.critic.parameters()):
            target_param.data.copy_(param.data)
        
        # Optimizers
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=lr)
        
        # Automatic entropy tuning
        self.automatic_entropy_tuning = automatic_entropy_tuning
        if automatic_entropy_tuning:
            self.target_entropy = -torch.prod(torch.Tensor([action_dim]).to(self.device)).item()
            self.log_alpha = torch.zeros(1, requires_grad=True, device=self.device)
            self.alpha_optimizer = optim.Adam([self.log_alpha], lr=lr)
        
        # Replay buffer
        self.replay_buffer = SACReplayBuffer(buffer_size)
    
    def get_action(self, state, evaluate: bool = False):
        """Get action from policy."""
        state = torch.FloatTensor(state).to(self.device).unsqueeze(0)
        
        if evaluate:
            _, _, action = self.actor.sample(state)
        else:
            action, _, _ = self.actor.sample(state)
        
        return action.detach().cpu().numpy()[0]
    
    def store_experience(self, state, action, reward, next_state, done):
        """Store experience in replay buffer."""
        self.replay_buffer.push(state, action, reward, next_state, done)
    
    def update(self):
        """Update SAC networks."""
        if len(self.replay_buffer) < self.batch_size:
            return
        
        # Sample batch
        state_batch, action_batch, reward_batch, next_state_batch, done_batch = \
            self.replay_buffer.sample(self.batch_size)
        
        # Convert to tensors
        state_batch = torch.FloatTensor(state_batch).to(self.device)
        action_batch = torch.FloatTensor(action_batch).to(self.device)
        reward_batch = torch.FloatTensor(reward_batch).to(self.device).unsqueeze(1)
        next_state_batch = torch.FloatTensor(next_state_batch).to(self.device)
        done_batch = torch.BoolTensor(done_batch).to(self.device).unsqueeze(1)
        
        # Update critic
        with torch.no_grad():
            next_state_action, next_state_log_pi, _ = self.actor.sample(next_state_batch)
            qf1_next_target, qf2_next_target = self.critic_target(next_state_batch, next_state_action)
            min_qf_next_target = torch.min(qf1_next_target, qf2_next_target) - self.alpha * next_state_log_pi
            next_q_value = reward_batch + (~done_batch) * self.gamma * min_qf_next_target
        
        qf1, qf2 = self.critic(state_batch, action_batch)
        qf1_loss = F.mse_loss(qf1, next_q_value)
        qf2_loss = F.mse_loss(qf2, next_q_value)
        qf_loss = qf1_loss + qf2_loss
        
        self.critic_optimizer.zero_grad()
        qf_loss.backward()
        self.critic_optimizer.step()
        
        # Update actor
        pi, log_pi, _ = self.actor.sample(state_batch)
        qf1_pi, qf2_pi = self.critic(state_batch, pi)
        min_qf_pi = torch.min(qf1_pi, qf2_pi)
        
        policy_loss = ((self.alpha * log_pi) - min_qf_pi).mean()
        
        self.actor_optimizer.zero_grad()
        policy_loss.backward()
        self.actor_optimizer.step()
        
        # Update alpha
        if self.automatic_entropy_tuning:
            alpha_loss = -(self.log_alpha * (log_pi + self.target_entropy).detach()).mean()
            
            self.alpha_optimizer.zero_grad()
            alpha_loss.backward()
            self.alpha_optimizer.step()
            
            self.alpha = self.log_alpha.exp()
        
        # Soft update target networks
        for target_param, param in zip(self.critic_target.parameters(), self.critic.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - self.tau) + param.data * self.tau)


class SACFederatedOptimizer(BaseEstimator, RegressorMixin):
    """SAC-based federated learning optimizer."""
    
    def __init__(self, base_model_class, n_clients: int = 5,
                 n_episodes: int = 100, max_steps: int = 50,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize SAC federated optimizer.
        
        Args:
            base_model_class: Base model class to optimize
            n_clients: Number of federated clients
            n_episodes: Number of optimization episodes
            max_steps: Maximum steps per episode
            device: Device to use
            random_state: Random seed
        """
        self.base_model_class = base_model_class
        self.n_clients = n_clients
        self.n_episodes = n_episodes
        self.max_steps = max_steps
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        # SAC parameters
        self.state_dim = 8  # Federated learning state features
        self.action_dim = n_clients  # Client participation weights
        
        # Initialize SAC agent
        self.sac_agent = SACAgent(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            device=self.device
        )
        
        self.best_strategy = None
        self.best_score = -np.inf
        self.history = []
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
        random.seed(random_state)
    
    def _get_state(self, round_num: int, client_losses: List[float], 
                   communication_costs: List[float] = None):
        """Get state representation for SAC."""
        if communication_costs is None:
            communication_costs = [1.0] * len(client_losses)
        
        # Pad lists to ensure consistent size
        while len(client_losses) < self.n_clients:
            client_losses.append(0.0)
            communication_costs.append(1.0)
        
        state = np.array([
            round_num / self.n_episodes,  # Normalized round number
            np.mean(client_losses[:self.n_clients]),  # Average client loss
            np.std(client_losses[:self.n_clients]),   # Client loss variance
            np.min(client_losses[:self.n_clients]),   # Best client performance
            np.max(client_losses[:self.n_clients]),   # Worst client performance
            np.mean(communication_costs[:self.n_clients]),  # Average communication cost
            len([l for l in client_losses[:self.n_clients] if l > 0]) / self.n_clients,  # Active clients ratio
            np.median(client_losses[:self.n_clients])  # Median client loss
        ])
        
        return state
    
    def optimize_federated_strategy(self, client_data: List, X_val, y_val):
        """Optimize federated learning strategy using SAC."""
        logger.info(f"Starting SAC federated optimization for {self.n_episodes} episodes")
        
        for episode in range(self.n_episodes):
            episode_reward = 0
            client_losses = [0.0] * self.n_clients
            
            for step in range(self.max_steps):
                # Get current state
                state = self._get_state(episode, client_losses)
                
                # Get action (client participation weights) from SAC
                action = self.sac_agent.get_action(state)
                
                # Convert action to participation probabilities
                participation_weights = torch.softmax(torch.tensor(action), dim=0).numpy()
                
                # Simulate federated learning round
                try:
                    # Select clients based on participation weights
                    selected_clients = np.random.choice(
                        self.n_clients, 
                        size=min(3, self.n_clients),  # Select up to 3 clients
                        replace=False,
                        p=participation_weights / participation_weights.sum()
                    )
                    
                    # Train selected clients and aggregate
                    client_models = []
                    client_weights = []
                    
                    for client_idx in selected_clients:
                        if client_idx < len(client_data):
                            X_client, y_client = client_data[client_idx]
                            if len(X_client) > 0:
                                # Train client model
                                model = self.base_model_class(device=self.device, epochs=5)
                                model.fit(X_client, y_client)
                                client_models.append(model)
                                client_weights.append(participation_weights[client_idx])
                                
                                # Evaluate client
                                pred = model.predict(X_val)
                                client_losses[client_idx] = -np.mean(np.abs(y_val.values - pred))
                    
                    # Aggregate models (simplified)
                    if client_models:
                        # Use the best performing client model as representative
                        best_client_idx = np.argmax([client_losses[i] for i in selected_clients])
                        best_model = client_models[best_client_idx]
                        
                        # Evaluate aggregated model
                        agg_pred = best_model.predict(X_val)
                        reward = -np.mean(np.abs(y_val.values - agg_pred))
                        
                        # Add efficiency bonus for fewer clients
                        efficiency_bonus = (self.n_clients - len(selected_clients)) / self.n_clients * 0.1
                        reward += efficiency_bonus
                    else:
                        reward = -np.inf
                        
                except Exception as e:
                    logger.warning(f"Error in federated optimization: {e}")
                    reward = -np.inf
                
                # Update best strategy
                if reward > self.best_score:
                    self.best_score = reward
                    self.best_strategy = {
                        'participation_weights': participation_weights.copy(),
                        'selected_clients': selected_clients.tolist() if 'selected_clients' in locals() else []
                    }
                
                # Store experience
                next_state = self._get_state(episode, client_losses)
                done = (step == self.max_steps - 1)
                
                self.sac_agent.store_experience(state, action, reward, next_state, done)
                
                # Update SAC
                self.sac_agent.update()
                
                episode_reward += reward
                state = next_state
            
            # Log progress
            self.history.append(episode_reward)
            if (episode + 1) % 20 == 0:
                logger.info(f"Episode {episode + 1}/{self.n_episodes}, "
                           f"Episode Reward: {episode_reward:.4f}, "
                           f"Best Score: {self.best_score:.4f}")
        
        logger.info(f"SAC optimization completed. Best score: {self.best_score:.4f}")
        return self.best_strategy, self.best_score
    
    def fit(self, X, y, client_data=None):
        """Fit the SAC-optimized federated model."""
        if client_data is None:
            # Create dummy client data for testing
            n_samples = len(X) // self.n_clients
            client_data = []
            for i in range(self.n_clients):
                start_idx = i * n_samples
                end_idx = (i + 1) * n_samples if i < self.n_clients - 1 else len(X)
                client_data.append((X.iloc[start_idx:end_idx], y.iloc[start_idx:end_idx]))
        
        # Split data for validation
        split_idx = int(0.8 * len(X))
        X_val = X.iloc[split_idx:]
        y_val = y.iloc[split_idx:]
        
        # Optimize federated strategy
        self.best_strategy, _ = self.optimize_federated_strategy(client_data, X_val, y_val)
        
        return self
    
    def predict(self, X):
        """Make predictions using optimized strategy."""
        # Placeholder - in practice, use the optimized federated model
        return np.zeros(len(X))
    
    @property
    def feature_importances_(self):
        """Get feature importances."""
        return np.ones(10) / 10  # Placeholder
