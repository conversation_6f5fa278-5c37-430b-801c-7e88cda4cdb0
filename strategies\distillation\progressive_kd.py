"""
Implementation of Progressive Knowledge Distillation.

This module implements progressive knowledge distillation where the student
model is trained in stages with gradually increasing complexity.
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time

logger = logging.getLogger(__name__)

class ProgressiveKD:
    """
    Progressive Knowledge Distillation for regression models.
    
    This implementation trains the student model in stages, gradually
    increasing its complexity while distilling knowledge from the teacher.
    """
    
    def __init__(self, teacher_model=None, student_model=None, 
                 n_stages=3, alpha_schedule=None, random_state=42):
        """
        Initialize ProgressiveKD.
        
        Args:
            teacher_model: Teacher model
            student_model: Final student model configuration (default: LGBMRegressor)
            n_stages: Number of training stages
            alpha_schedule: Schedule of alpha values for each stage (default: linear decrease)
            random_state: Random seed
        """
        self.teacher_model = teacher_model
        
        if student_model is None:
            # Default: LightGBM model
            self.student_model = LGBMRegressor(
                n_estimators=100,
                num_leaves=31,
                learning_rate=0.05,
                random_state=random_state
            )
        else:
            self.student_model = student_model
            
        self.n_stages = n_stages
        
        if alpha_schedule is None:
            # Default: linear decrease from 1.0 to 0.0
            self.alpha_schedule = np.linspace(1.0, 0.0, n_stages)
        else:
            self.alpha_schedule = alpha_schedule
            
        self.random_state = random_state
        self.history = {
            'train_loss': [],
            'val_loss': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_train, y_train, X_val=None, y_val=None, epochs_per_stage=1):
        """
        Train the student model with progressive knowledge distillation.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            epochs_per_stage: Number of epochs per stage
            
        Returns:
            self: Trained model
        """
        if self.teacher_model is None:
            raise ValueError("Teacher model is not provided")
        
        start_time = time.time()
        logger.info(f"Starting Progressive KD training with {self.n_stages} stages")
        
        # Get soft targets from teacher
        soft_targets = self.teacher_model.predict(X_train)
        
        # Create student model configurations for each stage
        student_configs = self._create_student_configs()
        
        # Current student model
        current_student = None
        
        # Training loop for each stage
        for stage in range(self.n_stages):
            stage_start = time.time()
            logger.info(f"Stage {stage+1}/{self.n_stages}, alpha={self.alpha_schedule[stage]:.4f}")
            
            # Create student model for this stage
            student_config = student_configs[stage]
            student = LGBMRegressor(**student_config)
            
            # If we have a model from previous stage, use its predictions as initialization
            if current_student is not None:
                # For LightGBM, we can't directly transfer parameters
                # Instead, we use the previous model's predictions as a starting point
                prev_preds = current_student.predict(X_train)
                
                # Train on residuals
                residuals = y_train - prev_preds
                student.fit(X_train, residuals)
                
                # Combine previous model and new model
                current_student = _CombinedModel([current_student, student])
            else:
                # First stage
                alpha = self.alpha_schedule[stage]
                combined_targets = (1 - alpha) * y_train + alpha * soft_targets
                
                # Train on combined targets
                student.fit(X_train, combined_targets)
                current_student = student
            
            # Evaluate
            if X_val is not None and y_val is not None:
                y_pred = current_student.predict(X_val)
                val_loss = np.mean(np.abs(y_val - y_pred))
                self.history['val_loss'].append(val_loss)
                
                # Also evaluate on training set
                y_pred_train = current_student.predict(X_train)
                train_loss = np.mean(np.abs(y_train - y_pred_train))
                self.history['train_loss'].append(train_loss)
                
                stage_time = time.time() - stage_start
                logger.info(f"Stage {stage+1} completed in {stage_time:.2f}s. "
                           f"Train loss: {train_loss:.4f}, Val loss: {val_loss:.4f}")
        
        # Final model
        self.student_model = current_student
        
        total_time = time.time() - start_time
        logger.info(f"Progressive KD training completed in {total_time:.2f}s")
        
        return self
    
    def _create_student_configs(self):
        """
        Create student model configurations for each stage.
        
        Returns:
            list: List of student model configurations
        """
        # Get base configuration from student_model
        base_config = self.student_model.get_params()
        
        # Create configurations with increasing complexity
        configs = []
        
        if 'n_estimators' in base_config:
            n_estimators = base_config['n_estimators']
            # Gradually increase n_estimators
            estimators_schedule = np.linspace(
                max(10, n_estimators // self.n_stages),
                n_estimators,
                self.n_stages
            ).astype(int)
            
            for i in range(self.n_stages):
                config = base_config.copy()
                config['n_estimators'] = estimators_schedule[i]
                configs.append(config)
        else:
            # If n_estimators not available, use same config for all stages
            configs = [base_config.copy() for _ in range(self.n_stages)]
        
        return configs
    
    def predict(self, X):
        """
        Make predictions using the student model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        return self.student_model.predict(X)
    
    def __getattr__(self, name):
        """
        Forward attribute access to the student model.
        
        Args:
            name: Attribute name
            
        Returns:
            Attribute value
        """
        return getattr(self.student_model, name)

class _CombinedModel:
    """
    Combined model that adds predictions from multiple models.
    """
    
    def __init__(self, models):
        """
        Initialize combined model.
        
        Args:
            models: List of models
        """
        self.models = models
    
    def predict(self, X):
        """
        Make predictions by adding predictions from all models.
        
        Args:
            X: Features
            
        Returns:
            array: Combined predictions
        """
        return np.sum([model.predict(X) for model in self.models], axis=0)
    
    def __getattr__(self, name):
        """
        Forward attribute access to the last model.
        
        Args:
            name: Attribute name
            
        Returns:
            Attribute value
        """
        return getattr(self.models[-1], name)
