"""
Reinforcement Learning strategies for federated learning and knowledge distillation.
Includes advanced RL algorithms: PPO, DQN, TRPO, SAC, Multi-Agent RL, and Hierarchical RL.
Windows-compatible implementations.
"""

from .ppo_optimizer import PPOOptimizer, PPOHyperparameterOptimizer, RLEnhancedRegressor
from .dqn_selector import DQNModelSelector, DQNFeatureSelector
from .rl_federated import RLFederatedLearning
from .trpo_optimizer import TRPOFederatedOptimizer
from .sac_optimizer import SACFederatedOptimizer
from .marl_federated import MARLFederatedLearning
from .hierarchical_rl import HierarchicalFederatedLearning

__all__ = [
    # Original RL strategies
    'PPOOptimizer',
    'PPOHyperparameterOptimizer',
    'RLEnhancedRegressor',
    'DQNModelSelector',
    'DQNFeatureSelector',
    'RLFederatedLearning',

    # Advanced RL strategies (Windows-compatible)
    'TRPOFederatedOptimizer',      # Trust Region Policy Optimization
    'SACFederatedOptimizer',       # Soft Actor-Critic
    'MARLFederatedLearning',       # Multi-Agent Reinforcement Learning
    'HierarchicalFederatedLearning'  # Hierarchical Reinforcement Learning
]
