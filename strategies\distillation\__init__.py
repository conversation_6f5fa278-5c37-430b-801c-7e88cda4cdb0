"""
Knowledge Distillation Strategies for Drug Solubility Prediction.

This module implements various knowledge distillation techniques including:
- VanillaKD: Standard knowledge distillation
- EnsembleKD: Ensemble-based knowledge distillation
- ProgressiveKD: Multi-stage progressive distillation
- AttentionKD: Attention-based knowledge distillation
"""

from .vanilla_kd import VanillaKD
from .ensemble_kd import EnsembleKD
from .progressive_kd import ProgressiveKD
from .attention_kd import AttentionKD

__all__ = ['VanillaKD', 'EnsembleKD', 'ProgressiveKD', 'AttentionKD']
