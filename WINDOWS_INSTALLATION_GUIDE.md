# Windows Installation Guide

## 🪟 **Complete Windows Setup Guide for FLKDDrug**

This guide provides step-by-step instructions for setting up the FLKDDrug project on Windows 10/11 with full GPU acceleration and advanced RL capabilities.

## 📋 **Prerequisites**

### **System Requirements:**
- Windows 10 (version 1903+) or Windows 11
- Python 3.8+ (3.9 or 3.10 recommended)
- At least 8GB RAM (16GB+ recommended)
- 10GB+ free disk space

### **GPU Requirements (Optional but Recommended):**
- NVIDIA GPU with CUDA Compute Capability 3.5+
- At least 4GB GPU memory (8GB+ recommended)

## 🔧 **Step 1: Install Python**

### **Option A: Python.org (Recommended)**
1. Download Python from [python.org](https://www.python.org/downloads/windows/)
2. **Important**: Check "Add Python to PATH" during installation
3. Choose "Customize installation" and ensure "pip" is selected
4. Verify installation:
```cmd
python --version
pip --version
```

### **Option B: Anaconda/Miniconda**
1. Download from [anaconda.com](https://www.anaconda.com/products/distribution)
2. Install with default settings
3. Open "Anaconda Prompt" for all subsequent commands

## 🎮 **Step 2: Install CUDA (For GPU Acceleration)**

### **Check GPU Compatibility:**
```cmd
nvidia-smi
```

### **Install CUDA Toolkit:**
1. Download CUDA 11.8 from [NVIDIA Developer](https://developer.nvidia.com/cuda-11-8-0-download-archive)
2. Choose: Windows → x86_64 → 10/11 → exe (local)
3. Run installer with default settings
4. Verify installation:
```cmd
nvcc --version
```

### **Install cuDNN (Optional for better performance):**
1. Download cuDNN 8.x from [NVIDIA Developer](https://developer.nvidia.com/cudnn)
2. Extract and copy files to CUDA installation directory
3. Add CUDA bin directory to PATH if not already added

## 📦 **Step 3: Clone and Setup Project**

### **Clone Repository:**
```cmd
git clone https://github.com/your-repo/FLKDDrug.git
cd FLKDDrug
```

### **Create Virtual Environment:**
```cmd
# Using venv (recommended)
python -m venv flkd_env
flkd_env\Scripts\activate

# OR using conda
conda create -n flkd_env python=3.9
conda activate flkd_env
```

## 🚀 **Step 4: Install Dependencies**

### **Install PyTorch with CUDA Support:**
```cmd
# For CUDA 11.8 (recommended)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# For CPU only (if no GPU)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### **Install Project Dependencies:**
```cmd
pip install -r requirements.txt
```

### **Verify GPU Setup:**
```cmd
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}'); print(f'GPU count: {torch.cuda.device_count()}')"
```

## 🧪 **Step 5: Test Installation**

### **Basic Functionality Test:**
```cmd
python test_gpu_optimization.py
```

### **Quick Strategy Test:**
```cmd
# Test basic neural network
python run_strategy.py --fl_strategy original --gpu 0 --epochs 5

# Test advanced RL strategy
python run_strategy.py --fl_strategy trpo_federated --gpu 0 --epochs 5
```

## 🔧 **Troubleshooting Common Windows Issues**

### **Issue 1: CUDA Not Found**
**Symptoms:** `RuntimeError: CUDA not available`
**Solutions:**
1. Reinstall PyTorch with correct CUDA version
2. Check NVIDIA driver compatibility
3. Verify CUDA installation path

### **Issue 2: Permission Errors**
**Symptoms:** `PermissionError` during installation
**Solutions:**
1. Run Command Prompt as Administrator
2. Use `--user` flag: `pip install --user -r requirements.txt`
3. Check antivirus software blocking

### **Issue 3: Package Installation Failures**
**Symptoms:** Failed to build wheels for packages
**Solutions:**
1. Install Visual Studio Build Tools
2. Update pip: `python -m pip install --upgrade pip`
3. Install packages individually

### **Issue 4: Memory Errors**
**Symptoms:** `CUDA out of memory`
**Solutions:**
1. Reduce batch size in config.py
2. Enable gradient checkpointing
3. Use mixed precision training

## 🎯 **Step 6: Configuration for Windows**

### **Update config.py for Windows:**
```python
# GPU Configuration
GPU_CONFIG = {
    'use_gpu': True,
    'gpu_id': 0,
    'memory_fraction': 0.8,  # Use 80% of GPU memory
    'mixed_precision': True,
    'pin_memory': True
}

# Windows-specific optimizations
SYSTEM_CONFIG = {
    'num_workers': 0,  # Set to 0 on Windows to avoid multiprocessing issues
    'persistent_workers': False,
    'prefetch_factor': 2
}
```

## 🚀 **Step 7: Running Advanced RL Strategies**

### **All Windows-Compatible RL Strategies:**
```cmd
# Basic RL strategies
python run_strategy.py --fl_strategy rl_enhanced_neural_net --gpu 0
python run_strategy.py --fl_strategy rl_federated_learning --gpu 0

# Advanced RL strategies (Windows-optimized)
python run_strategy.py --fl_strategy trpo_federated --gpu 0
python run_strategy.py --fl_strategy sac_federated --gpu 0
python run_strategy.py --fl_strategy marl_federated --gpu 0
python run_strategy.py --fl_strategy hierarchical_rl_federated --gpu 0

# Combined RL + KD strategies
python run_strategy.py --fl_strategy trpo_federated --kd_strategy vanilla_kd --gpu 0
python run_strategy.py --fl_strategy sac_federated --kd_strategy ensemble_kd --gpu 0
```

### **Run All Strategies:**
```cmd
# Run comprehensive comparison (will take several hours)
python run_all.py --gpu 0 --n_clients 3 --n_rounds 10
```

## 📊 **Performance Optimization for Windows**

### **GPU Memory Optimization:**
```python
# In config.py
GPU_CONFIG = {
    'memory_fraction': 0.7,  # Conservative memory usage
    'empty_cache_frequency': 10,  # Clear cache every 10 batches
    'gradient_checkpointing': True
}
```

### **CPU Optimization:**
```python
# Set optimal thread count
import torch
torch.set_num_threads(4)  # Adjust based on your CPU cores
```

## 🔍 **Verification and Testing**

### **Complete System Test:**
```cmd
# Test all components
python -c "
import torch
import numpy as np
import pandas as pd
from utils.gpu_utils import GPUManager
from strategies.reinforcement import TRPOFederatedOptimizer

print('✅ All imports successful')
print(f'✅ PyTorch version: {torch.__version__}')
print(f'✅ CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'✅ GPU: {torch.cuda.get_device_name(0)}')
print('✅ Windows setup complete!')
"
```

## 📈 **Expected Performance on Windows**

### **Typical Performance Gains:**
- **Neural Network Training**: 10-50x faster with GPU
- **Data Processing**: 5-10x faster with optimized pandas
- **RL Training**: 20-100x faster with GPU acceleration
- **Memory Usage**: 30-50% reduction with mixed precision

### **Benchmark Results (Example):**
- **CPU-only**: ~30 minutes per strategy
- **GPU-accelerated**: ~2-5 minutes per strategy
- **Advanced RL strategies**: ~5-15 minutes per strategy

## 🆘 **Getting Help**

### **Common Commands for Debugging:**
```cmd
# Check Python environment
python -c "import sys; print(sys.executable)"

# Check installed packages
pip list | findstr torch
pip list | findstr numpy

# Check GPU status
nvidia-smi

# Test CUDA
python -c "import torch; print(torch.cuda.is_available())"
```

### **Log Files:**
- Check `logs/` directory for detailed error messages
- GPU memory usage logged in real-time
- Performance metrics saved automatically

## 🎉 **Success Indicators**

You'll know the setup is successful when:
1. ✅ `test_gpu_optimization.py` runs without errors
2. ✅ GPU memory usage is displayed in logs
3. ✅ Training completes significantly faster than CPU-only
4. ✅ All RL strategies can be executed
5. ✅ Results are saved in `results/` directory

## 📞 **Support**

If you encounter issues:
1. Check this troubleshooting guide first
2. Review the main README.md for additional information
3. Check GPU compatibility and driver versions
4. Ensure all dependencies are correctly installed

The FLKDDrug project is now fully optimized for Windows with comprehensive GPU acceleration and advanced reinforcement learning capabilities!

## 🔄 **Regular Maintenance**

### **Keep Your Setup Updated:**
```cmd
# Update PyTorch
pip install --upgrade torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Update other packages
pip install --upgrade -r requirements.txt

# Update NVIDIA drivers regularly for best performance
```

### **Monitor Performance:**
- Use Task Manager to monitor GPU usage
- Check GPU memory with `nvidia-smi`
- Monitor training logs for performance metrics

Your Windows setup is now ready for cutting-edge federated learning and reinforcement learning research! 🚀
