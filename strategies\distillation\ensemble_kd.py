"""
Implementation of Ensemble Knowledge Distillation.

This module implements knowledge distillation from an ensemble of teacher models
to a single student model.
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time

logger = logging.getLogger(__name__)

class EnsembleKD:
    """
    Ensemble Knowledge Distillation for regression models.
    
    This implementation distills knowledge from an ensemble of teacher models
    to a single student model.
    """
    
    def __init__(self, teacher_models=None, student_model=None, 
                 alpha=0.5, teacher_weights=None, random_state=42):
        """
        Initialize EnsembleKD.
        
        Args:
            teacher_models: List of teacher models
            student_model: Student model (default: smaller LGBMRegressor)
            alpha: Weight of soft targets (0-1)
            teacher_weights: Weights for teacher models (default: equal weights)
            random_state: Random seed
        """
        self.teacher_models = teacher_models
        
        if student_model is None:
            # Default: smaller LightGBM model
            self.student_model = LGBMRegressor(
                n_estimators=100,
                num_leaves=31,
                learning_rate=0.05,
                random_state=random_state
            )
        else:
            self.student_model = student_model
            
        self.alpha = alpha
        
        if teacher_weights is None and teacher_models is not None:
            self.teacher_weights = np.ones(len(teacher_models)) / len(teacher_models)
        else:
            self.teacher_weights = teacher_weights
            
        self.random_state = random_state
        self.history = {
            'train_loss': [],
            'val_loss': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_train, y_train, X_val=None, y_val=None, epochs=1):
        """
        Train the student model with ensemble knowledge distillation.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            epochs: Number of training epochs
            
        Returns:
            self: Trained model
        """
        if self.teacher_models is None or len(self.teacher_models) == 0:
            raise ValueError("Teacher models are not provided")
        
        start_time = time.time()
        logger.info(f"Starting Ensemble KD training with {len(self.teacher_models)} teachers and alpha={self.alpha}")
        
        # Get soft targets from each teacher
        teacher_predictions = []
        for i, teacher in enumerate(self.teacher_models):
            preds = teacher.predict(X_train)
            teacher_predictions.append(preds)
            logger.debug(f"Teacher {i+1} predictions: mean={np.mean(preds):.4f}, std={np.std(preds):.4f}")
        
        # Combine teacher predictions using weights
        soft_targets = np.zeros_like(y_train)
        for i, preds in enumerate(teacher_predictions):
            soft_targets += self.teacher_weights[i] * preds
        
        logger.debug(f"Ensemble soft targets: mean={np.mean(soft_targets):.4f}, std={np.std(soft_targets):.4f}")
        
        # Training loop
        for epoch in range(epochs):
            epoch_start = time.time()
            
            # Combine hard and soft targets using alpha
            combined_targets = (1 - self.alpha) * y_train + self.alpha * soft_targets
            
            # Train student on combined targets
            self.student_model.fit(X_train, combined_targets)
            
            # Evaluate
            if X_val is not None and y_val is not None:
                y_pred = self.student_model.predict(X_val)
                val_loss = np.mean(np.abs(y_val - y_pred))
                self.history['val_loss'].append(val_loss)
                
                # Also evaluate on training set
                y_pred_train = self.student_model.predict(X_train)
                train_loss = np.mean(np.abs(y_train - y_pred_train))
                self.history['train_loss'].append(train_loss)
                
                epoch_time = time.time() - epoch_start
                logger.info(f"Epoch {epoch+1}/{epochs} completed in {epoch_time:.2f}s. "
                           f"Train loss: {train_loss:.4f}, Val loss: {val_loss:.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"Ensemble KD training completed in {total_time:.2f}s")
        
        return self
    
    def predict(self, X):
        """
        Make predictions using the student model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        return self.student_model.predict(X)
    
    def __getattr__(self, name):
        """
        Forward attribute access to the student model.
        
        Args:
            name: Attribute name
            
        Returns:
            Attribute value
        """
        return getattr(self.student_model, name)
    
    def evaluate_teachers(self, X, y_true):
        """
        Evaluate each teacher model and the ensemble.
        
        Args:
            X: Features
            y_true: True targets
            
        Returns:
            dict: Dictionary of teacher performances
        """
        performances = {}
        
        # Evaluate each teacher
        for i, teacher in enumerate(self.teacher_models):
            y_pred = teacher.predict(X)
            mae = np.mean(np.abs(y_true - y_pred))
            performances[f'teacher_{i+1}_mae'] = mae
        
        # Evaluate ensemble
        ensemble_preds = np.zeros_like(y_true)
        for i, teacher in enumerate(self.teacher_models):
            ensemble_preds += self.teacher_weights[i] * teacher.predict(X)
        
        ensemble_mae = np.mean(np.abs(y_true - ensemble_preds))
        performances['ensemble_mae'] = ensemble_mae
        
        # Evaluate student
        student_preds = self.student_model.predict(X)
        student_mae = np.mean(np.abs(y_true - student_preds))
        performances['student_mae'] = student_mae
        
        # Log performances
        logger.info(f"Teacher performances: {performances}")
        
        return performances
