import pandas as pd
from sklearn.model_selection import train_test_split, KFold
from lightgbm import LGBMRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import numpy as np
from bayes_opt import BayesianOptimization
import pickle
import os
import json
from datetime import datetime

# Create results directory with timestamp to avoid mixing logs
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
results_dir = f"results/fl_kd_CVtest_{timestamp}"
os.makedirs(results_dir, exist_ok=True)

print(f"FL+KD cross-validation results will be saved to: {results_dir}")

def evaluate_performance(model, X, y_true):
    y_pred = model.predict(X)
    return {
        'MAE': mean_absolute_error(y_true, y_pred),
        'MSE': mean_squared_error(y_true, y_pred),
        'R2': r2_score(y_true, y_pred)
    }

# Load data
try:
    data = pd.read_csv("logS_des.csv")
    if 'value' not in data.columns:
        raise ValueError("Column 'value' not found in logS_des.csv")
    X = data.drop(columns=['value'])
    y = data['value']
except FileNotFoundError:
    print("Error: logS_des.csv not found")
    exit(1)
except Exception as e:
    print(f"Error loading data: {e}")
    exit(1)

# Load additional data (logS.csv) if available
try:
    data_extra = pd.read_csv("logS.csv")
    if 'value' in data_extra.columns:
        X_extra = data_extra.drop(columns=['value'])
        y_extra = data_extra['value']
        X = pd.concat([X, X_extra], axis=0, ignore_index=True)
        y = pd.concat([y, y_extra], axis=0, ignore_index=True)
except FileNotFoundError:
    print("logS.csv not found, using only logS_des.csv")
except Exception as e:
    print(f"Error loading logS.csv: {e}")

# Initialize KFold
skf = KFold(n_splits=5, shuffle=True, random_state=0)
train_performance = []
val_performance = []
test_performance = []

# Federated Learning and KD function
def train_fl_kd(X_train, y_train, X_val, y_val, X_test, y_test, client_seed):
    n_clients = 3
    client_data = np.array_split(pd.concat([X_train, y_train], axis=1), n_clients)
    client_models = []

    def rf(n_estimators, learning_rate, num_leaves, subsample, colsample_bytree, reg_alpha, reg_lambda):
        model = LGBMRegressor(
            n_estimators=int(n_estimators),
            learning_rate=learning_rate,
            num_leaves=int(num_leaves),
            subsample=subsample,
            colsample_bytree=colsample_bytree,
            reg_alpha=reg_alpha,
            reg_lambda=reg_lambda,
            random_state=42
        )
        model.fit(X_train_client, y_train_client)
        y_pred = model.predict(X_val)
        return -mean_absolute_error(y_val, y_pred)

    pbounds = {
        'n_estimators': (100, 1000),
        'learning_rate': (0.0001, 0.1),
        'num_leaves': (2, 50),
        'subsample': (0.5, 1.0),
        'colsample_bytree': (0.1, 1.0),
        'reg_alpha': (0.0, 5.0),
        'reg_lambda': (0.0, 10.0)
    }

    # Train client models
    for i, client_df in enumerate(client_data):
        X_train_client = client_df.drop(columns=['value'])
        y_train_client = client_df['value']

        optimizer = BayesianOptimization(f=rf, pbounds=pbounds, random_state=client_seed+i)
        optimizer.maximize(init_points=5, n_iter=20)
        params = optimizer.max['params']

        client_model = LGBMRegressor(
            n_estimators=int(params['n_estimators']),
            learning_rate=params['learning_rate'],
            num_leaves=int(params['num_leaves']),
            subsample=params['subsample'],
            colsample_bytree=params['colsample_bytree'],
            reg_alpha=params['reg_alpha'],
            reg_lambda=params['reg_lambda'],
            random_state=client_seed+i
        )
        client_model.fit(X_train_client, y_train_client)
        client_models.append(client_model)

    # Teacher: Ensemble predictions
    def ensemble_predict(X, models):
        predictions = np.array([model.predict(X) for model in models])
        return np.mean(predictions, axis=0)

    y_train_soft = ensemble_predict(X_train, client_models)
    y_val_soft = ensemble_predict(X_val, client_models)

    # KD: Train student model
    student_model = LGBMRegressor(
        n_estimators=100,
        num_leaves=10,
        learning_rate=0.05,
        random_state=client_seed
    )
    student_model.fit(
        X_train, y_train_soft,
        eval_set=[(X_val, y_val_soft)],
        eval_metric='mae'
    )

    return student_model

# Cross-validation loop
for fold, (train_index, test_index) in enumerate(skf.split(X, y), 1):
    print(f"\nFold {fold}")
    X_remain, X_test = X.iloc[train_index], X.iloc[test_index]
    y_remain, y_test = y.iloc[train_index], y.iloc[test_index]
    X_train, X_val, y_train, y_val = train_test_split(X_remain, y_remain, test_size=0.2, random_state=42)

    try:
        student_model = train_fl_kd(X_train, y_train, X_val, y_val, X_test, y_test, client_seed=42+fold)

        performance_train = evaluate_performance(student_model, X_train, y_train)
        performance_val = evaluate_performance(student_model, X_val, y_val)
        performance_test = evaluate_performance(student_model, X_test, y_test)

        print("Student Model Performance:")
        print("Train:", performance_train)
        print("Validation:", performance_val)
        print("Test:", performance_test)

        train_performance.append(performance_train)
        val_performance.append(performance_val)
        test_performance.append(performance_test)
    except Exception as e:
        print(f"Error in fold {fold}: {e}")
        continue

# Calculate and print average performance
avg_train_performance = pd.DataFrame(train_performance).mean()
avg_val_performance = pd.DataFrame(val_performance).mean()
avg_test_performance = pd.DataFrame(test_performance).mean()
std_train_performance = pd.DataFrame(train_performance).std()
std_val_performance = pd.DataFrame(val_performance).std()
std_test_performance = pd.DataFrame(test_performance).std()

print("\nAverage Train Performance:")
print(avg_train_performance, "\n±", std_train_performance)
print("\nAverage Validation Performance:")
print(avg_val_performance, "\n±", std_val_performance)
print("\nAverage Test Performance:")
print(avg_test_performance, "\n±", std_test_performance)

# Save detailed CV results with timestamp
fl_kd_cv_results = {
    'timestamp': timestamp,
    'n_folds': 5,
    'n_clients': 3,
    'individual_fold_results': {
        'train_performance': train_performance,
        'val_performance': val_performance,
        'test_performance': test_performance
    },
    'average_performance': {
        'train': avg_train_performance.to_dict(),
        'val': avg_val_performance.to_dict(),
        'test': avg_test_performance.to_dict()
    },
    'std_performance': {
        'train': std_train_performance.to_dict(),
        'val': std_val_performance.to_dict(),
        'test': std_test_performance.to_dict()
    }
}

# Save results to JSON file
with open(f'{results_dir}/fl_kd_CVtest_results.json', 'w') as f:
    json.dump(fl_kd_cv_results, f, indent=2, default=str)

print(f"\nFL+KD CV results saved to {results_dir}/fl_kd_CVtest_results.json")

# Train and save final student model
try:
    final_student = train_fl_kd(X_remain, y_remain, X_val, y_val, X_test, y_test, client_seed=42)

    # Save model with timestamp
    with open(f'{results_dir}/fl_kd_CVtest_model.pkl', 'wb') as f:
        pickle.dump(final_student, f)
    # Also save to main directory for compatibility
    with open('model_cv_fl_kd.pkl', 'wb') as f:
        pickle.dump(final_student, f)
    print(f"Final student model saved to {results_dir}/fl_kd_CVtest_model.pkl and model_cv_fl_kd.pkl")

    # Visualize predictions
    predictions = final_student.predict(X)
    plt.scatter(y, predictions, label='All Data', color='blue')
    plt.plot([-2, 3], [-2, 3], linestyle='--', color='black')
    plt.xlabel('Actual Values')
    plt.ylabel('Predicted Values')
    plt.title('Actual vs Predicted logS (FL+KD CV)')
    plt.legend()
    plt.xlim(-2, 3)
    plt.ylim(-2, 3)
    plt.savefig(f'{results_dir}/fl_kd_CVtest_predictions.png', dpi=300, bbox_inches='tight')
    plt.savefig('cv_fl_kd_actual_vs_predicted.png')  # For compatibility
    plt.show()

    print("FL+KD cross-validation completed successfully!")
except Exception as e:
    print(f"Error training final model: {e}")