2025-05-26 10:17:01,328 - utils.gpu_utils - INFO - Using GPU: NVIDIA GeForce GTX 1050
2025-05-26 10:17:01,329 - utils.gpu_utils - INFO - GPU Memory: 4.3 GB
2025-05-26 10:17:01,330 - utils.gpu_utils - INFO - Applying Windows-specific GPU optimizations
2025-05-26 10:17:01,330 - utils.gpu_utils - INFO - Enabled Flash SDP for Windows
2025-05-26 10:17:01,330 - utils.gpu_utils - INFO - Mixed precision training enabled
2025-05-26 10:17:01,330 - utils.gpu_utils - INFO - CUDNN benchmark mode enabled
2025-05-26 10:17:01,331 - utils.gpu_utils - INFO - TF32 enabled for Ampere GPUs
2025-05-26 10:17:01,331 - __main__ - INFO - Using GPU 0: cuda:0
2025-05-26 10:17:01,333 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.00GB, Free: 4.29GB, Utilization: 0.0%
2025-05-26 10:17:01,334 - __main__ - INFO - Loading data...
2025-05-26 10:17:02,139 - utils.data_utils - INFO - Loaded primary data from logS_des.csv: 14594 samples, 199 features
2025-05-26 10:17:02,141 - __main__ - INFO - Splitting data...
2025-05-26 10:17:02,213 - utils.data_utils - INFO - Data split: train=9340, val=2335, test=2919
2025-05-26 10:17:02,214 - __main__ - INFO - Preprocessing data...
2025-05-26 10:17:02,270 - utils.data_utils - INFO - Found 62 NaN values in training data
2025-05-26 10:17:02,433 - utils.data_utils - INFO - NaN values filled with median values
2025-05-26 10:17:02,592 - utils.data_utils - INFO - Data scaled using RobustScaler
2025-05-26 10:17:02,595 - __main__ - INFO - Creating client data for 3 clients...
2025-05-26 10:17:02,639 - utils.data_utils - INFO - Client 1: 3114 samples, mean(y)=-2.8155, std(y)=2.1633
2025-05-26 10:17:02,639 - utils.data_utils - INFO - Client 2: 3113 samples, mean(y)=-2.8533, std(y)=2.2124
2025-05-26 10:17:02,640 - utils.data_utils - INFO - Client 3: 3113 samples, mean(y)=-2.9316, std(y)=2.2317
2025-05-26 10:17:06,058 - utils.visualization - INFO - Plot saved to results/plots\client_data_distribution.png
2025-05-26 10:17:06,065 - __main__ - INFO - Running GPU-accelerated neural network baseline model...
2025-05-26 10:17:06,232 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 10:17:31,733 - utils.neural_models - INFO - Early stopping at epoch 10
2025-05-26 10:17:31,733 - utils.neural_models - INFO - Neural network training completed in 25.50s
2025-05-26 10:17:31,734 - __main__ - INFO - Neural network training completed successfully
