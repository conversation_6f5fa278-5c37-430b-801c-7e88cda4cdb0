"""
SimCLR (Simple Contrastive Learning of Visual Representations) for Drug Solubility.

Adapted for molecular data by creating augmented views of molecular descriptors
and learning representations that bring similar molecules closer together.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.metrics import mean_squared_error, r2_score
import logging
import time

logger = logging.getLogger(__name__)


class MolecularAugmentation:
    """Data augmentation for molecular descriptors."""
    
    def __init__(self, noise_std=0.1, dropout_rate=0.1):
        self.noise_std = noise_std
        self.dropout_rate = dropout_rate
    
    def augment(self, X):
        """Create augmented views of molecular data."""
        X_aug1 = self._add_noise(X)
        X_aug2 = self._feature_dropout(X)
        return X_aug1, X_aug2
    
    def _add_noise(self, X):
        """Add Gaussian noise to features."""
        noise = torch.randn_like(X) * self.noise_std
        return X + noise
    
    def _feature_dropout(self, X):
        """Randomly mask some features."""
        mask = torch.rand_like(X) > self.dropout_rate
        return X * mask.float()


class ContrastiveEncoder(nn.Module):
    """Encoder network for contrastive learning."""
    
    def __init__(self, input_dim, hidden_dims=[256, 128], projection_dim=64):
        super().__init__()
        
        # Encoder layers
        layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*layers)
        
        # Projection head for contrastive learning
        self.projection_head = nn.Sequential(
            nn.Linear(prev_dim, projection_dim),
            nn.ReLU(),
            nn.Linear(projection_dim, projection_dim)
        )
        
        # Prediction head for downstream task
        self.prediction_head = nn.Sequential(
            nn.Linear(prev_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 1)
        )
    
    def forward(self, x, return_projection=False):
        features = self.encoder(x)
        
        if return_projection:
            projection = self.projection_head(features)
            return features, F.normalize(projection, dim=1)
        else:
            return self.prediction_head(features)


class SimCLR(BaseEstimator, RegressorMixin):
    """
    SimCLR for molecular property prediction.
    
    Uses contrastive learning to learn molecular representations,
    then fine-tunes for solubility prediction.
    """
    
    def __init__(self, hidden_dims=[256, 128], projection_dim=64, 
                 temperature=0.1, learning_rate=0.001, batch_size=64,
                 contrastive_epochs=50, finetune_epochs=50, device=None):
        self.hidden_dims = hidden_dims
        self.projection_dim = projection_dim
        self.temperature = temperature
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.contrastive_epochs = contrastive_epochs
        self.finetune_epochs = finetune_epochs
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model = None
        self.augmentation = MolecularAugmentation()
        self.history = {'contrastive_loss': [], 'finetune_loss': []}
    
    def contrastive_loss(self, z1, z2):
        """Compute NT-Xent (Normalized Temperature-scaled Cross Entropy) loss."""
        batch_size = z1.shape[0]
        
        # Concatenate representations
        z = torch.cat([z1, z2], dim=0)  # Shape: (2*batch_size, projection_dim)
        
        # Compute similarity matrix
        sim_matrix = torch.mm(z, z.t()) / self.temperature
        
        # Create labels for positive pairs
        labels = torch.cat([torch.arange(batch_size) + batch_size,
                           torch.arange(batch_size)], dim=0).to(self.device)
        
        # Mask out self-similarities
        mask = torch.eye(2 * batch_size, dtype=torch.bool).to(self.device)
        sim_matrix = sim_matrix.masked_fill(mask, -float('inf'))
        
        # Compute cross-entropy loss
        loss = F.cross_entropy(sim_matrix, labels)
        return loss
    
    def fit(self, X, y):
        """
        Fit SimCLR model with two-stage training:
        1. Contrastive pre-training
        2. Supervised fine-tuning
        """
        start_time = time.time()
        logger.info(f"Training SimCLR on {self.device}")
        
        # Convert to tensors
        if isinstance(X, pd.DataFrame):
            X = X.values
        if isinstance(y, pd.Series):
            y = y.values
            
        X = torch.FloatTensor(X).to(self.device)
        y = torch.FloatTensor(y).to(self.device)
        
        # Initialize model
        input_dim = X.shape[1]
        self.model = ContrastiveEncoder(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            projection_dim=self.projection_dim
        ).to(self.device)
        
        # Stage 1: Contrastive pre-training
        logger.info("Stage 1: Contrastive pre-training")
        self._contrastive_pretrain(X)
        
        # Stage 2: Supervised fine-tuning
        logger.info("Stage 2: Supervised fine-tuning")
        self._supervised_finetune(X, y)
        
        total_time = time.time() - start_time
        logger.info(f"SimCLR training completed in {total_time:.2f}s")
        
        return self
    
    def _contrastive_pretrain(self, X):
        """Contrastive pre-training stage."""
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        dataset = torch.utils.data.TensorDataset(X)
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True
        )
        
        self.model.train()
        for epoch in range(self.contrastive_epochs):
            total_loss = 0.0
            
            for (batch_X,) in dataloader:
                # Create augmented views
                x1, x2 = self.augmentation.augment(batch_X)
                
                # Get projections
                _, z1 = self.model(x1, return_projection=True)
                _, z2 = self.model(x2, return_projection=True)
                
                # Compute contrastive loss
                loss = self.contrastive_loss(z1, z2)
                
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(dataloader)
            self.history['contrastive_loss'].append(avg_loss)
            
            if epoch % 10 == 0:
                logger.info(f"Contrastive Epoch {epoch}/{self.contrastive_epochs}, Loss: {avg_loss:.4f}")
    
    def _supervised_finetune(self, X, y):
        """Supervised fine-tuning stage."""
        # Freeze encoder, only train prediction head
        for param in self.model.encoder.parameters():
            param.requires_grad = False
        
        optimizer = torch.optim.Adam(
            self.model.prediction_head.parameters(), 
            lr=self.learning_rate * 0.1  # Lower learning rate for fine-tuning
        )
        
        dataset = torch.utils.data.TensorDataset(X, y)
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True
        )
        
        self.model.train()
        for epoch in range(self.finetune_epochs):
            total_loss = 0.0
            
            for batch_X, batch_y in dataloader:
                predictions = self.model(batch_X)
                loss = F.mse_loss(predictions.squeeze(), batch_y)
                
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(dataloader)
            self.history['finetune_loss'].append(avg_loss)
            
            if epoch % 10 == 0:
                logger.info(f"Finetune Epoch {epoch}/{self.finetune_epochs}, Loss: {avg_loss:.4f}")
    
    def predict(self, X):
        """Make predictions."""
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        self.model.eval()
        
        if isinstance(X, pd.DataFrame):
            X = X.values
        X = torch.FloatTensor(X).to(self.device)
        
        with torch.no_grad():
            predictions = self.model(X)
            predictions = predictions.cpu().numpy().flatten()
        
        return predictions
    
    @property
    def feature_importances_(self):
        """Compute feature importances using gradient-based method."""
        if self.model is None:
            return np.ones(199) / 199  # Default uniform importance
        
        # Use gradient of first layer weights as importance
        first_layer = self.model.encoder[0]  # First linear layer
        if hasattr(first_layer, 'weight'):
            importances = torch.norm(first_layer.weight, dim=0).detach().cpu().numpy()
            return importances / np.sum(importances)
        else:
            return np.ones(199) / 199
