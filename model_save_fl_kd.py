import pandas as pd
from sklearn.model_selection import train_test_split
from lightgbm import LGBMRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import numpy as np
from bayes_opt import BayesianOptimization
import pickle

def evaluate_performance(model, X, y_true):
    y_pred = model.predict(X)
    return {
        'MAE': mean_absolute_error(y_true, y_pred),
        'MSE': mean_squared_error(y_true, y_pred),
        'R2': r2_score(y_true, y_pred)
    }

# Load data
try:
    data = pd.read_csv("logS_des.csv")
    if 'value' not in data.columns:
        raise ValueError("Column 'value' not found in logS_des.csv")
    X = data.drop(columns=['value'])
    y = data['value']
except FileNotFoundError:
    print("Error: logS_des.csv not found")
    exit(1)
except Exception as e:
    print(f"Error loading data: {e}")
    exit(1)

# Load additional data (logS.csv) if available
try:
    data_extra = pd.read_csv("logS.csv")
    if 'value' in data_extra.columns:
        X_extra = data_extra.drop(columns=['value'])
        y_extra = data_extra['value']
        # Combine datasets for FL
        X = pd.concat([X, X_extra], axis=0, ignore_index=True)
        y = pd.concat([y, y_extra], axis=0, ignore_index=True)
except FileNotFoundError:
    print("logS.csv not found, using only logS_des.csv")
except Exception as e:
    print(f"Error loading logS.csv: {e}")

# Split data
train_data, test_data = train_test_split(pd.concat([X, y], axis=1), test_size=0.2, random_state=42)
X_remain = train_data.drop(columns=['value'])
y_remain = train_data['value']
X_test = test_data.drop(columns=['value'])
y_test = test_data['value']
X_train, X_val, y_train, y_val = train_test_split(X_remain, y_remain, test_size=0.2, random_state=42)

# Federated Learning: Simulate 3 clients
n_clients = 3
client_data = np.array_split(pd.concat([X_train, y_train], axis=1), n_clients)
client_models = []

# Optimization function for each client
def rf(n_estimators, learning_rate, num_leaves, subsample, colsample_bytree, reg_alpha, reg_lambda):
    model = LGBMRegressor(
        n_estimators=int(n_estimators),
        learning_rate=learning_rate,
        num_leaves=int(num_leaves),
        subsample=subsample,
        colsample_bytree=colsample_bytree,
        reg_alpha=reg_alpha,
        reg_lambda=reg_lambda,
        random_state=42
    )
    model.fit(X_train_client, y_train_client)
    y_pred = model.predict(X_val)
    return -mean_absolute_error(y_val, y_pred)

pbounds = {
    'n_estimators': (100, 1000),
    'learning_rate': (0.0001, 0.1),
    'num_leaves': (2, 50),
    'subsample': (0.5, 1.0),
    'colsample_bytree': (0.1, 1.0),
    'reg_alpha': (0.0, 5.0),
    'reg_lambda': (0.0, 10.0)
}

# Train client models
for i, client_df in enumerate(client_data):
    print(f"Training client {i+1}")
    X_train_client = client_df.drop(columns=['value'])
    y_train_client = client_df['value']
    
    optimizer = BayesianOptimization(f=rf, pbounds=pbounds, random_state=42+i)
    optimizer.maximize(init_points=5, n_iter=20)
    params = optimizer.max['params']
    
    client_model = LGBMRegressor(
        n_estimators=int(params['n_estimators']),
        learning_rate=params['learning_rate'],
        num_leaves=int(params['num_leaves']),
        subsample=params['subsample'],
        colsample_bytree=params['colsample_bytree'],
        reg_alpha=params['reg_alpha'],
        reg_lambda=params['reg_lambda'],
        random_state=42+i
    )
    client_model.fit(X_train_client, y_train_client)
    client_models.append(client_model)

# Teacher model: Ensemble predictions
def ensemble_predict(X, models):
    predictions = np.array([model.predict(X) for model in models])
    return np.mean(predictions, axis=0)

# Generate soft targets for KD
y_train_soft = ensemble_predict(X_train, client_models)
y_val_soft = ensemble_predict(X_val, client_models)

# Knowledge Distillation: Train student model
student_model = LGBMRegressor(
    n_estimators=100,  # Smaller model
    num_leaves=10,
    learning_rate=0.05,
    random_state=42
)
student_model.fit(
    X_train, y_train_soft,
    eval_set=[(X_val, y_val_soft)],
    eval_metric='mae'
)

# Evaluate student model on original labels
performance_train = evaluate_performance(student_model, X_train, y_train)
performance_val = evaluate_performance(student_model, X_val, y_val)
performance_test = evaluate_performance(student_model, X_test, y_test)

print("Student Model Performance:")
print("Train:", performance_train)
print("Validation:", performance_val)
print("Test:", performance_test)

# Visualize results
predictions_train = student_model.predict(X_train)
predictions_test = student_model.predict(X_test)
plt.scatter(y_train, predictions_train, label='Training Data', color='blue')
plt.scatter(y_test, predictions_test, label='Testing Data', color='red')
plt.plot([-2, 3], [-2, 3], linestyle='--', color='black')
plt.xlabel('Actual Values')
plt.ylabel('Predicted Values')
plt.title('Actual vs Predicted logS (FL+KD)')
plt.legend()
plt.xlim(-2, 3)
plt.ylim(-2, 3)
plt.savefig('fl_kd_actual_vs_predicted.png')
plt.show()

# Save student model
try:
    with open('model_fl_kd.pkl', 'wb') as f:
        pickle.dump(student_model, f)
    print("Student model saved as model_fl_kd.pkl")
except Exception as e:
    print(f"Error saving model: {e}")