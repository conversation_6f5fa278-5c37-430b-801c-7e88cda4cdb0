"""
Script to run all strategies and compare results.
"""

import os
import sys
import argparse
import logging
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle
import json

# Create timestamped results directory to avoid mixing logs
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
results_dir = f"results/run_all_{timestamp}"
os.makedirs(results_dir, exist_ok=True)

print(f"All strategies results will be saved to: {results_dir}")

# Import configuration
import config

# Import utilities
from utils.data_utils import load_data, split_data, create_client_data, preprocess_data
from utils.evaluation import evaluate_model, compare_models, log_results, aggregate_results
from utils.visualization import (
    plot_actual_vs_predicted, plot_residuals, plot_feature_importance,
    plot_strategy_comparison, plot_client_data_distribution
)
from utils.model_utils import create_model, save_model
from utils.gpu_utils import GPUManager

# Import federated learning strategies
from strategies.federated.fedavg import FedAvg
from strategies.federated.fedprox import FedProx
from strategies.federated.scaffold import SCAFFOLD
from strategies.federated.personalized_fl import PersonalizedFL

# Import knowledge distillation strategies
from strategies.distillation.vanilla_kd import VanillaKD
from strategies.distillation.ensemble_kd import EnsembleKD
from strategies.distillation.progressive_kd import ProgressiveKD
from strategies.distillation.attention_kd import AttentionKD

# Import contrastive learning strategies
from strategies.contrastive.simclr import SimCLR

# Import transfer learning strategies
from strategies.transfer.domain_adaptation import DomainAdaptation

# Import advanced federated learning strategies
from strategies.federated.fedopt import FedOpt

# Import original model dependencies
from lightgbm import LGBMRegressor
from bayes_opt import BayesianOptimization
from sklearn.model_selection import KFold
from sklearn.metrics import mean_absolute_error, r2_score

# Set up logger
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run all strategies and compare results')

    parser.add_argument('--gpu', type=int, default=-1, help='GPU ID to use (-1 for CPU)')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--output_dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--n_clients', type=int, default=3, help='Number of clients for federated learning')
    parser.add_argument('--n_rounds', type=int, default=10, help='Number of communication rounds')
    parser.add_argument('--local_epochs', type=int, default=1, help='Number of local epochs')
    parser.add_argument('--strategies', type=str, nargs='+', default=None,
                       help='Specific strategies to run (default: all enabled in config)')

    return parser.parse_args()

def run_original_model(X_train, y_train, X_val, y_val, X_test, y_test, gpu_manager=None):
    """
    Run the original model exactly as in original-model_save.py.

    This replicates the exact LightGBM with Bayesian optimization approach
    from the original scripts to ensure 'original' results are consistent.
    """
    logger.info("Running original LightGBM model with Bayesian optimization (replicating original-model_save.py)...")

    # Combine train and val back to X_remain, y_remain (as in original script)
    X_remain = pd.concat([X_train, X_val], axis=0)
    y_remain = pd.concat([y_train, y_val], axis=0)

    # Define optimization function exactly as in original-model_save.py
    def rf(n_estimators, learning_rate, subsample, colsample_bytree, reg_alpha, reg_lambda, num_leaves, subsample_freq, colsample_bynode):
        model_params = {
            'n_estimators': int(n_estimators),
            'learning_rate': learning_rate,
            'subsample_freq': int(subsample_freq),
            'num_leaves': int(num_leaves),
            'subsample': subsample,
            'colsample_bytree': colsample_bytree,
            'colsample_bynode': colsample_bynode,
            'reg_alpha': reg_alpha,
            'reg_lambda': reg_lambda,
            'random_state': 42
        }

        # Enable GPU for LightGBM if available
        if gpu_manager and gpu_manager.device.type == 'cuda':
            model_params.update({
                'device': 'gpu',
                'gpu_platform_id': 0,
                'gpu_device_id': gpu_manager.device.index or 0
            })

        rf_model = LGBMRegressor(**model_params)
        rf_model.fit(X_train, y_train)
        y_pred_train = rf_model.predict(X_train)
        y_pred = rf_model.predict(X_val)

        # Loss function exactly as in original script
        loss = -mean_absolute_error(y_val, y_pred) - 0*(r2_score(y_train, y_pred_train) - r2_score(y_val, y_pred))
        return loss

    # Parameter bounds exactly as in original-model_save.py
    pbounds = {
        'n_estimators': (100, 1000),
        'learning_rate': (0.0001, 0.1),
        'num_leaves': (2, 50),
        'subsample': (0.5, 1.0),
        'subsample_freq': (1, 5),
        'colsample_bytree': (0.1, 1.0),
        'colsample_bynode': (0.1, 1.0),
        'reg_alpha': (0.0, 5.0),
        'reg_lambda': (0.0, 10.0)
    }

    # Run Bayesian optimization exactly as in original script
    optimizer = BayesianOptimization(
        f=rf,
        pbounds=pbounds,
        random_state=42
    )

    # Use reduced optimization parameters for faster execution (original: init_points=10, n_iter=100)
    optimizer.maximize(init_points=5, n_iter=20)

    params = optimizer.max['params']
    logger.info(f"Best score: {optimizer.max['target']:.3f}")
    logger.info(f"Best parameters: {params}")

    # Create final model with best parameters exactly as in original script
    final_params = {
        'n_estimators': int(params['n_estimators']),
        'learning_rate': params['learning_rate'],
        'subsample': params['subsample'],
        'subsample_freq': int(params['subsample_freq']),
        'num_leaves': int(params['num_leaves']),
        'colsample_bytree': params['colsample_bytree'],
        'colsample_bynode': params['colsample_bynode'],
        'reg_alpha': params['reg_alpha'],
        'reg_lambda': params['reg_lambda'],
        'random_state': 42
    }

    # Enable GPU for LightGBM if available
    if gpu_manager and gpu_manager.device.type == 'cuda':
        final_params.update({
            'device': 'gpu',
            'gpu_platform_id': 0,
            'gpu_device_id': gpu_manager.device.index or 0
        })

    best_model = LGBMRegressor(**final_params)

    # Train on X_remain, y_remain as in original script
    best_model.fit(X_remain, y_remain)
    logger.info("Original LightGBM model training completed successfully")



    # Evaluate model
    train_metrics, train_preds = evaluate_model(best_model, X_train, y_train, prefix="train_")
    val_metrics, val_preds = evaluate_model(best_model, X_val, y_val, prefix="val_")
    test_metrics, test_preds = evaluate_model(best_model, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}

    # Log results
    log_file = log_results(metrics, "original", config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(best_model, os.path.join(config.OUTPUT_CONFIG['models_dir'], 'original_model.pkl'))

    # Create plots
    if config.OUTPUT_CONFIG['save_plots']:
        plot_actual_vs_predicted(
            y_test, test_preds,
            title="Original Model: Actual vs Predicted",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'original_actual_vs_pred.png'),
            show=False
        )

        plot_residuals(
            y_test, test_preds,
            title="Original Model: Residuals",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'original_residuals.png'),
            show=False
        )

        plot_feature_importance(
            best_model, X_train.columns,
            title="Original Model: Feature Importance",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'original_feature_importance.png'),
            show=False
        )

    return best_model, metrics

def run_federated_learning(strategy_name, X_train, y_train, X_val, y_val, X_test, y_test, client_data, server_optimizer=None):
    """Run a federated learning strategy."""
    logger.info(f"Running federated learning strategy: {strategy_name}")

    # Create base model
    base_model = create_model(
        model_type='lgbm',
        params=config.MODEL_CONFIG['lgbm_teacher']
    )

    # Initialize strategy
    if strategy_name == 'fedavg':
        strategy = FedAvg(
            base_model=base_model,
            n_clients=config.FL_CONFIG['n_clients'],
            client_data=client_data,
            n_rounds=config.FL_CONFIG['n_rounds'],
            local_epochs=config.FL_CONFIG['local_epochs'],
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            random_state=config.FL_CONFIG['random_state']
        )
    elif strategy_name == 'fedprox':
        strategy = FedProx(
            base_model=base_model,
            n_clients=config.FL_CONFIG['n_clients'],
            client_data=client_data,
            n_rounds=config.FL_CONFIG['n_rounds'],
            local_epochs=config.FL_CONFIG['local_epochs'],
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            mu=0.01,  # Proximal term coefficient
            random_state=config.FL_CONFIG['random_state']
        )
    elif strategy_name == 'scaffold':
        strategy = SCAFFOLD(
            base_model=base_model,
            n_clients=config.FL_CONFIG['n_clients'],
            client_data=client_data,
            n_rounds=config.FL_CONFIG['n_rounds'],
            local_epochs=config.FL_CONFIG['local_epochs'],
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            learning_rate=0.1,  # Learning rate for control variate updates
            random_state=config.FL_CONFIG['random_state']
        )
    elif strategy_name == 'personalized_fl':
        strategy = PersonalizedFL(
            base_model=base_model,
            n_clients=config.FL_CONFIG['n_clients'],
            client_data=client_data,
            n_rounds=config.FL_CONFIG['n_rounds'],
            local_epochs=config.FL_CONFIG['local_epochs'],
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            personalization_alpha=0.5,  # Weight of global model
            random_state=config.FL_CONFIG['random_state']
        )
    elif strategy_name == 'fedopt':
        strategy = FedOpt(
            n_clients=config.FL_CONFIG['n_clients'],
            n_rounds=config.FL_CONFIG['n_rounds'],
            local_epochs=config.FL_CONFIG['local_epochs'],
            client_fraction=config.FL_CONFIG['client_fraction'],
            server_optimizer=server_optimizer or 'adam',
            model_type=config.MODEL_CONFIG['default_model_type'],
            model_config=config.MODEL_CONFIG['neural_net_teacher']
        )
    else:
        raise ValueError(f"Unknown federated learning strategy: {strategy_name}")

    # Train strategy
    strategy.fit(X_val, y_val)

    # Evaluate strategy
    train_metrics, train_preds = evaluate_model(strategy, X_train, y_train, prefix="train_")
    val_metrics, val_preds = evaluate_model(strategy, X_val, y_val, prefix="val_")
    test_metrics, test_preds = evaluate_model(strategy, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}

    # Log results
    log_file = log_results(metrics, strategy_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(strategy, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{strategy_name}_model.pkl'))

    # Create plots
    if config.OUTPUT_CONFIG['save_plots']:
        plot_actual_vs_predicted(
            y_test, test_preds,
            title=f"{strategy_name}: Actual vs Predicted",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_actual_vs_pred.png'),
            show=False
        )

        plot_residuals(
            y_test, test_preds,
            title=f"{strategy_name}: Residuals",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_residuals.png'),
            show=False
        )

    return strategy, metrics

def run_knowledge_distillation(strategy_name, teacher_model, X_train, y_train, X_val, y_val, X_test, y_test):
    """Run a knowledge distillation strategy."""
    logger.info(f"Running knowledge distillation strategy: {strategy_name}")

    # Create student model
    student_model = create_model(
        model_type='lgbm',
        params=config.MODEL_CONFIG['lgbm_student']
    )

    # Initialize strategy
    if strategy_name == 'vanilla_kd':
        strategy = VanillaKD(
            teacher_model=teacher_model,
            student_model=student_model,
            alpha=config.KD_CONFIG['alpha'],
            temperature=config.KD_CONFIG['temperature'],
            random_state=config.KD_CONFIG['random_state']
        )
    elif strategy_name == 'ensemble_kd':
        # For ensemble KD, teacher_model should be a list of models
        if not isinstance(teacher_model, list):
            teacher_model = [teacher_model]

        strategy = EnsembleKD(
            teacher_models=teacher_model,
            student_model=student_model,
            alpha=config.KD_CONFIG['alpha'],
            random_state=config.KD_CONFIG['random_state']
        )
    elif strategy_name == 'progressive_kd':
        strategy = ProgressiveKD(
            teacher_model=teacher_model,
            student_model=student_model,
            n_stages=config.KD_CONFIG['n_stages'],
            random_state=config.KD_CONFIG['random_state']
        )
    elif strategy_name == 'attention_kd':
        strategy = AttentionKD(
            teacher_model=teacher_model,
            student_model=student_model,
            sample_attention=True,
            feature_attention=True,
            attention_threshold=config.KD_CONFIG['attention_threshold'],
            random_state=config.KD_CONFIG['random_state']
        )
    else:
        raise ValueError(f"Unknown knowledge distillation strategy: {strategy_name}")

    # Train strategy
    strategy.fit(X_train, y_train, X_val, y_val, epochs=config.KD_CONFIG['epochs'])

    # Evaluate strategy
    train_metrics, train_preds = evaluate_model(strategy, X_train, y_train, prefix="train_")
    val_metrics, val_preds = evaluate_model(strategy, X_val, y_val, prefix="val_")
    test_metrics, test_preds = evaluate_model(strategy, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}

    # Log results
    log_file = log_results(metrics, strategy_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(strategy, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{strategy_name}_model.pkl'))

    # Create plots
    if config.OUTPUT_CONFIG['save_plots']:
        plot_actual_vs_predicted(
            y_test, test_preds,
            title=f"{strategy_name}: Actual vs Predicted",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_actual_vs_pred.png'),
            show=False
        )

        plot_residuals(
            y_test, test_preds,
            title=f"{strategy_name}: Residuals",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_residuals.png'),
            show=False
        )

    return strategy, metrics

def run_combined_strategy(fl_strategy_name, kd_strategy_name, X_train, y_train, X_val, y_val, X_test, y_test, client_data):
    """Run a combined federated learning and knowledge distillation strategy."""
    logger.info(f"Running combined strategy: {fl_strategy_name}+{kd_strategy_name}")

    # First run federated learning
    fl_model, _ = run_federated_learning(fl_strategy_name, X_train, y_train, X_val, y_val, X_test, y_test, client_data)

    # Then run knowledge distillation with FL model as teacher
    combined_model, metrics = run_knowledge_distillation(kd_strategy_name, fl_model, X_train, y_train, X_val, y_val, X_test, y_test)

    # Update strategy name in metrics
    combined_name = f"{fl_strategy_name}_{kd_strategy_name}"
    metrics['strategy'] = combined_name

    # Log results
    log_file = log_results(metrics, combined_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(combined_model, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{combined_name}_model.pkl'))

    return combined_model, metrics

def run_contrastive_learning(strategy_config, X_train, y_train, X_val, y_val, X_test, y_test):
    """Run contrastive learning strategy."""
    strategy_name = strategy_config['contrastive_strategy']
    logger.info(f"Running contrastive learning strategy: {strategy_name}")

    start_time = time.time()

    # Create strategy
    if strategy_name == 'simclr':
        strategy = SimCLR(
            hidden_dims=config.MODEL_CONFIG['neural_net_teacher']['hidden_dims'],
            learning_rate=config.MODEL_CONFIG['neural_net_teacher']['learning_rate'],
            batch_size=config.MODEL_CONFIG['neural_net_teacher']['batch_size'],
            contrastive_epochs=50,
            finetune_epochs=50
        )
    else:
        raise ValueError(f"Unknown contrastive learning strategy: {strategy_name}")

    # Train strategy
    strategy.fit(X_train, y_train)

    # Evaluate strategy
    train_metrics, _ = evaluate_model(strategy, X_train, y_train, prefix="train_")
    val_metrics, _ = evaluate_model(strategy, X_val, y_val, prefix="val_")
    test_metrics, _ = evaluate_model(strategy, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}
    metrics['training_time'] = time.time() - start_time
    metrics['strategy'] = strategy_name

    # Log results
    log_results(metrics, strategy_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(strategy, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{strategy_name}_model.pkl'))

    return strategy, metrics

def run_transfer_learning(strategy_config, X_train, y_train, X_val, y_val, X_test, y_test):
    """Run transfer learning strategy."""
    strategy_name = strategy_config['transfer_strategy']
    logger.info(f"Running transfer learning strategy: {strategy_name}")

    start_time = time.time()

    # Create strategy
    if strategy_name == 'domain_adaptation':
        strategy = DomainAdaptation(
            hidden_dims=config.MODEL_CONFIG['neural_net_teacher']['hidden_dims'],
            learning_rate=config.MODEL_CONFIG['neural_net_teacher']['learning_rate'],
            batch_size=config.MODEL_CONFIG['neural_net_teacher']['batch_size'],
            epochs=config.MODEL_CONFIG['neural_net_teacher']['epochs']
        )
    else:
        raise ValueError(f"Unknown transfer learning strategy: {strategy_name}")

    # Train strategy
    strategy.fit(X_train, y_train)

    # Evaluate strategy
    train_metrics, _ = evaluate_model(strategy, X_train, y_train, prefix="train_")
    val_metrics, _ = evaluate_model(strategy, X_val, y_val, prefix="val_")
    test_metrics, _ = evaluate_model(strategy, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}
    metrics['training_time'] = time.time() - start_time
    metrics['strategy'] = strategy_name

    # Log results
    log_results(metrics, strategy_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(strategy, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{strategy_name}_model.pkl'))

    return strategy, metrics

def main():
    """Main function to run all strategies and compare results."""
    # Parse command line arguments
    args = parse_args()

    # Update configuration with command line arguments
    config.FL_CONFIG['n_clients'] = args.n_clients
    config.FL_CONFIG['n_rounds'] = args.n_rounds
    config.FL_CONFIG['local_epochs'] = args.local_epochs
    config.GPU_CONFIG['gpu_id'] = args.gpu
    config.GPU_CONFIG['use_gpu'] = args.gpu >= 0
    config.DATA_CONFIG['random_state'] = args.seed
    config.OUTPUT_CONFIG['results_dir'] = args.output_dir

    # Configure GPU if available
    gpu_manager = GPUManager(config.GPU_CONFIG)

    if config.GPU_CONFIG['use_gpu']:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(config.GPU_CONFIG['gpu_id'])
        logger.info(f"Using GPU {config.GPU_CONFIG['gpu_id']}: {gpu_manager.device}")
        gpu_manager.log_memory_usage()
    else:
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        logger.info("Using CPU")

    # Load data using EXACT same method as original files
    logger.info("Loading data using original method...")

    # Load data exactly as in original-model_save.py and original-model_CVtest.py
    try:
        data = pd.read_csv("logS_des.csv")
        if 'value' not in data.columns:
            raise ValueError("Column 'value' not found in logS_des.csv")
        X = data.drop(columns=['value'])
        y = data['value']
        logger.info(f"Loaded logS_des.csv: {X.shape} features, {y.shape} targets")
    except FileNotFoundError:
        logger.error("Error: logS_des.csv not found")
        return
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return

    # Split data using EXACT same method as original files
    from sklearn.model_selection import train_test_split
    logger.info("Splitting data using original method...")

    # First split: separate test set (same as original)
    X_remain, X_test, y_remain, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )

    # Second split: separate train and validation (same as original)
    X_train, X_val, y_train, y_val = train_test_split(
        X_remain, y_remain, test_size=0.2, random_state=42
    )

    logger.info(f"Data split - Train: {X_train.shape}, Val: {X_val.shape}, Test: {X_test.shape}")

    # Create client data for federated learning (simple split)
    logger.info(f"Creating client data for {args.n_clients} clients...")
    client_data = np.array_split(pd.concat([X_train, y_train], axis=1), args.n_clients)
    logger.info(f"Created {len(client_data)} client datasets")

    # Run original model as baseline
    if config.ORIGINAL_STRATEGY['enabled']:
        original_model, original_metrics = run_original_model(
            X_train, y_train, X_val, y_val, X_test, y_test, gpu_manager
        )

    # Dictionary to store all results
    all_results = {}
    if config.ORIGINAL_STRATEGY['enabled']:
        all_results['original'] = original_metrics

    # Run enabled strategies
    for strategy in config.STRATEGIES:
        # Skip disabled strategies
        if not strategy['enabled']:
            continue

        # Skip strategies not in the command line arguments (if specified)
        if args.strategies is not None and strategy['name'] not in args.strategies:
            continue

        try:
            # Run strategy based on type
            if strategy.get('contrastive_strategy'):
                # Contrastive learning strategy
                model, metrics = run_contrastive_learning(
                    strategy, X_train, y_train, X_val, y_val, X_test, y_test
                )
            elif strategy.get('transfer_strategy'):
                # Transfer learning strategy
                model, metrics = run_transfer_learning(
                    strategy, X_train, y_train, X_val, y_val, X_test, y_test
                )
            elif strategy['fl_strategy'] is not None and strategy['kd_strategy'] is not None:
                # Combined strategy
                model, metrics = run_combined_strategy(
                    strategy['fl_strategy'], strategy['kd_strategy'],
                    X_train, y_train, X_val, y_val, X_test, y_test, client_data
                )
            elif strategy['fl_strategy'] is not None:
                # Federated learning strategy
                model, metrics = run_federated_learning(
                    strategy['fl_strategy'],
                    X_train, y_train, X_val, y_val, X_test, y_test, client_data,
                    server_optimizer=strategy.get('server_optimizer')
                )
            elif strategy['kd_strategy'] is not None:
                # Knowledge distillation strategy
                teacher = original_model if config.ORIGINAL_STRATEGY['enabled'] else None
                model, metrics = run_knowledge_distillation(
                    strategy['kd_strategy'], teacher,
                    X_train, y_train, X_val, y_val, X_test, y_test
                )

            # Store results
            all_results[strategy['name']] = metrics

        except Exception as e:
            logger.error(f"Error running strategy {strategy['name']}: {e}")
            continue

    # Aggregate results
    results_df = pd.DataFrame(all_results).T

    # Save aggregated results with timestamp to avoid mixing logs
    results_df.to_csv(f'{results_dir}/all_results.csv')
    results_df.to_csv(os.path.join(config.OUTPUT_CONFIG['results_dir'], 'all_results.csv'))  # For compatibility

    # Save detailed results as JSON
    with open(f'{results_dir}/all_results_detailed.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)

    logger.info(f"All results saved to {results_dir}")

    # Plot strategy comparison
    if config.OUTPUT_CONFIG['save_plots']:
        # Compare MAE
        plot_strategy_comparison(
            results_df, metric='test_MAE',
            title="Strategy Comparison - Test MAE",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'strategy_comparison_mae.png'),
            show=False
        )

        # Compare MSE
        plot_strategy_comparison(
            results_df, metric='test_MSE',
            title="Strategy Comparison - Test MSE",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'strategy_comparison_mse.png'),
            show=False
        )

        # Compare RMSE
        plot_strategy_comparison(
            results_df, metric='test_RMSE',
            title="Strategy Comparison - Test RMSE",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'strategy_comparison_rmse.png'),
            show=False
        )

        # Compare R2
        plot_strategy_comparison(
            results_df, metric='test_R2',
            title="Strategy Comparison - Test R²",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'strategy_comparison_r2.png'),
            show=False
        )

        # Compare EVS (Explained Variance Score)
        plot_strategy_comparison(
            results_df, metric='test_EVS',
            title="Strategy Comparison - Test EVS",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'strategy_comparison_evs.png'),
            show=False
        )

        # Compare MAPE (Mean Absolute Percentage Error)
        if 'test_MAPE' in results_df.columns:
            plot_strategy_comparison(
                results_df, metric='test_MAPE',
                title="Strategy Comparison - Test MAPE",
                save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'strategy_comparison_mape.png'),
                show=False
            )

    logger.info("All strategies completed. Results saved to results directory.")

if __name__ == "__main__":
    main()
