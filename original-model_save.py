# %%
import pandas as pd
from sklearn.model_selection import train_test_split
from lightgbm import LGBMRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import numpy as np
from bayes_opt import BayesianOptimization

# %%
def evaluate_performance(best_model, X, y_true):
    y_pred = best_model.predict(X)

    mae = mean_absolute_error(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)

    performance = {
        'MAE': mae,
        'MSE': mse,
        'R2': r2,
    }
    return performance

# %%
data = pd.read_csv("logS_des.csv")
X = data.iloc[:, 1:]
y = data['value']

train_data, test_data = train_test_split(data, test_size=0.2, random_state=42)

X_remain = train_data.iloc[:, 1:]
y_remain = train_data['value']
X_test=test_data.iloc[:, 1:]
y_test=test_data['value']

X_train, X_val, y_train, y_val = train_test_split(X_remain, y_remain, test_size=0.2, random_state=42)

# Define function to optimize
def rf(n_estimators, learning_rate,subsample, colsample_bytree, reg_alpha, reg_lambda, num_leaves, subsample_freq, colsample_bynode):
        rf = LGBMRegressor( 
                                    n_estimators=int(n_estimators),
                                    learning_rate=learning_rate,
                                    subsample_freq=int(subsample_freq),
                                    num_leaves=int(num_leaves),
                                    subsample=subsample,
                                    colsample_bytree=colsample_bytree,
                                    colsample_bynode=colsample_bynode,
                                    reg_alpha=reg_alpha,
                                    reg_lambda=reg_lambda,
                                    random_state=42
                                    )
        rf.fit(X_train, y_train)
        y_pred_train = rf.predict(X_train)
        y_pred = rf.predict(X_val)
        loss= -mean_absolute_error(y_val, y_pred) - 0*(r2_score(y_train, y_pred_train)-r2_score(y_val, y_pred))
        return loss

# Define parameter bounds for Bayesian optimization
pbounds = {
    'n_estimators': (100, 1000),  
    'learning_rate': (0.0001, 0.1),  
    'num_leaves': (2, 50), 
    'subsample': (0.5, 1.0),  
    'subsample_freq': (1, 5),  
    'colsample_bytree': (0.1, 1.0), 
    'colsample_bynode': (0.1, 1.0),  
    'reg_alpha': (0.0, 5.0),  
    'reg_lambda': (0.0, 10.0)  
            }

#Run Bayesian optimization
optimizer = BayesianOptimization(
        f=rf,
        pbounds=pbounds,
        random_state=42, 
    )

optimizer.maximize(init_points=10, n_iter=100)

params = optimizer.max['params']
print("Best score: {:.3f}".format(optimizer.max['target']))
    
best_model=LGBMRegressor( 
                                    n_estimators=int(params['n_estimators']),
                                    learning_rate=params['learning_rate'],
                                    subsample=params['subsample'],
                                    subsample_freq=int(params['subsample_freq']),
                                    num_leaves=int(params['num_leaves']),
                                    colsample_bytree=params['colsample_bytree'],
                                    colsample_bynode=params['colsample_bynode'],
                                    reg_alpha=params['reg_alpha'],
                                    reg_lambda=params['reg_lambda'], random_state=42
                                    )

# %%
best_model_train=best_model.fit(X_train, y_train)  
performance_val = evaluate_performance(best_model_train, X_val, y_val)

best_model_remain=best_model.fit(X_remain, y_remain)
performance_train = evaluate_performance(best_model_remain, X_remain, y_remain)
performance_test = evaluate_performance(best_model_remain, X_test, y_test)
    
print("performance_train=")
print(performance_train)
print("performance_val=")
print(performance_val)
print("performance_test=")
print(performance_test)

# %%
predictions_train = best_model_remain.predict(X_remain)
train_data['predictions'] = predictions_train

predictions_test = best_model_remain.predict(X_test)
test_data['predictions'] = predictions_test

# %%
plt.scatter(y_remain, predictions_train, label='Training Data', color='blue')
plt.scatter(y_test, predictions_test, label='Testing Data', color='red')
plt.plot([-2, 3], [-2, 3], linestyle='--', color='black')  
plt.xlabel('Actual Values')
plt.ylabel('Predicted Values')

plt.title('Actual vs Predicted Values')
plt.legend()
plt.xlim(-2, 3)  
plt.ylim(-2, 3)  
plt.show()

# %%
output_model = best_model.fit(X,y)

import pickle
with open('model.pkl', 'wb') as f:
    pickle.dump(output_model, f)