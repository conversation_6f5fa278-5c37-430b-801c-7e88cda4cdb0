"""
Advanced neural network models and architectures for enhanced performance.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import logging
from typing import List, Optional, Dict, Any, Tuple
import time
from sklearn.base import BaseEstimator, RegressorMixin

logger = logging.getLogger(__name__)


class ResidualBlock(nn.Module):
    """Residual block for deeper networks."""

    def __init__(self, dim: int, dropout_rate: float = 0.1):
        super(ResidualBlock, self).__init__()
        self.linear1 = nn.Linear(dim, dim)
        self.linear2 = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(dropout_rate)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)

    def forward(self, x):
        residual = x
        x = self.norm1(x)
        x = F.relu(self.linear1(x))
        x = self.dropout(x)
        x = self.norm2(x)
        x = self.linear2(x)
        x = self.dropout(x)
        return F.relu(x + residual)


class AttentionLayer(nn.Module):
    """Self-attention layer for feature importance."""

    def __init__(self, dim: int, num_heads: int = 8):
        super(AttentionLayer, self).__init__()
        self.num_heads = num_heads
        self.dim = dim
        self.head_dim = dim // num_heads

        self.query = nn.Linear(dim, dim)
        self.key = nn.Linear(dim, dim)
        self.value = nn.Linear(dim, dim)
        self.output = nn.Linear(dim, dim)

    def forward(self, x):
        batch_size = x.size(0)

        # Generate Q, K, V
        Q = self.query(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        attention = F.softmax(scores, dim=-1)

        # Apply attention
        context = torch.matmul(attention, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.dim)

        return self.output(context.squeeze(1))


class TransformerRegressor(nn.Module):
    """Transformer-based regressor with self-attention."""

    def __init__(self, input_dim: int, hidden_dims: List[int],
                 num_heads: int = 8, num_layers: int = 3,
                 dropout_rate: float = 0.1):
        super(TransformerRegressor, self).__init__()

        self.input_projection = nn.Linear(input_dim, hidden_dims[0])

        # Transformer layers
        self.transformer_layers = nn.ModuleList([
            AttentionLayer(hidden_dims[0], num_heads) for _ in range(num_layers)
        ])

        # Feed-forward layers
        layers = []
        for i in range(len(hidden_dims) - 1):
            layers.extend([
                nn.Linear(hidden_dims[i], hidden_dims[i + 1]),
                nn.LayerNorm(hidden_dims[i + 1]),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])

        layers.append(nn.Linear(hidden_dims[-1], 1))
        self.ff_layers = nn.Sequential(*layers)

    def forward(self, x):
        x = self.input_projection(x)
        x = x.unsqueeze(1)  # Add sequence dimension

        # Apply transformer layers
        for transformer in self.transformer_layers:
            x = transformer(x)

        x = x.squeeze(1)  # Remove sequence dimension
        return self.ff_layers(x).squeeze(-1)


class ResNetRegressor(nn.Module):
    """ResNet-style regressor with residual connections."""

    def __init__(self, input_dim: int, hidden_dims: List[int],
                 num_blocks: int = 3, dropout_rate: float = 0.1):
        super(ResNetRegressor, self).__init__()

        self.input_layer = nn.Linear(input_dim, hidden_dims[0])
        self.input_norm = nn.LayerNorm(hidden_dims[0])

        # Residual blocks
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_dims[0], dropout_rate) for _ in range(num_blocks)
        ])

        # Output layers
        layers = []
        for i in range(len(hidden_dims) - 1):
            layers.extend([
                nn.Linear(hidden_dims[i], hidden_dims[i + 1]),
                nn.LayerNorm(hidden_dims[i + 1]),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])

        layers.append(nn.Linear(hidden_dims[-1], 1))
        self.output_layers = nn.Sequential(*layers)

    def forward(self, x):
        x = F.relu(self.input_norm(self.input_layer(x)))

        # Apply residual blocks
        for block in self.residual_blocks:
            x = block(x)

        return self.output_layers(x).squeeze(-1)


class EnsembleNeuralNet(nn.Module):
    """Ensemble of neural networks for improved performance."""

    def __init__(self, input_dim: int, hidden_dims: List[int],
                 num_models: int = 5, model_type: str = 'mlp',
                 dropout_rate: float = 0.1):
        super(EnsembleNeuralNet, self).__init__()

        self.num_models = num_models
        self.models = nn.ModuleList()

        for i in range(num_models):
            if model_type == 'transformer':
                model = TransformerRegressor(input_dim, hidden_dims, dropout_rate=dropout_rate)
            elif model_type == 'resnet':
                model = ResNetRegressor(input_dim, hidden_dims, dropout_rate=dropout_rate)
            else:  # mlp
                layers = []
                prev_dim = input_dim
                for hidden_dim in hidden_dims:
                    layers.extend([
                        nn.Linear(prev_dim, hidden_dim),
                        nn.LayerNorm(hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate)
                    ])
                    prev_dim = hidden_dim
                layers.append(nn.Linear(prev_dim, 1))
                model = nn.Sequential(*layers)

            self.models.append(model)

    def forward(self, x):
        outputs = []
        for model in self.models:
            output = model(x)
            if output.dim() > 1:
                output = output.squeeze(-1)
            outputs.append(output)

        # Average ensemble predictions
        return torch.stack(outputs).mean(dim=0)


class AdvancedNeuralRegressor(BaseEstimator, RegressorMixin):
    """Advanced neural network regressor with multiple architectures."""

    def __init__(self, hidden_dims: List[int] = [512, 256, 128, 64],
                 model_type: str = 'transformer',  # 'mlp', 'transformer', 'resnet', 'ensemble'
                 num_heads: int = 8, num_layers: int = 3, num_models: int = 5,
                 dropout_rate: float = 0.1, learning_rate: float = 0.001,
                 weight_decay: float = 1e-5, batch_size: int = 64,
                 epochs: int = 100, early_stopping_patience: int = 15,
                 lr_scheduler: str = 'cosine', optimizer: str = 'adamw',
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize advanced neural network regressor.

        Args:
            hidden_dims: Hidden layer dimensions
            model_type: Type of model ('mlp', 'transformer', 'resnet', 'ensemble')
            num_heads: Number of attention heads for transformer
            num_layers: Number of transformer/residual layers
            num_models: Number of models in ensemble
            dropout_rate: Dropout rate
            learning_rate: Learning rate
            weight_decay: Weight decay
            batch_size: Batch size
            epochs: Number of epochs
            early_stopping_patience: Early stopping patience
            lr_scheduler: Learning rate scheduler
            optimizer: Optimizer type
            device: Device to use
            random_state: Random seed
        """
        self.hidden_dims = hidden_dims
        self.model_type = model_type
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.num_models = num_models
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.batch_size = batch_size
        self.epochs = epochs
        self.early_stopping_patience = early_stopping_patience
        self.lr_scheduler = lr_scheduler
        self.optimizer = optimizer
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state

        self.model = None
        self.optimizer_obj = None
        self.scheduler = None
        self.scaler = None
        self.history = {'train_loss': [], 'val_loss': []}

        # Set random seeds
        torch.manual_seed(random_state)
        if self.device.type == 'cuda':
            torch.cuda.manual_seed(random_state)

    def _create_model(self, input_dim: int):
        """Create the neural network model."""
        if self.model_type == 'transformer':
            self.model = TransformerRegressor(
                input_dim=input_dim,
                hidden_dims=self.hidden_dims,
                num_heads=self.num_heads,
                num_layers=self.num_layers,
                dropout_rate=self.dropout_rate
            ).to(self.device)
        elif self.model_type == 'resnet':
            self.model = ResNetRegressor(
                input_dim=input_dim,
                hidden_dims=self.hidden_dims,
                num_blocks=self.num_layers,
                dropout_rate=self.dropout_rate
            ).to(self.device)
        elif self.model_type == 'ensemble':
            self.model = EnsembleNeuralNet(
                input_dim=input_dim,
                hidden_dims=self.hidden_dims,
                num_models=self.num_models,
                model_type='mlp',
                dropout_rate=self.dropout_rate
            ).to(self.device)
        else:  # mlp
            from .neural_models import MLPRegressor
            self.model = MLPRegressor(
                input_dim=input_dim,
                hidden_dims=self.hidden_dims,
                dropout_rate=self.dropout_rate,
                activation='relu',
                batch_norm=True
            ).to(self.device)

        # Create optimizer
        if self.optimizer == 'adamw':
            self.optimizer_obj = optim.AdamW(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif self.optimizer == 'adam':
            self.optimizer_obj = optim.Adam(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif self.optimizer == 'sgd':
            self.optimizer_obj = optim.SGD(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay,
                momentum=0.9
            )

        # Create learning rate scheduler
        if self.lr_scheduler == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer_obj, T_max=self.epochs
            )
        elif self.lr_scheduler == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer_obj, step_size=30, gamma=0.1
            )
        elif self.lr_scheduler == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer_obj, mode='min', patience=5, factor=0.5
            )

        # Mixed precision scaler
        if self.device.type == 'cuda':
            self.scaler = torch.cuda.amp.GradScaler()

    def fit(self, X, y, X_val=None, y_val=None, sample_weight=None):
        """Fit the advanced neural network."""
        start_time = time.time()
        logger.info(f"Training {self.model_type} neural network on {self.device}")

        # Convert to tensors and move to device
        if not isinstance(X, torch.Tensor):
            X = torch.from_numpy(X.values if hasattr(X, 'values') else X).float()
        if not isinstance(y, torch.Tensor):
            y = torch.from_numpy(y.values if hasattr(y, 'values') else y).float()

        # Check for NaN/inf values in input data
        if torch.isnan(X).any() or torch.isinf(X).any():
            logger.warning("NaN/inf values detected in input features, replacing with zeros")
            X = torch.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)

        if torch.isnan(y).any() or torch.isinf(y).any():
            logger.warning("NaN/inf values detected in targets, replacing with zeros")
            y = torch.nan_to_num(y, nan=0.0, posinf=1e6, neginf=-1e6)

        # Normalize input features to prevent gradient explosion
        X_mean = X.mean(dim=0, keepdim=True)
        X_std = X.std(dim=0, keepdim=True)
        X_std = torch.where(X_std == 0, torch.ones_like(X_std), X_std)
        X = (X - X_mean) / X_std

        # Store normalization parameters
        self.X_mean = X_mean
        self.X_std = X_std

        X = X.to(self.device)
        y = y.to(self.device)

        # Create model
        self._create_model(X.shape[1])

        # Create data loader
        dataset = TensorDataset(X, y)
        train_loader = DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True
        )

        # Validation data
        if X_val is not None and y_val is not None:
            if not isinstance(X_val, torch.Tensor):
                X_val = torch.from_numpy(X_val.values if hasattr(X_val, 'values') else X_val).float()
            if not isinstance(y_val, torch.Tensor):
                y_val = torch.from_numpy(y_val.values if hasattr(y_val, 'values') else y_val).float()
            X_val = X_val.to(self.device)
            y_val = y_val.to(self.device)

        # Training loop with advanced features
        best_val_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.epochs):
            # Training
            self.model.train()
            train_loss = 0.0

            for batch_X, batch_y in train_loader:
                self.optimizer_obj.zero_grad()

                if self.scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(batch_X)
                        loss = F.mse_loss(outputs, batch_y)

                        # Check for NaN loss
                        if torch.isnan(loss) or torch.isinf(loss):
                            logger.warning(f"NaN/inf loss detected: {loss.item()}, skipping batch")
                            continue

                    self.scaler.scale(loss).backward()

                    # Gradient clipping to prevent explosion
                    self.scaler.unscale_(self.optimizer_obj)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                    self.scaler.step(self.optimizer_obj)
                    self.scaler.update()
                else:
                    outputs = self.model(batch_X)
                    loss = F.mse_loss(outputs, batch_y)

                    # Check for NaN loss
                    if torch.isnan(loss) or torch.isinf(loss):
                        logger.warning(f"NaN/inf loss detected: {loss.item()}, skipping batch")
                        continue

                    loss.backward()

                    # Gradient clipping to prevent explosion
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                    self.optimizer_obj.step()

                train_loss += loss.item()

            train_loss /= len(train_loader)
            self.history['train_loss'].append(train_loss)

            # Validation
            if X_val is not None and y_val is not None:
                self.model.eval()
                with torch.no_grad():
                    val_outputs = self.model(X_val)
                    val_loss = F.mse_loss(val_outputs, y_val).item()
                    self.history['val_loss'].append(val_loss)

                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= self.early_stopping_patience:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break

                # Learning rate scheduling
                if self.lr_scheduler == 'plateau':
                    self.scheduler.step(val_loss)
                elif self.scheduler is not None:
                    self.scheduler.step()

                if (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, "
                               f"Train Loss: {train_loss:.6f}, "
                               f"Val Loss: {val_loss:.6f}")
            else:
                if self.scheduler is not None and self.lr_scheduler != 'plateau':
                    self.scheduler.step()

                if (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, "
                               f"Train Loss: {train_loss:.6f}")

        total_time = time.time() - start_time
        logger.info(f"{self.model_type} neural network training completed in {total_time:.2f}s")

        return self

    def predict(self, X):
        """Make predictions."""
        if self.model is None:
            raise ValueError("Model has not been trained yet")

        self.model.eval()

        # Convert to tensor and move to device
        if not isinstance(X, torch.Tensor):
            X = torch.from_numpy(X.values if hasattr(X, 'values') else X).float()

        # Apply same normalization as training
        if hasattr(self, 'X_mean') and hasattr(self, 'X_std'):
            X = (X - self.X_mean.cpu()) / self.X_std.cpu()

        X = X.to(self.device)

        with torch.no_grad():
            predictions = self.model(X)
            predictions = predictions.cpu().numpy().flatten()

            # Handle NaN values
            if np.isnan(predictions).any():
                logger.warning("NaN values detected in predictions, replacing with zeros")
                predictions = np.nan_to_num(predictions, nan=0.0)

        return predictions

    @property
    def feature_importances_(self):
        """Compute feature importances using gradient-based method."""
        if self.model is None:
            raise ValueError("Model has not been trained yet")

        # For advanced models, use integrated gradients or similar
        # This is a simplified version
        importances = []
        for param in self.model.parameters():
            if param.requires_grad and len(param.shape) == 2:
                importances.append(torch.norm(param, dim=0).detach().cpu().numpy())
                break

        if importances:
            return importances[0] / np.sum(importances[0])
        else:
            input_dim = getattr(self.model, 'input_dim', 199)  # Default to dataset feature count
            return np.ones(input_dim) / input_dim
