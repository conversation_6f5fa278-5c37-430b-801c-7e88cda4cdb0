@echo off
REM ============================================================================
REM FLKDDrug Platform - Comprehensive Windows Testing Script
REM ============================================================================
REM This script provides a complete testing suite for the FLKDDrug platform
REM Compatible with Windows 10/11 and PowerShell
REM ============================================================================

echo.
echo ============================================================================
echo  FLKDDrug Platform - Comprehensive Testing Suite
echo ============================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

echo [INFO] Python detected: 
python --version

REM Check if we're in the correct directory
if not exist "run_strategy.py" (
    echo ERROR: Please run this script from the FLKDDrug root directory
    echo Current directory should contain run_strategy.py
    pause
    exit /b 1
)

echo [INFO] Directory check passed
echo.

REM Create logs directory if it doesn't exist
if not exist "test_logs" mkdir test_logs

REM Set timestamp for log files
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

echo ============================================================================
echo  PHASE 1: SYSTEM VALIDATION
echo ============================================================================
echo.

echo [1/4] Testing system dependencies...
python test_system.py > test_logs\system_test_%timestamp%.log 2>&1
if errorlevel 1 (
    echo [ERROR] System test failed. Check test_logs\system_test_%timestamp%.log
    echo.
    echo Common issues:
    echo - Missing dependencies: pip install -r requirements.txt
    echo - CUDA not available: Install CUDA toolkit and PyTorch with CUDA
    echo - GPU drivers: Update NVIDIA drivers
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" exit /b 1
) else (
    echo [SUCCESS] System validation passed
)

echo.
echo [2/4] Testing data loading...
python -c "from utils.data_utils import load_data; X, y = load_data(); print(f'Data loaded: X={X.shape}, y={y.shape}')" 2>&1
if errorlevel 1 (
    echo [ERROR] Data loading failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Data loading test passed
)

echo.
echo [3/4] Testing GPU functionality...
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count() if torch.cuda.is_available() else 0}')" 2>&1
if errorlevel 1 (
    echo [WARNING] GPU test failed - will use CPU mode
) else (
    echo [SUCCESS] GPU functionality test passed
)

echo.
echo [4/4] Testing strategy imports...
python -c "from strategies.federated.fedavg import FedAvg; from strategies.distillation.vanilla_kd import VanillaKD; print('Strategy imports successful')" 2>&1
if errorlevel 1 (
    echo [ERROR] Strategy import failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Strategy imports test passed
)

echo.
echo ============================================================================
echo  PHASE 2: QUICK FUNCTIONALITY TESTS
echo ============================================================================
echo.

echo [1/3] Testing original baseline model (2 epochs)...
python run_strategy.py --original --epochs 2 --gpu 0 > test_logs\original_test_%timestamp%.log 2>&1
if errorlevel 1 (
    echo [ERROR] Original model test failed. Check test_logs\original_test_%timestamp%.log
    set /p continue="Continue with other tests? (y/N): "
    if /i not "%continue%"=="y" exit /b 1
) else (
    echo [SUCCESS] Original model test passed
)

echo.
echo [2/3] Testing federated learning (2 rounds, 2 clients)...
python run_strategy.py --fl_strategy fedavg --n_rounds 2 --n_clients 2 --gpu 0 > test_logs\fedavg_test_%timestamp%.log 2>&1
if errorlevel 1 (
    echo [ERROR] Federated learning test failed. Check test_logs\fedavg_test_%timestamp%.log
    set /p continue="Continue with other tests? (y/N): "
    if /i not "%continue%"=="y" exit /b 1
) else (
    echo [SUCCESS] Federated learning test passed
)

echo.
echo [3/3] Testing knowledge distillation (2 epochs)...
python run_strategy.py --kd_strategy vanilla_kd --epochs 2 --gpu 0 > test_logs\vanilla_kd_test_%timestamp%.log 2>&1
if errorlevel 1 (
    echo [ERROR] Knowledge distillation test failed. Check test_logs\vanilla_kd_test_%timestamp%.log
    set /p continue="Continue with comprehensive tests? (y/N): "
    if /i not "%continue%"=="y" exit /b 1
) else (
    echo [SUCCESS] Knowledge distillation test passed
)

echo.
echo ============================================================================
echo  PHASE 3: COMPREHENSIVE STRATEGY TESTING
echo ============================================================================
echo.

set /p run_comprehensive="Run comprehensive strategy testing? This may take 30-60 minutes. (y/N): "
if /i not "%run_comprehensive%"=="y" goto :performance_test

echo [INFO] Starting comprehensive strategy testing...
echo [INFO] This will test all available strategies with minimal parameters
echo [INFO] Progress will be logged to test_logs\comprehensive_test_%timestamp%.log
echo.

python test_strategies.py --timeout 600 > test_logs\comprehensive_test_%timestamp%.log 2>&1
if errorlevel 1 (
    echo [WARNING] Some strategies failed. Check test_logs\comprehensive_test_%timestamp%.log for details
) else (
    echo [SUCCESS] All strategies passed comprehensive testing
)

:performance_test
echo.
echo ============================================================================
echo  PHASE 4: PERFORMANCE MONITORING TEST
echo ============================================================================
echo.

set /p run_monitoring="Test performance monitoring? (y/N): "
if /i not "%run_monitoring%"=="y" goto :final_report

echo [INFO] Starting performance monitoring test (60 seconds)...
echo [INFO] This will monitor system resources during a short training session

REM Start performance monitor in background
start /b python monitor_performance.py --duration 60 --interval 5 --save-report

REM Run a quick training session
python run_strategy.py --original --epochs 1 --gpu 0 > test_logs\monitoring_test_%timestamp%.log 2>&1

echo [SUCCESS] Performance monitoring test completed
echo [INFO] Check for performance_report_*.json files for detailed metrics

:final_report
echo.
echo ============================================================================
echo  FINAL REPORT
echo ============================================================================
echo.

echo [INFO] Test Summary:
echo - System validation: Completed
echo - Quick functionality tests: Completed  
echo - Comprehensive testing: %run_comprehensive%
echo - Performance monitoring: %run_monitoring%
echo.

echo [INFO] Log files created in test_logs\ directory:
dir /b test_logs\*%timestamp%*

echo.
echo [INFO] Next steps:
echo 1. Review log files for any errors or warnings
echo 2. Run 'python run_all.py --help' to see all available options
echo 3. Start with: python run_all.py --gpu 0 --n_clients 3 --n_rounds 5
echo 4. Monitor performance: python monitor_performance.py --save-report --plot
echo.

echo ============================================================================
echo  TESTING COMPLETE
echo ============================================================================
echo.

echo [SUCCESS] FLKDDrug platform testing completed successfully!
echo [INFO] The platform is ready for use.
echo.
echo Useful commands:
echo - Quick test: python run_strategy.py --original --epochs 5 --gpu 0
echo - Full FL test: python run_strategy.py --fl_strategy fedavg --n_rounds 10 --gpu 0  
echo - Combined strategy: python run_all.py --strategies fedavg_vanilla_kd --gpu 0
echo - Monitor performance: python monitor_performance.py --save-report --plot
echo.

pause
