"""
Hierarchical Reinforcement Learning for multi-level federated learning optimization.
High-level policy decides strategy, low-level policy executes tactics.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical, Normal
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from sklearn.base import BaseEstimator, RegressorMixin
import copy
import time

logger = logging.getLogger(__name__)


class HighLevelPolicy(nn.Module):
    """High-level policy for strategic decisions in federated learning."""
    
    def __init__(self, state_dim: int, n_strategies: int, hidden_dims: List[int] = [256, 128]):
        super(HighLevelPolicy, self).__init__()
        
        self.state_dim = state_dim
        self.n_strategies = n_strategies
        
        # Policy network
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*layers)
        
        # Strategy selection head
        self.strategy_head = nn.Linear(prev_dim, n_strategies)
        
        # Value head
        self.value_head = nn.Linear(prev_dim, 1)
        
    def forward(self, state):
        """Forward pass."""
        shared = self.shared_layers(state)
        strategy_logits = self.strategy_head(shared)
        value = self.value_head(shared)
        
        return strategy_logits, value
    
    def get_strategy(self, state):
        """Select strategy."""
        strategy_logits, value = self.forward(state)
        strategy_probs = F.softmax(strategy_logits, dim=-1)
        strategy_dist = Categorical(strategy_probs)
        strategy = strategy_dist.sample()
        log_prob = strategy_dist.log_prob(strategy)
        
        return strategy, log_prob, value, strategy_dist


class LowLevelPolicy(nn.Module):
    """Low-level policy for tactical execution given a strategy."""
    
    def __init__(self, state_dim: int, strategy_dim: int, action_dim: int,
                 hidden_dims: List[int] = [128, 64]):
        super(LowLevelPolicy, self).__init__()
        
        self.state_dim = state_dim
        self.strategy_dim = strategy_dim
        self.action_dim = action_dim
        
        # Input includes state + strategy embedding
        input_dim = state_dim + strategy_dim
        
        # Policy network
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*layers)
        
        # Action heads
        self.client_selection_head = nn.Linear(prev_dim, action_dim)  # Client selection weights
        self.aggregation_head = nn.Linear(prev_dim, 3)               # Aggregation method
        self.learning_rate_head = nn.Linear(prev_dim, 1)             # Learning rate adjustment
        
        # Value head
        self.value_head = nn.Linear(prev_dim, 1)
        
        # Strategy embedding
        self.strategy_embedding = nn.Embedding(strategy_dim, strategy_dim)
        
    def forward(self, state, strategy):
        """Forward pass with state and strategy."""
        # Embed strategy
        strategy_emb = self.strategy_embedding(strategy)
        
        # Concatenate state and strategy
        combined_input = torch.cat([state, strategy_emb], dim=-1)
        
        # Forward pass
        shared = self.shared_layers(combined_input)
        
        # Action outputs
        client_weights = torch.softmax(self.client_selection_head(shared), dim=-1)
        aggregation_logits = self.aggregation_head(shared)
        learning_rate = torch.sigmoid(self.learning_rate_head(shared))
        
        # Value
        value = self.value_head(shared)
        
        return client_weights, aggregation_logits, learning_rate, value
    
    def get_action(self, state, strategy):
        """Get tactical actions given strategy."""
        client_weights, aggregation_logits, learning_rate, value = self.forward(state, strategy)
        
        # Sample aggregation method
        aggregation_probs = F.softmax(aggregation_logits, dim=-1)
        aggregation_dist = Categorical(aggregation_probs)
        aggregation_method = aggregation_dist.sample()
        aggregation_log_prob = aggregation_dist.log_prob(aggregation_method)
        
        return {
            'client_weights': client_weights,
            'aggregation_method': aggregation_method,
            'learning_rate': learning_rate,
            'aggregation_log_prob': aggregation_log_prob,
            'value': value
        }


class HierarchicalRLAgent:
    """Hierarchical RL agent for federated learning."""
    
    def __init__(self, state_dim: int, n_strategies: int, action_dim: int,
                 lr: float = 3e-4, gamma: float = 0.99,
                 strategy_update_freq: int = 5,
                 device: torch.device = None):
        """
        Initialize hierarchical RL agent.
        
        Args:
            state_dim: State dimension
            n_strategies: Number of high-level strategies
            action_dim: Action dimension for low-level policy
            lr: Learning rate
            gamma: Discount factor
            strategy_update_freq: How often to update strategy
            device: Device to use
        """
        self.state_dim = state_dim
        self.n_strategies = n_strategies
        self.action_dim = action_dim
        self.gamma = gamma
        self.strategy_update_freq = strategy_update_freq
        self.device = device if device is not None else torch.device('cpu')
        
        # High-level policy
        self.high_level_policy = HighLevelPolicy(state_dim, n_strategies).to(self.device)
        self.high_level_optimizer = optim.Adam(self.high_level_policy.parameters(), lr=lr)
        
        # Low-level policy
        self.low_level_policy = LowLevelPolicy(state_dim, n_strategies, action_dim).to(self.device)
        self.low_level_optimizer = optim.Adam(self.low_level_policy.parameters(), lr=lr)
        
        # Current strategy
        self.current_strategy = None
        self.strategy_step_count = 0
        
        # Experience storage
        self.high_level_experiences = []
        self.low_level_experiences = []
        
    def get_action(self, state):
        """Get hierarchical action."""
        state_tensor = torch.tensor(state, dtype=torch.float32, device=self.device).unsqueeze(0)
        
        # Update strategy if needed
        if (self.current_strategy is None or 
            self.strategy_step_count % self.strategy_update_freq == 0):
            
            strategy, strategy_log_prob, strategy_value, strategy_dist = \
                self.high_level_policy.get_strategy(state_tensor)
            
            self.current_strategy = strategy
            self.strategy_log_prob = strategy_log_prob
            self.strategy_value = strategy_value
            self.strategy_step_count = 0
        
        # Get low-level action
        low_level_action = self.low_level_policy.get_action(state_tensor, self.current_strategy)
        
        self.strategy_step_count += 1
        
        return {
            'strategy': self.current_strategy,
            'low_level_action': low_level_action,
            'strategy_log_prob': self.strategy_log_prob,
            'strategy_value': self.strategy_value
        }
    
    def store_experience(self, state, action, reward, next_state, done):
        """Store experience for both levels."""
        # Store low-level experience
        self.low_level_experiences.append({
            'state': state,
            'action': action['low_level_action'],
            'reward': reward,
            'next_state': next_state,
            'done': done
        })
        
        # Store high-level experience (less frequent)
        if self.strategy_step_count == 1 or done:  # Start of strategy or episode end
            self.high_level_experiences.append({
                'state': state,
                'strategy': action['strategy'],
                'strategy_log_prob': action['strategy_log_prob'],
                'strategy_value': action['strategy_value'],
                'reward': reward,
                'next_state': next_state,
                'done': done
            })
    
    def update(self):
        """Update both high-level and low-level policies."""
        # Update low-level policy
        if len(self.low_level_experiences) >= 32:
            self._update_low_level()
        
        # Update high-level policy
        if len(self.high_level_experiences) >= 16:
            self._update_high_level()
    
    def _update_low_level(self):
        """Update low-level policy using recent experiences."""
        experiences = self.low_level_experiences[-32:]  # Use last 32 experiences
        
        states = torch.stack([torch.tensor(exp['state'], dtype=torch.float32) 
                             for exp in experiences]).to(self.device)
        rewards = torch.tensor([exp['reward'] for exp in experiences], 
                              dtype=torch.float32, device=self.device)
        
        # Compute returns
        returns = []
        G = 0
        for reward in reversed(rewards):
            G = reward + self.gamma * G
            returns.insert(0, G)
        returns = torch.tensor(returns, dtype=torch.float32, device=self.device)
        
        # Compute advantages and update
        total_loss = 0
        for i, exp in enumerate(experiences):
            state = torch.tensor(exp['state'], dtype=torch.float32, device=self.device).unsqueeze(0)
            action = exp['action']
            
            # Get current policy output
            _, _, _, value = self.low_level_policy.forward(state, self.current_strategy)
            
            # Compute advantage
            advantage = returns[i] - value.squeeze()
            
            # Policy loss (simplified)
            policy_loss = -action['aggregation_log_prob'] * advantage.detach()
            
            # Value loss
            value_loss = F.mse_loss(value.squeeze(), returns[i])
            
            total_loss += policy_loss + 0.5 * value_loss
        
        # Update
        self.low_level_optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.low_level_policy.parameters(), 0.5)
        self.low_level_optimizer.step()
        
        # Clear old experiences
        self.low_level_experiences = self.low_level_experiences[-16:]
    
    def _update_high_level(self):
        """Update high-level policy using strategy experiences."""
        experiences = self.high_level_experiences[-16:]  # Use last 16 experiences
        
        rewards = torch.tensor([exp['reward'] for exp in experiences], 
                              dtype=torch.float32, device=self.device)
        
        # Compute returns
        returns = []
        G = 0
        for reward in reversed(rewards):
            G = reward + self.gamma * G
            returns.insert(0, G)
        returns = torch.tensor(returns, dtype=torch.float32, device=self.device)
        
        # Update high-level policy
        total_loss = 0
        for i, exp in enumerate(experiences):
            # Compute advantage
            advantage = returns[i] - exp['strategy_value'].squeeze()
            
            # Policy loss
            policy_loss = -exp['strategy_log_prob'] * advantage.detach()
            
            # Value loss
            value_loss = F.mse_loss(exp['strategy_value'].squeeze(), returns[i])
            
            total_loss += policy_loss + 0.5 * value_loss
        
        # Update
        self.high_level_optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.high_level_policy.parameters(), 0.5)
        self.high_level_optimizer.step()
        
        # Clear old experiences
        self.high_level_experiences = self.high_level_experiences[-8:]


class HierarchicalFederatedLearning(BaseEstimator, RegressorMixin):
    """Hierarchical RL for federated learning optimization."""
    
    def __init__(self, base_model, n_clients: int = 5, client_data: List = None,
                 n_rounds: int = 30, local_epochs: int = 1,
                 n_strategies: int = 4, device: torch.device = None, 
                 random_state: int = 42):
        """
        Initialize hierarchical federated learning.
        
        Args:
            base_model: Base model for clients
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of federated rounds
            local_epochs: Local training epochs per round
            n_strategies: Number of high-level strategies
            device: Device to use
            random_state: Random seed
        """
        self.base_model = base_model
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.n_strategies = n_strategies
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        # Hierarchical RL agent
        self.state_dim = 12  # State features
        self.action_dim = n_clients  # Action dimension
        
        self.hrl_agent = HierarchicalRLAgent(
            state_dim=self.state_dim,
            n_strategies=n_strategies,
            action_dim=self.action_dim,
            device=self.device
        )
        
        # Strategy definitions
        self.strategy_names = [
            "Conservative",    # Focus on stable clients
            "Aggressive",      # Include all available clients
            "Selective",       # Choose best performing clients
            "Balanced"         # Balance performance and diversity
        ]
        
        # Models
        self.client_models = [copy.deepcopy(base_model) for _ in range(n_clients)]
        self.global_model = copy.deepcopy(base_model)
        
        # History
        self.history = {
            'strategies': [],
            'rewards': [],
            'global_performance': [],
            'client_participation': []
        }
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
    
    def _get_state(self, round_num: int, client_performances: List[float],
                   participation_history: List[List[int]]):
        """Get state representation."""
        # Pad client performances
        while len(client_performances) < self.n_clients:
            client_performances.append(0.0)
        
        # Compute participation rates
        if participation_history:
            participation_rates = [np.mean([round_part[i] for round_part in participation_history[-5:]])
                                 for i in range(self.n_clients)]
        else:
            participation_rates = [0.0] * self.n_clients
        
        state = np.array([
            round_num / self.n_rounds,  # Progress
            np.mean(client_performances[:self.n_clients]),  # Average performance
            np.std(client_performances[:self.n_clients]),   # Performance variance
            np.max(client_performances[:self.n_clients]),   # Best performance
            np.min(client_performances[:self.n_clients]),   # Worst performance
            np.mean(participation_rates),                   # Average participation
            len([p for p in client_performances[:self.n_clients] if p > 0]) / self.n_clients,  # Active ratio
            np.median(client_performances[:self.n_clients]),  # Median performance
            np.percentile(client_performances[:self.n_clients], 25),  # 25th percentile
            np.percentile(client_performances[:self.n_clients], 75),  # 75th percentile
            np.var(participation_rates),                    # Participation variance
            round_num % 5 / 5                              # Cycle feature
        ])
        
        return state
    
    def fit(self, X_val: np.ndarray, y_val: np.ndarray):
        """Train hierarchical federated learning system."""
        logger.info(f"Starting hierarchical RL federated learning for {self.n_rounds} rounds")
        
        client_performances = [0.0] * self.n_clients
        participation_history = []
        
        for round_num in range(self.n_rounds):
            round_start_time = time.time()
            
            # Get state
            state = self._get_state(round_num, client_performances, participation_history)
            
            # Get hierarchical action
            action_dict = self.hrl_agent.get_action(state)
            strategy = action_dict['strategy'].item()
            low_level_action = action_dict['low_level_action']
            
            # Execute federated learning round based on hierarchical decision
            client_weights = low_level_action['client_weights'].squeeze().cpu().numpy()
            aggregation_method = low_level_action['aggregation_method'].item()
            learning_rate_mult = low_level_action['learning_rate'].item()
            
            # Select clients based on strategy and weights
            if strategy == 0:  # Conservative
                # Select most stable clients
                stability_scores = [1.0 - np.std(participation_history[-3:]) if len(participation_history) >= 3 
                                  else 0.5 for _ in range(self.n_clients)]
                selected_clients = np.argsort(stability_scores)[-3:]
            elif strategy == 1:  # Aggressive
                # Include all available clients
                selected_clients = list(range(min(self.n_clients, len(self.client_data) if self.client_data else self.n_clients)))
            elif strategy == 2:  # Selective
                # Choose best performing clients
                selected_clients = np.argsort(client_performances)[-3:]
            else:  # Balanced
                # Use client weights for selection
                selected_clients = np.random.choice(
                    self.n_clients, 
                    size=min(3, self.n_clients), 
                    replace=False,
                    p=client_weights / client_weights.sum()
                )
            
            # Train selected clients
            round_performances = []
            participation = [0] * self.n_clients
            
            for client_idx in selected_clients:
                participation[client_idx] = 1
                
                if self.client_data and client_idx < len(self.client_data):
                    try:
                        X_client, y_client = self.client_data[client_idx]
                        
                        # Adjust learning rate
                        if hasattr(self.client_models[client_idx], 'learning_rate'):
                            self.client_models[client_idx].learning_rate *= learning_rate_mult
                        
                        # Train client
                        self.client_models[client_idx].fit(X_client, y_client)
                        
                        # Evaluate
                        pred = self.client_models[client_idx].predict(X_val)
                        performance = -np.mean(np.abs(y_val - pred))
                        round_performances.append(performance)
                        client_performances[client_idx] = performance
                        
                    except Exception as e:
                        logger.warning(f"Error training client {client_idx}: {e}")
                        round_performances.append(0.0)
                        client_performances[client_idx] = 0.0
            
            # Aggregate models
            if round_performances:
                # Different aggregation methods
                if aggregation_method == 0:  # Simple average
                    weights = [1.0] * len(selected_clients)
                elif aggregation_method == 1:  # Performance weighted
                    weights = [max(0, p) for p in round_performances]
                else:  # Inverse variance weighted
                    variances = [np.var(round_performances) + 1e-6] * len(selected_clients)
                    weights = [1.0 / v for v in variances]
                
                # Normalize weights
                if sum(weights) > 0:
                    weights = [w / sum(weights) for w in weights]
                
                # Aggregate (simplified for neural networks)
                if hasattr(self.global_model, 'model') and hasattr(self.global_model.model, 'state_dict'):
                    global_state_dict = {}
                    
                    # Initialize
                    for key in self.client_models[selected_clients[0]].model.state_dict().keys():
                        global_state_dict[key] = torch.zeros_like(
                            self.client_models[selected_clients[0]].model.state_dict()[key]
                        )
                    
                    # Weighted average
                    for i, client_idx in enumerate(selected_clients):
                        client_state_dict = self.client_models[client_idx].model.state_dict()
                        for key in global_state_dict.keys():
                            global_state_dict[key] += weights[i] * client_state_dict[key]
                    
                    self.global_model.model.load_state_dict(global_state_dict)
                
                # Evaluate global model
                try:
                    global_pred = self.global_model.predict(X_val)
                    global_performance = -np.mean(np.abs(y_val - global_pred))
                except:
                    global_performance = np.mean(round_performances)
            else:
                global_performance = -1.0
            
            # Compute reward
            reward = global_performance + 0.1 * len(selected_clients) / self.n_clients  # Efficiency bonus
            
            # Store experience
            next_state = self._get_state(round_num + 1, client_performances, participation_history + [participation])
            done = (round_num == self.n_rounds - 1)
            
            self.hrl_agent.store_experience(state, action_dict, reward, next_state, done)
            
            # Update agent
            self.hrl_agent.update()
            
            # Record history
            participation_history.append(participation)
            self.history['strategies'].append(strategy)
            self.history['rewards'].append(reward)
            self.history['global_performance'].append(global_performance)
            self.history['client_participation'].append(participation.copy())
            
            round_time = time.time() - round_start_time
            
            if (round_num + 1) % 5 == 0:
                strategy_name = self.strategy_names[strategy]
                logger.info(f"Round {round_num + 1}/{self.n_rounds}, "
                           f"Strategy: {strategy_name}, "
                           f"Global Performance: {global_performance:.6f}, "
                           f"Reward: {reward:.6f}, "
                           f"Clients: {len(selected_clients)}, "
                           f"Time: {round_time:.2f}s")
        
        logger.info("Hierarchical RL federated learning completed")
        return self
    
    def predict(self, X):
        """Make predictions using global model."""
        return self.global_model.predict(X)
    
    @property
    def feature_importances_(self):
        """Get feature importances."""
        if hasattr(self.global_model, 'feature_importances_'):
            return self.global_model.feature_importances_
        else:
            return np.ones(10) / 10  # Placeholder
