# 🧪 **FLKDDrug Testing Guide**

## 📋 **Quick Testing Overview**

This guide provides comprehensive testing instructions for the FLKDDrug platform on Windows systems.

## 🚀 **Quick Start Testing**

### **Option 1: Automated Windows Testing (Recommended)**

```batch
# Run the comprehensive Windows batch script
test_all_windows.bat

# Or use PowerShell for advanced features
PowerShell -ExecutionPolicy Bypass -File Test-FLKDDrug.ps1
```

### **Option 2: Manual Step-by-Step Testing**

```bash
# 1. System validation
python test_system.py

# 2. Quick functionality tests
python run_strategy.py --original --epochs 2 --gpu 0
python run_strategy.py --fl_strategy fedavg --n_rounds 2 --n_clients 2 --gpu 0
python run_strategy.py --kd_strategy vanilla_kd --epochs 2 --gpu 0

# 3. Comprehensive strategy testing
python test_strategies.py --timeout 300

# 4. Performance monitoring
python monitor_performance.py --duration 60 --save-report
```

## 📊 **Testing Scripts Overview**

### **1. test_system.py**
- **Purpose**: Validates system dependencies and configuration
- **Tests**: Python packages, GPU availability, data loading, strategy imports
- **Runtime**: 1-2 minutes
- **Usage**: `python test_system.py`

### **2. test_strategies.py**
- **Purpose**: Tests all available strategies with minimal parameters
- **Tests**: All FL, KD, contrastive, and transfer learning strategies
- **Runtime**: 30-60 minutes (full), 5-15 minutes (quick)
- **Usage**: `python test_strategies.py [--category <category>] [--timeout <seconds>]`

### **3. monitor_performance.py**
- **Purpose**: Real-time system performance monitoring
- **Monitors**: CPU, RAM, GPU usage, VRAM usage
- **Runtime**: Configurable (default: continuous)
- **Usage**: `python monitor_performance.py [--duration <seconds>] [--interval <seconds>]`

### **4. test_all_windows.bat**
- **Purpose**: Comprehensive Windows batch testing script
- **Features**: Automated testing, error handling, log generation
- **Runtime**: 10-90 minutes (depending on options)
- **Usage**: Double-click or run from command prompt

### **5. Test-FLKDDrug.ps1**
- **Purpose**: Advanced PowerShell testing script
- **Features**: Colored output, parallel execution, detailed reporting
- **Runtime**: 10-90 minutes (depending on options)
- **Usage**: `PowerShell -ExecutionPolicy Bypass -File Test-FLKDDrug.ps1`

## 🎯 **Testing Scenarios**

### **Scenario 1: First-Time Setup Validation**
```bash
# Validate complete system setup
python test_system.py

# Quick functionality check
python run_strategy.py --original --epochs 2 --gpu 0
```

### **Scenario 2: Strategy Development Testing**
```bash
# Test specific strategy category
python test_strategies.py --category federated_learning

# Test individual strategy
python run_strategy.py --fl_strategy fedavg --n_rounds 3 --n_clients 2 --gpu 0
```

### **Scenario 3: Performance Benchmarking**
```bash
# Start monitoring
python monitor_performance.py --save-report --plot &

# Run benchmark
python run_all.py --strategies "original,fedavg,vanilla_kd" --gpu 0

# Analyze results
# Check performance_report_*.json and *.png files
```

### **Scenario 4: Continuous Integration Testing**
```bash
# Quick validation (5-10 minutes)
python test_strategies.py --timeout 300

# Or use automated script
test_all_windows.bat
```

## 🔧 **Troubleshooting Testing Issues**

### **Common Test Failures**

**1. Import Errors**
```bash
# Solution: Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Verify imports
python -c "from strategies.federated.fedavg import FedAvg; print('Success')"
```

**2. GPU Not Available**
```bash
# Check CUDA installation
python -c "import torch; print(torch.cuda.is_available())"

# Install CUDA-enabled PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Run tests without GPU
python test_strategies.py --timeout 300  # Will automatically use CPU
```

**3. Memory Issues**
```bash
# Reduce batch size and model complexity
python run_strategy.py --original --epochs 1 --gpu 0

# Monitor memory usage
python monitor_performance.py --duration 60
```

**4. Data Loading Errors**
```bash
# Verify data files exist
python -c "import os; print('logS.csv exists:', os.path.exists('logS.csv'))"

# Test data loading
python -c "from utils.data_utils import load_data; X, y = load_data(); print(f'Loaded: {X.shape}, {y.shape}')"
```

### **Windows-Specific Issues**

**PowerShell Execution Policy**
```powershell
# Enable script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Run with bypass
PowerShell -ExecutionPolicy Bypass -File Test-FLKDDrug.ps1
```

**Long Path Issues**
```powershell
# Enable long paths (requires admin)
New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled" -Value 1 -PropertyType DWORD -Force
```

**Antivirus Interference**
- Add FLKDDrug directory to antivirus exclusions
- Temporarily disable real-time protection during testing

## 📈 **Expected Test Results**

### **System Validation**
- ✅ All dependencies installed
- ✅ GPU detected (if available)
- ✅ Data loading successful
- ✅ Strategy imports working

### **Quick Functionality Tests**
- ✅ Original model: Test MAE < 0.3, R² > 0.85
- ✅ FedAvg: Test MAE < 0.35, R² > 0.80
- ✅ Vanilla KD: Test MAE < 0.32, R² > 0.83

### **Performance Benchmarks**
- **Training Time**: 2-10 minutes per strategy (depending on GPU)
- **Memory Usage**: 4-12 GB GPU memory (depending on model)
- **CPU Usage**: 50-90% during training
- **Success Rate**: >95% for all strategies

## 📊 **Test Reports and Logs**

### **Generated Files**
- `test_logs/system_test_*.log`: System validation results
- `test_logs/strategy_test_*.log`: Individual strategy test results
- `test_logs/comprehensive_test_*.log`: Full strategy testing results
- `performance_report_*.json`: Performance monitoring data
- `performance_plot_*.png`: Performance visualization charts
- `strategy_test_results_*.json`: Detailed strategy test results
- `strategy_test_summary_*.csv`: Summary table of all tests

### **Log Analysis**
```bash
# View recent test results
type test_logs\*latest*.log

# Check performance metrics
python -c "
import json
with open('performance_report_latest.json') as f:
    data = json.load(f)
    print(f'Avg CPU: {data[\"summary\"][\"avg_cpu\"]:.1f}%')
    print(f'Max GPU: {data[\"summary\"][\"max_gpu\"]:.1f}%')
"
```

## 🎯 **Testing Best Practices**

### **Before Testing**
1. Ensure stable internet connection
2. Close unnecessary applications
3. Check available disk space (>5GB)
4. Update GPU drivers if using GPU acceleration

### **During Testing**
1. Monitor system resources
2. Don't interrupt long-running tests
3. Check logs for warnings/errors
4. Note any performance anomalies

### **After Testing**
1. Review all log files
2. Analyze performance reports
3. Document any issues found
4. Clean up temporary files if needed

## 🚀 **Next Steps After Testing**

### **If All Tests Pass**
```bash
# Start with full experiments
python run_all.py --gpu 0 --n_clients 5 --n_rounds 10

# Try combined strategies
python run_all.py --strategies "fedavg_vanilla_kd,fedprox_ensemble_kd" --gpu 0

# Monitor performance
python monitor_performance.py --save-report --plot &
```

### **If Some Tests Fail**
1. Check specific error messages in log files
2. Verify system requirements are met
3. Try running individual components
4. Consult troubleshooting section
5. Report issues with detailed logs

---

**📧 Support**: Check README.md for contact information and troubleshooting guides.
