"""
Implementation of Personalized Federated Learning.

This module implements personalized federated learning where each client
maintains a personalized model that combines global and local knowledge.
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time
from strategies.federated.fedavg import EnsembleModel

logger = logging.getLogger(__name__)

class PersonalizedFL:
    """
    Personalized Federated Learning for tree-based models.
    
    This implementation creates personalized models for each client by
    combining global and local knowledge.
    """
    
    def __init__(self, base_model=None, n_clients=3, client_data=None, 
                 n_rounds=10, local_epochs=1, client_sample_ratio=1.0,
                 personalization_alpha=0.5, random_state=42):
        """
        Initialize PersonalizedFL.
        
        Args:
            base_model: Base model to use for clients (default: LGBMRegressor)
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of communication rounds
            local_epochs: Number of local epochs per round
            client_sample_ratio: Ratio of clients to sample in each round
            personalization_alpha: Weight of global model (0-1)
            random_state: Random seed
        """
        self.base_model = base_model if base_model is not None else LGBMRegressor(random_state=random_state)
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.client_sample_ratio = client_sample_ratio
        self.personalization_alpha = personalization_alpha
        self.random_state = random_state
        
        self.client_models = None
        self.global_model = None
        self.personalized_models = None
        self.history = {
            'client_losses': [],
            'global_loss': [],
            'personalized_losses': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_val=None, y_val=None):
        """
        Train the federated model.
        
        Args:
            X_val: Validation features (for tracking progress)
            y_val: Validation targets (for tracking progress)
            
        Returns:
            self: Trained model
        """
        if self.client_data is None or len(self.client_data) == 0:
            raise ValueError("No client data provided")
        
        start_time = time.time()
        logger.info(f"Starting PersonalizedFL training with {self.n_clients} clients, "
                   f"{self.n_rounds} rounds, and alpha={self.personalization_alpha}")
        
        # Initialize client models
        self.client_models = [clone(self.base_model) for _ in range(self.n_clients)]
        
        # Training loop
        for round_idx in range(self.n_rounds):
            round_start = time.time()
            logger.info(f"Round {round_idx+1}/{self.n_rounds}")
            
            # Sample clients
            n_sampled = max(1, int(self.client_sample_ratio * self.n_clients))
            sampled_clients = np.random.choice(self.n_clients, n_sampled, replace=False)
            
            # Train client models
            client_losses = []
            for client_idx in sampled_clients:
                X_client, y_client = self.client_data[client_idx]
                
                # Train for multiple local epochs
                for _ in range(self.local_epochs):
                    self.client_models[client_idx].fit(X_client, y_client)
                
                # Evaluate client model
                if X_val is not None and y_val is not None:
                    y_pred = self.client_models[client_idx].predict(X_val)
                    loss = np.mean(np.abs(y_val - y_pred))
                    client_losses.append(loss)
                    logger.debug(f"Client {client_idx} loss: {loss:.4f}")
            
            # Update global model
            self.global_model = self._create_ensemble_model()
            
            # Create personalized models
            self.personalized_models = self._create_personalized_models()
            
            # Evaluate models
            if X_val is not None and y_val is not None:
                # Evaluate global model
                y_pred_global = self.global_model.predict(X_val)
                global_loss = np.mean(np.abs(y_val - y_pred_global))
                
                # Evaluate personalized models
                personalized_losses = []
                for client_idx in range(self.n_clients):
                    y_pred_pers = self.personalized_models[client_idx].predict(X_val)
                    pers_loss = np.mean(np.abs(y_val - y_pred_pers))
                    personalized_losses.append(pers_loss)
                
                self.history['client_losses'].append(client_losses)
                self.history['global_loss'].append(global_loss)
                self.history['personalized_losses'].append(personalized_losses)
                
                round_time = time.time() - round_start
                logger.info(f"Round {round_idx+1} completed in {round_time:.2f}s. "
                           f"Global loss: {global_loss:.4f}, "
                           f"Avg client loss: {np.mean(client_losses):.4f}, "
                           f"Avg personalized loss: {np.mean(personalized_losses):.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"PersonalizedFL training completed in {total_time:.2f}s")
        
        return self
    
    def _create_ensemble_model(self):
        """
        Create an ensemble model from client models.
        
        Returns:
            EnsembleModel: Ensemble of client models
        """
        return EnsembleModel(self.client_models)
    
    def _create_personalized_models(self):
        """
        Create personalized models by combining global and local models.
        
        Returns:
            list: List of personalized models
        """
        personalized_models = []
        
        for client_idx in range(self.n_clients):
            # For tree-based models, we create a weighted ensemble
            personalized_model = WeightedEnsembleModel(
                [self.global_model, self.client_models[client_idx]],
                [self.personalization_alpha, 1 - self.personalization_alpha]
            )
            personalized_models.append(personalized_model)
        
        return personalized_models
    
    def predict(self, X, client_idx=None):
        """
        Make predictions using the global or personalized model.
        
        Args:
            X: Features
            client_idx: Client index for personalized prediction (None for global)
            
        Returns:
            array: Predictions
        """
        if self.global_model is None:
            raise ValueError("Model has not been trained yet")
        
        if client_idx is not None:
            if self.personalized_models is None or client_idx >= len(self.personalized_models):
                raise ValueError(f"Invalid client index: {client_idx}")
            return self.personalized_models[client_idx].predict(X)
        else:
            return self.global_model.predict(X)

class WeightedEnsembleModel:
    """
    Weighted ensemble model that combines predictions from multiple models.
    """
    
    def __init__(self, models, weights=None):
        """
        Initialize weighted ensemble model.
        
        Args:
            models: List of models
            weights: List of weights for each model
        """
        self.models = models
        
        if weights is None:
            self.weights = np.ones(len(models)) / len(models)
        else:
            self.weights = np.array(weights) / np.sum(weights)
    
    def predict(self, X):
        """
        Make predictions by weighted averaging of predictions from all models.
        
        Args:
            X: Features
            
        Returns:
            array: Weighted averaged predictions
        """
        predictions = np.array([model.predict(X) for model in self.models])
        return np.sum(predictions * self.weights[:, np.newaxis], axis=0)
    
    def __getattr__(self, name):
        """
        Forward attribute access to the first model.
        
        This allows accessing attributes like feature_importances_.
        
        Args:
            name: Attribute name
            
        Returns:
            Attribute value
        """
        if name == 'feature_importances_' and hasattr(self.models[0], 'feature_importances_'):
            # Weighted average of feature importances
            importances = np.array([model.feature_importances_ for model in self.models 
                                   if hasattr(model, 'feature_importances_')])
            return np.average(importances, axis=0, weights=self.weights[:len(importances)])
        
        # Forward to first model
        return getattr(self.models[0], name)
