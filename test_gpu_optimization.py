#!/usr/bin/env python3
"""
Test script to verify GPU optimization functionality.
"""

import os
import sys
import time
import torch
import numpy as np
import pandas as pd
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import project modules
import config
from utils.gpu_utils import GPUManager
from utils.neural_models import NeuralNetRegressor
from utils.model_utils import create_model
from utils.data_utils import load_data, preprocess_data_gpu

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gpu_detection():
    """Test GPU detection and setup."""
    logger.info("Testing GPU detection...")
    
    gpu_manager = GPUManager(config.GPU_CONFIG)
    
    logger.info(f"Device: {gpu_manager.device}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    
    if gpu_manager.device.type == 'cuda':
        logger.info(f"GPU Name: {torch.cuda.get_device_name(gpu_manager.device)}")
        memory_info = gpu_manager.get_memory_info()
        logger.info(f"GPU Memory: {memory_info}")
        gpu_manager.log_memory_usage()
    
    return gpu_manager

def test_data_loading_gpu(gpu_manager):
    """Test GPU-accelerated data loading."""
    logger.info("Testing GPU data loading...")
    
    try:
        # Load data
        X, y = load_data(
            primary_file=config.DATA_CONFIG['primary_file'],
            target_col='value'
        )
        
        logger.info(f"Data shape: X={X.shape}, y={y.shape}")
        
        # Test GPU preprocessing
        if gpu_manager.device.type == 'cuda':
            # Split data for testing
            n_samples = len(X)
            train_idx = int(0.6 * n_samples)
            val_idx = int(0.8 * n_samples)
            
            X_train = X[:train_idx]
            X_val = X[train_idx:val_idx]
            X_test = X[val_idx:]
            y_train = y[:train_idx]
            y_val = y[train_idx:val_idx]
            y_test = y[val_idx:]
            
            start_time = time.time()
            X_train_gpu, X_val_gpu, X_test_gpu, y_train_gpu, y_val_gpu, y_test_gpu = preprocess_data_gpu(
                X_train, X_val, X_test, y_train, y_val, y_test,
                device=gpu_manager.device,
                scale=True
            )
            gpu_time = time.time() - start_time
            
            logger.info(f"GPU preprocessing time: {gpu_time:.4f}s")
            logger.info(f"GPU tensors shape: X_train={X_train_gpu.shape}, y_train={y_train_gpu.shape}")
            
            gpu_manager.log_memory_usage()
            
            return X_train, X_val, X_test, y_train, y_val, y_test
        else:
            logger.info("GPU not available, skipping GPU preprocessing test")
            return None, None, None, None, None, None
            
    except Exception as e:
        logger.error(f"Error in data loading test: {e}")
        return None, None, None, None, None, None

def test_neural_network_gpu(gpu_manager, X_train, y_train, X_val, y_val):
    """Test GPU-accelerated neural network training."""
    logger.info("Testing neural network GPU training...")
    
    if X_train is None:
        logger.info("No data available, skipping neural network test")
        return None
    
    try:
        # Create neural network model
        model_params = config.MODEL_CONFIG.get('neural_net_teacher', {})
        model_params.update({
            'epochs': 10,  # Reduced for testing
            'batch_size': 32,
            'early_stopping_patience': 5
        })
        
        model = create_model(
            model_type='neural_net',
            params=model_params,
            device=gpu_manager.device,
            gpu_manager=gpu_manager
        )
        
        logger.info(f"Model device: {model.device}")
        
        # Train model
        start_time = time.time()
        model.fit(X_train, y_train, X_val=X_val, y_val=y_val)
        training_time = time.time() - start_time
        
        logger.info(f"Training time: {training_time:.4f}s")
        
        # Test prediction
        start_time = time.time()
        predictions = model.predict(X_val)
        inference_time = time.time() - start_time
        
        logger.info(f"Inference time: {inference_time:.4f}s")
        logger.info(f"Predictions shape: {predictions.shape}")
        
        # Calculate basic metrics
        mse = np.mean((y_val.values - predictions) ** 2)
        mae = np.mean(np.abs(y_val.values - predictions))
        
        logger.info(f"Test MSE: {mse:.6f}")
        logger.info(f"Test MAE: {mae:.6f}")
        
        gpu_manager.log_memory_usage()
        
        return model
        
    except Exception as e:
        logger.error(f"Error in neural network test: {e}")
        return None

def test_lightgbm_gpu(gpu_manager, X_train, y_train, X_val, y_val):
    """Test LightGBM with GPU acceleration."""
    logger.info("Testing LightGBM GPU training...")
    
    if X_train is None:
        logger.info("No data available, skipping LightGBM test")
        return None
    
    try:
        # Create LightGBM model with GPU support
        model_params = config.MODEL_CONFIG.get('lgbm_teacher', {})
        model_params.update({
            'n_estimators': 100,  # Reduced for testing
            'verbose': -1
        })
        
        model = create_model(
            model_type='lgbm',
            params=model_params,
            device=gpu_manager.device,
            gpu_manager=gpu_manager
        )
        
        # Train model
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        logger.info(f"LightGBM training time: {training_time:.4f}s")
        
        # Test prediction
        start_time = time.time()
        predictions = model.predict(X_val)
        inference_time = time.time() - start_time
        
        logger.info(f"LightGBM inference time: {inference_time:.4f}s")
        
        # Calculate basic metrics
        mse = np.mean((y_val.values - predictions) ** 2)
        mae = np.mean(np.abs(y_val.values - predictions))
        
        logger.info(f"LightGBM Test MSE: {mse:.6f}")
        logger.info(f"LightGBM Test MAE: {mae:.6f}")
        
        return model
        
    except Exception as e:
        logger.error(f"Error in LightGBM test: {e}")
        return None

def test_memory_management(gpu_manager):
    """Test GPU memory management."""
    logger.info("Testing GPU memory management...")
    
    if gpu_manager.device.type != 'cuda':
        logger.info("GPU not available, skipping memory management test")
        return
    
    try:
        # Create large tensors to test memory management
        logger.info("Creating large tensors...")
        
        tensors = []
        for i in range(5):
            tensor = torch.randn(1000, 1000, device=gpu_manager.device)
            tensors.append(tensor)
            gpu_manager.log_memory_usage()
        
        # Clear cache
        logger.info("Clearing GPU cache...")
        gpu_manager.clear_cache()
        gpu_manager.log_memory_usage()
        
        # Delete tensors
        del tensors
        gpu_manager.clear_cache()
        gpu_manager.log_memory_usage()
        
        logger.info("Memory management test completed")
        
    except Exception as e:
        logger.error(f"Error in memory management test: {e}")

def main():
    """Run all GPU optimization tests."""
    logger.info("Starting GPU optimization tests...")
    
    # Test 1: GPU Detection
    gpu_manager = test_gpu_detection()
    
    # Test 2: Data Loading
    X_train, X_val, X_test, y_train, y_val, y_test = test_data_loading_gpu(gpu_manager)
    
    # Test 3: Neural Network Training
    if X_train is not None:
        nn_model = test_neural_network_gpu(gpu_manager, X_train, y_train, X_val, y_val)
        
        # Test 4: LightGBM Training
        lgbm_model = test_lightgbm_gpu(gpu_manager, X_train, y_train, X_val, y_val)
    
    # Test 5: Memory Management
    test_memory_management(gpu_manager)
    
    logger.info("All GPU optimization tests completed!")
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("GPU OPTIMIZATION TEST SUMMARY")
    logger.info("="*50)
    logger.info(f"Device: {gpu_manager.device}")
    logger.info(f"CUDA Available: {torch.cuda.is_available()}")
    
    if gpu_manager.device.type == 'cuda':
        memory_info = gpu_manager.get_memory_info()
        logger.info(f"GPU Memory Total: {memory_info['total_gb']:.2f} GB")
        logger.info(f"GPU Memory Used: {memory_info['allocated_gb']:.2f} GB")
        logger.info(f"GPU Utilization: {memory_info['utilization']:.1f}%")
    
    logger.info("GPU optimization is working correctly!")

if __name__ == "__main__":
    main()
