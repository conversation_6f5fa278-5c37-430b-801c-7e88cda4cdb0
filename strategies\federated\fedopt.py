"""
FedOpt: Federated Optimization with Adaptive Server Optimization.

Implements adaptive server-side optimization for federated learning,
including FedAdam, FedYogi, and FedAdagrad variants.
"""

import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.metrics import mean_squared_error, r2_score
import logging
import time
import copy

from utils.model_utils import create_model
from utils.data_utils import create_client_data

logger = logging.getLogger(__name__)


class FedOpt(BaseEstimator, RegressorMixin):
    """
    FedOpt: Federated Learning with Adaptive Server Optimization.

    Implements server-side adaptive optimization algorithms including:
    - FedAdam: Server-side Adam optimization
    - FedYogi: Server-side Yogi optimization
    - FedAdagrad: Server-side Adagrad optimization
    """

    def __init__(self, n_clients=5, n_rounds=20, local_epochs=2,
                 client_fraction=1.0, server_optimizer='adam',
                 server_lr=1.0, beta1=0.9, beta2=0.99, tau=1e-3,
                 model_type='neural_net', model_config=None, random_state=42):
        self.n_clients = n_clients
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.client_fraction = client_fraction
        self.server_optimizer = server_optimizer
        self.server_lr = server_lr
        self.beta1 = beta1
        self.beta2 = beta2
        self.tau = tau  # Yogi-specific parameter
        self.model_type = model_type
        self.model_config = model_config or {}
        self.random_state = random_state

        self.global_model = None
        self.server_state = {}
        self.history = {'train_loss': [], 'val_loss': [], 'train_r2': [], 'val_r2': []}

    def _initialize_server_state(self, model_params):
        """Initialize server-side optimization state."""
        if self.server_optimizer in ['adam', 'yogi']:
            self.server_state = {
                'm': {name: np.zeros_like(param) for name, param in model_params.items()},
                'v': {name: np.zeros_like(param) for name, param in model_params.items()},
                't': 0  # Time step
            }
        elif self.server_optimizer == 'adagrad':
            self.server_state = {
                'v': {name: np.zeros_like(param) for name, param in model_params.items()}
            }

    def _server_update(self, global_params, client_updates):
        """Apply server-side optimization."""
        if not self.server_state:
            self._initialize_server_state(global_params)

        # Compute pseudo-gradient (negative of aggregated update)
        pseudo_grad = {}
        for name in global_params:
            pseudo_grad[name] = -client_updates[name]

        if self.server_optimizer == 'adam':
            return self._adam_update(global_params, pseudo_grad)
        elif self.server_optimizer == 'yogi':
            return self._yogi_update(global_params, pseudo_grad)
        elif self.server_optimizer == 'adagrad':
            return self._adagrad_update(global_params, pseudo_grad)
        else:
            # Default: simple averaging (FedAvg)
            return {name: global_params[name] + client_updates[name]
                   for name in global_params}

    def _adam_update(self, params, grad):
        """Server-side Adam optimization."""
        self.server_state['t'] += 1
        t = self.server_state['t']

        updated_params = {}
        for name in params:
            # Update biased first moment estimate
            self.server_state['m'][name] = (
                self.beta1 * self.server_state['m'][name] +
                (1 - self.beta1) * grad[name]
            )

            # Update biased second raw moment estimate
            self.server_state['v'][name] = (
                self.beta2 * self.server_state['v'][name] +
                (1 - self.beta2) * (grad[name] ** 2)
            )

            # Compute bias-corrected first moment estimate
            m_hat = self.server_state['m'][name] / (1 - self.beta1 ** t)

            # Compute bias-corrected second raw moment estimate
            v_hat = self.server_state['v'][name] / (1 - self.beta2 ** t)

            # Update parameters
            updated_params[name] = params[name] - self.server_lr * m_hat / (np.sqrt(v_hat) + 1e-8)

        return updated_params

    def _yogi_update(self, params, grad):
        """Server-side Yogi optimization."""
        self.server_state['t'] += 1
        t = self.server_state['t']

        updated_params = {}
        for name in params:
            # Update biased first moment estimate
            self.server_state['m'][name] = (
                self.beta1 * self.server_state['m'][name] +
                (1 - self.beta1) * grad[name]
            )

            # Yogi-specific second moment update
            grad_squared = grad[name] ** 2
            v_diff = grad_squared - self.server_state['v'][name]
            self.server_state['v'][name] = (
                self.server_state['v'][name] +
                (1 - self.beta2) * np.sign(v_diff) * v_diff
            )

            # Compute bias-corrected estimates
            m_hat = self.server_state['m'][name] / (1 - self.beta1 ** t)
            v_hat = self.server_state['v'][name] / (1 - self.beta2 ** t)

            # Update parameters
            updated_params[name] = params[name] - self.server_lr * m_hat / (np.sqrt(np.maximum(v_hat, self.tau)) + 1e-8)

        return updated_params

    def _adagrad_update(self, params, grad):
        """Server-side Adagrad optimization."""
        updated_params = {}
        for name in params:
            # Accumulate squared gradients
            self.server_state['v'][name] += grad[name] ** 2

            # Update parameters
            updated_params[name] = params[name] - self.server_lr * grad[name] / (np.sqrt(self.server_state['v'][name]) + 1e-8)

        return updated_params

    def _get_model_params(self, model):
        """Extract model parameters as numpy arrays."""
        if hasattr(model, 'get_params_dict'):
            return model.get_params_dict()
        elif hasattr(model, 'coef_'):
            return {'coef_': model.coef_, 'intercept_': model.intercept_}
        else:
            # For neural networks, extract weights
            params = {}
            if hasattr(model, 'model') and hasattr(model.model, 'state_dict'):
                state_dict = model.model.state_dict()
                for name, param in state_dict.items():
                    params[name] = param.cpu().numpy()
            return params

    def _set_model_params(self, model, params):
        """Set model parameters from numpy arrays."""
        if hasattr(model, 'set_params_dict'):
            model.set_params_dict(params)
        elif hasattr(model, 'coef_'):
            model.coef_ = params['coef_']
            model.intercept_ = params['intercept_']
        else:
            # For neural networks, set weights
            if hasattr(model, 'model') and hasattr(model.model, 'state_dict'):
                import torch
                state_dict = {}
                for name, param_array in params.items():
                    state_dict[name] = torch.FloatTensor(param_array)
                model.model.load_state_dict(state_dict)

    def fit(self, X, y, X_val=None, y_val=None):
        """Fit FedOpt model."""
        start_time = time.time()
        logger.info(f"Training FedOpt ({self.server_optimizer}) with {self.n_clients} clients")

        # Split data among clients
        client_data = create_client_data(X, y, self.n_clients, random_state=self.random_state)

        # Initialize global model
        self.global_model = create_model(self.model_type, self.model_config)
        self.global_model.fit(X.iloc[:10], y.iloc[:10])  # Initialize with small sample

        # Federated training rounds
        for round_num in range(self.n_rounds):
            logger.info(f"Round {round_num + 1}/{self.n_rounds}")

            # Select clients
            n_selected = max(1, int(self.client_fraction * self.n_clients))
            selected_clients = np.random.choice(self.n_clients, n_selected, replace=False)

            # Client updates
            client_models = []
            client_weights = []

            for client_id in selected_clients:
                X_client, y_client = client_data[client_id]

                # Create local model copy
                local_model = create_model(self.model_type, self.model_config)

                # Set global parameters
                global_params = self._get_model_params(self.global_model)
                self._set_model_params(local_model, global_params)

                # Local training
                local_model.fit(X_client, y_client)

                client_models.append(local_model)
                client_weights.append(len(X_client))

            # Server aggregation with adaptive optimization
            if client_models:
                global_params = self._get_model_params(self.global_model)

                # Compute weighted average of client updates
                total_weight = sum(client_weights)
                aggregated_update = {}

                for name in global_params:
                    aggregated_update[name] = np.zeros_like(global_params[name])

                for model, weight in zip(client_models, client_weights):
                    client_params = self._get_model_params(model)
                    for name in global_params:
                        update = client_params[name] - global_params[name]
                        aggregated_update[name] += (weight / total_weight) * update

                # Apply server optimization
                new_global_params = self._server_update(global_params, aggregated_update)
                self._set_model_params(self.global_model, new_global_params)

            # Evaluate
            if round_num % 5 == 0:
                train_pred = self.global_model.predict(X)
                train_mse = mean_squared_error(y, train_pred)
                train_r2 = r2_score(y, train_pred)

                self.history['train_loss'].append(train_mse)
                self.history['train_r2'].append(train_r2)

                if X_val is not None and y_val is not None:
                    val_pred = self.global_model.predict(X_val)
                    val_mse = mean_squared_error(y_val, val_pred)
                    val_r2 = r2_score(y_val, val_pred)

                    self.history['val_loss'].append(val_mse)
                    self.history['val_r2'].append(val_r2)

                    logger.info(f"Train R²: {train_r2:.4f}, Val R²: {val_r2:.4f}")
                else:
                    logger.info(f"Train R²: {train_r2:.4f}")

        total_time = time.time() - start_time
        logger.info(f"FedOpt training completed in {total_time:.2f}s")

        return self

    def predict(self, X):
        """Make predictions using the global model."""
        if self.global_model is None:
            raise ValueError("Model has not been trained yet")
        return self.global_model.predict(X)

    @property
    def feature_importances_(self):
        """Get feature importances from the global model."""
        if self.global_model is None:
            return np.ones(199) / 199

        if hasattr(self.global_model, 'feature_importances_'):
            return self.global_model.feature_importances_
        else:
            return np.ones(199) / 199
