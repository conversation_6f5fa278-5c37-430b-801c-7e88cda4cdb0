# FLKDDrug: Federated Learning and Knowledge Distillation for Drug Solubility Prediction

This repository contains the implementation of various federated learning (FL) and knowledge distillation (KD) strategies for predicting drug solubility (logS) based on molecular descriptors.

## Overview

The project focuses on predicting the aqueous solubility of drug compounds, which is a critical property in drug development. The original implementation uses a LightGBM model optimized with Bayesian optimization. This enhanced version implements federated learning and knowledge distillation techniques to improve model performance and efficiency.

## Dataset

The project uses two main datasets:

- `logS_des.csv`: Contains molecular descriptors and logS values
- `logS.csv`: Contains SMILES strings and logS values (optional)

## Features

- **Federated Learning**: Train models across distributed datasets while preserving data privacy
- **Knowledge Distillation**: Transfer knowledge from complex teacher models to simpler student models
- **Multiple Strategies**: Various FL and KD strategies that can be combined and compared
- **Cross-Validation**: Robust evaluation using k-fold cross-validation
- **Visualization**: Comprehensive visualization of results
- **GPU Support**: Efficient training using GPU acceleration

## Implemented Strategies

### Federated Learning Strategies

#### 1. FedAvg (Federated Averaging)

- **Description**: The basic federated learning algorithm that averages model parameters from multiple clients.
- **Implementation**: For tree-based models, we average predictions rather than model parameters.
- **Key Parameters**:
  - `n_clients`: Number of clients participating in federated learning
  - `n_rounds`: Number of communication rounds
  - `local_epochs`: Number of training epochs per client per round
  - `client_sample_ratio`: Fraction of clients to sample in each round
- **Reference**: McMahan, H. B., et al. (2017). "[Communication-Efficient Learning of Deep Networks from Decentralized Data](https://proceedings.mlr.press/v54/mcmahan17a?ref=https://githubhelp.com)."

#### 2. FedProx (Federated Proximal)

- **Description**: Extends FedAvg by adding a proximal term to client objectives to improve stability and convergence.
- **Implementation**: Uses sample weights to implement the proximal regularization for tree-based models.
- **Key Parameters**:
  - All FedAvg parameters
  - `mu`: Proximal term coefficient (controls regularization strength)
- **Reference**: Li, T., et al. (2020). "[Federated Optimization in Heterogeneous Networks](https://proceedings.mlsys.org/paper_files/paper/2020/hash/1f5fe83998a09396ebe6477d9475ba0c-Abstract.html)."

#### 3. SCAFFOLD (Stochastic Controlled Averaging for Federated Learning)

- **Description**: Uses control variates to correct for client drift in federated optimization.
- **Implementation**: Adapts the control variate approach for tree-based models using prediction differences.
- **Key Parameters**:
  - All FedAvg parameters
  - `learning_rate`: Learning rate for control variate updates
- **Reference**: Karimireddy, S. P., et al. (2020). "[SCAFFOLD: Stochastic Controlled Averaging for Federated Learning](https://proceedings.mlr.press/v119/karimireddy20a.html)."

#### 4. Personalized FL

- **Description**: Creates personalized models for each client by combining global and local knowledge.
- **Implementation**: Uses a weighted ensemble of global and local models.
- **Key Parameters**:
  - All FedAvg parameters
  - `personalization_alpha`: Weight of global model (0-1)
- **Reference**: Various papers on personalized federated learning.

### Knowledge Distillation Strategies

#### 1. Vanilla KD (Knowledge Distillation)

- **Description**: Basic knowledge distillation that transfers knowledge from a teacher model to a student model.
- **Implementation**: Uses soft targets from the teacher model to guide student training.
- **Key Parameters**:
  - `alpha`: Weight of soft targets (0-1)
  - `temperature`: Temperature for softening targets
- **Reference**: Hinton, G., et al. (2015). "[Distilling the Knowledge in a Neural Network](https://arxiv.org/abs/1503.02531)."

#### 2. Ensemble KD

- **Description**: Distills knowledge from an ensemble of teacher models to a single student model.
- **Implementation**: Combines predictions from multiple teachers with optional weighting.
- **Key Parameters**:
  - `alpha`: Weight of soft targets (0-1)
  - `teacher_weights`: Weights for each teacher model
- **Reference**: Various papers on ensemble knowledge distillation.

#### 3. Progressive KD

- **Description**: Trains the student model in stages with gradually increasing complexity.
- **Implementation**: Creates a sequence of student models with increasing capacity.
- **Key Parameters**:
  - `n_stages`: Number of training stages
  - `alpha_schedule`: Schedule of alpha values for each stage
- **Reference**: Various papers on progressive knowledge distillation.

#### 4. Attention KD

- **Description**: Uses the teacher model to identify important samples and features.
- **Implementation**: Applies feature selection and sample weighting based on teacher's behavior.
- **Key Parameters**:
  - `sample_attention`: Whether to use sample attention
  - `feature_attention`: Whether to use feature attention
  - `attention_threshold`: Threshold for feature importance
- **Reference**: Various papers on attention-based knowledge distillation.

## Requirements

- Python 3.7+
- pandas
- numpy
- scikit-learn
- LightGBM (with GPU support)
- matplotlib
- seaborn
- bayesian-optimization
- torch (with CUDA support for GPU acceleration)
- torchvision
- cupy-cuda11x (for GPU array operations)
- psutil (for system monitoring)

### GPU Requirements (Recommended)
- NVIDIA GPU with CUDA Compute Capability 3.5+
- CUDA 11.0+ and cuDNN 8.0+
- At least 4GB GPU memory (8GB+ recommended)

## Installation

```bash
pip install -r requirements.txt
```

## GPU Acceleration

This project has been optimized for GPU acceleration to significantly improve performance:

### Key Features
- **Neural Network Models**: GPU-accelerated PyTorch models as default
- **GPU Data Processing**: Faster data loading and preprocessing on GPU
- **Mixed Precision Training**: Reduced memory usage and faster training
- **Automatic Device Management**: Seamless GPU/CPU fallback
- **Memory Optimization**: Efficient GPU memory utilization

### Performance Benefits
- **10-100x faster** model training with neural networks
- **5-10x faster** data preprocessing
- **2-5x faster** LightGBM training with GPU support
- **Reduced memory usage** with mixed precision training

### Testing GPU Optimization
```bash
# Test GPU functionality
python test_gpu_optimization.py

# Run with GPU acceleration (default)
python run_strategy.py --fl_strategy fedavg --gpu 0

# Force CPU usage if needed
python run_strategy.py --fl_strategy fedavg --gpu -1
```

## Usage Examples

### Running the Original Model (Baseline)

The original model serves as a baseline for comparison with federated learning and knowledge distillation strategies.

```bash
python run_strategy.py --original --gpu 0
```

### Running Federated Learning Strategies

#### FedAvg (Federated Averaging)

The basic federated learning algorithm that averages model parameters from multiple clients.

```bash
python run_strategy.py --fl_strategy fedavg --n_clients 3 --n_rounds 10 --local_epochs 1 --gpu 0
```

Additional parameters:

- `--client_sample_ratio 0.8`: Fraction of clients to sample in each round (default: 1.0)

#### FedProx (Federated Proximal)

Extends FedAvg by adding a proximal term to client objectives to improve stability and convergence.

```bash
python run_strategy.py --fl_strategy fedprox --n_clients 3 --n_rounds 10 --local_epochs 1 --mu 0.01 --gpu 0
```

Additional parameters:

- `--mu 0.01`: Proximal term coefficient (default: 0.01)
- `--client_sample_ratio 0.8`: Fraction of clients to sample in each round (default: 1.0)

#### SCAFFOLD (Stochastic Controlled Averaging)

Uses control variates to correct for client drift in federated optimization.

```bash
python run_strategy.py --fl_strategy scaffold --n_clients 3 --n_rounds 10 --local_epochs 1 --learning_rate 0.1 --gpu 0
```

Additional parameters:

- `--learning_rate 0.1`: Learning rate for control variate updates (default: 0.1)
- `--client_sample_ratio 0.8`: Fraction of clients to sample in each round (default: 1.0)

#### Personalized FL

Creates personalized models for each client by combining global and local knowledge.

```bash
python run_strategy.py --fl_strategy personalized_fl --n_clients 3 --n_rounds 10 --local_epochs 1 --personalization_alpha 0.5 --gpu 0
```

Additional parameters:

- `--personalization_alpha 0.5`: Weight of global model (0-1) (default: 0.5)
- `--client_sample_ratio 0.8`: Fraction of clients to sample in each round (default: 1.0)

### Running Knowledge Distillation Strategies

Knowledge distillation strategies require a teacher model. By default, they use the original model as the teacher.

#### Vanilla KD (Knowledge Distillation)

Basic knowledge distillation that transfers knowledge from a teacher model to a student model.

```bash
python run_strategy.py --original --kd_strategy vanilla_kd --alpha 0.5 --temperature 1.0 --gpu 0
```

Additional parameters:

- `--alpha 0.5`: Weight of soft targets (0-1) (default: 0.5)
- `--temperature 1.0`: Temperature for softening targets (default: 1.0)
- `--student_model small_lgbm`: Type of student model (default: small_lgbm)

#### Ensemble KD

Distills knowledge from an ensemble of teacher models to a single student model.

```bash
python run_strategy.py --original --kd_strategy ensemble_kd --alpha 0.5 --n_teachers 3 --gpu 0
```

Additional parameters:

- `--alpha 0.5`: Weight of soft targets (0-1) (default: 0.5)
- `--n_teachers 3`: Number of teacher models in the ensemble (default: 3)
- `--student_model small_lgbm`: Type of student model (default: small_lgbm)

#### Progressive KD

Trains the student model in stages with gradually increasing complexity.

```bash
python run_strategy.py --original --kd_strategy progressive_kd --n_stages 3 --gpu 0
```

Additional parameters:

- `--n_stages 3`: Number of training stages (default: 3)
- `--alpha_schedule 0.8,0.5,0.2`: Schedule of alpha values for each stage (default: 0.8,0.5,0.2)

#### Attention KD

Uses the teacher model to identify important samples and features.

```bash
python run_strategy.py --original --kd_strategy attention_kd --sample_attention --feature_attention --gpu 0
```

Additional parameters:

- `--sample_attention`: Whether to use sample attention (flag)
- `--feature_attention`: Whether to use feature attention (flag)
- `--attention_threshold 0.01`: Threshold for feature importance (default: 0.01)
- `--student_model small_lgbm`: Type of student model (default: small_lgbm)

### Running Combined FL+KD Strategies

All federated learning strategies can be combined with any knowledge distillation strategy. Here are some examples of combined strategies:

#### FedAvg + Vanilla KD

```bash
python run_strategy.py --fl_strategy fedavg --kd_strategy vanilla_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --alpha 0.5 --temperature 1.0 --gpu 0
```

#### FedAvg + Ensemble KD

```bash
python run_strategy.py --fl_strategy fedavg --kd_strategy ensemble_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --alpha 0.5 --n_teachers 3 --gpu 0
```

#### FedAvg + Progressive KD

```bash
python run_strategy.py --fl_strategy fedavg --kd_strategy progressive_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --n_stages 3 --gpu 0
```

#### FedAvg + Attention KD

```bash
python run_strategy.py --fl_strategy fedavg --kd_strategy attention_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --sample_attention --feature_attention --gpu 0
```

#### FedProx + Vanilla KD

```bash
python run_strategy.py --fl_strategy fedprox --kd_strategy vanilla_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --mu 0.01 --alpha 0.5 --temperature 1.0 --gpu 0
```

#### FedProx + Ensemble KD

```bash
python run_strategy.py --fl_strategy fedprox --kd_strategy ensemble_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --mu 0.01 --alpha 0.5 --n_teachers 3 --gpu 0
```

#### FedProx + Progressive KD

```bash
python run_strategy.py --fl_strategy fedprox --kd_strategy progressive_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --mu 0.01 --n_stages 3 --gpu 0
```

#### FedProx + Attention KD

```bash
python run_strategy.py --fl_strategy fedprox --kd_strategy attention_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --mu 0.01 --sample_attention --feature_attention --gpu 0
```

#### SCAFFOLD + Vanilla KD

```bash
python run_strategy.py --fl_strategy scaffold --kd_strategy vanilla_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --learning_rate 0.1 --alpha 0.5 --temperature 1.0 --gpu 0
```

#### SCAFFOLD + Ensemble KD

```bash
python run_strategy.py --fl_strategy scaffold --kd_strategy ensemble_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --learning_rate 0.1 --alpha 0.5 --n_teachers 3 --gpu 0
```

#### SCAFFOLD + Progressive KD

```bash
python run_strategy.py --fl_strategy scaffold --kd_strategy progressive_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --learning_rate 0.1 --n_stages 3 --gpu 0
```

#### SCAFFOLD + Attention KD

```bash
python run_strategy.py --fl_strategy scaffold --kd_strategy attention_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --learning_rate 0.1 --sample_attention --feature_attention --gpu 0
```

#### Personalized FL + Vanilla KD

```bash
python run_strategy.py --fl_strategy personalized_fl --kd_strategy vanilla_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --personalization_alpha 0.5 --alpha 0.5 --temperature 1.0 --gpu 0
```

#### Personalized FL + Ensemble KD

```bash
python run_strategy.py --fl_strategy personalized_fl --kd_strategy ensemble_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --personalization_alpha 0.5 --alpha 0.5 --n_teachers 3 --gpu 0
```

#### Personalized FL + Progressive KD

```bash
python run_strategy.py --fl_strategy personalized_fl --kd_strategy progressive_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --personalization_alpha 0.5 --n_stages 3 --gpu 0
```

#### Personalized FL + Attention KD

```bash
python run_strategy.py --fl_strategy personalized_fl --kd_strategy attention_kd --n_clients 3 --n_rounds 10 --local_epochs 1 --personalization_alpha 0.5 --sample_attention --feature_attention --gpu 0
```

### Running All Strategies and Comparing Results

To run all strategies and compare their results:

```bash
python run_all.py --gpu 0 --n_clients 3 --n_rounds 10 --local_epochs 100
# COMMAND ABOVE WILL TAKE ≈36 HOURS TO RUN ON AN RTX 1050 GPU WITH i7-7700K CPU
```

### Running Specific Strategies Only

To run only specific strategies:

```bash
python run_all.py --gpu 0 --strategies fedavg fedprox vanilla_kd
```

### Command-line Arguments

- `--fl_strategy`: Federated learning strategy (fedavg, fedprox, scaffold, personalized_fl)
- `--kd_strategy`: Knowledge distillation strategy (vanilla_kd, ensemble_kd, progressive_kd, attention_kd)
- `--n_clients`: Number of clients for federated learning (default: 3)
- `--n_rounds`: Number of communication rounds (default: 10)
- `--local_epochs`: Number of local epochs (default: 1)
- `--alpha`: Alpha parameter for knowledge distillation (default: 0.5)
- `--temperature`: Temperature parameter for knowledge distillation (default: 1.0)
- `--mu`: Proximal term coefficient for FedProx (default: 0.01)
- `--learning_rate`: Learning rate for SCAFFOLD (default: 0.1)
- `--personalization_alpha`: Weight of global model for Personalized FL (default: 0.5)
- `--n_teachers`: Number of teacher models for Ensemble KD (default: 3)
- `--n_stages`: Number of training stages for Progressive KD (default: 3)
- `--sample_attention`: Whether to use sample attention for Attention KD (flag)
- `--feature_attention`: Whether to use feature attention for Attention KD (flag)
- `--attention_threshold`: Threshold for feature importance (default: 0.01)
- `--student_model`: Type of student model (default: small_lgbm)
- `--gpu`: GPU ID to use (-1 for CPU)
- `--seed`: Random seed (default: 42)
- `--output_dir`: Directory to save results (default: 'results')
- `--original`: Run original model as baseline (for run_strategy.py)
- `--strategies`: Specific strategies to run (for run_all.py)

## Project Structure

```bash
.
├── README.md                  # Project documentation
├── config.py                  # Configuration parameters
├── requirements.txt           # Dependencies
├── run_all.py                 # Script to run all strategies
├── run_strategy.py            # Script to run a specific strategy
├── utils/                     # Utility functions
│   ├── data_utils.py          # Data loading and preprocessing
│   ├── evaluation.py          # Model evaluation
│   ├── visualization.py       # Plotting and visualization
│   └── model_utils.py         # Model creation and utilities
├── strategies/                # Strategy implementations
│   ├── federated/             # Federated learning strategies
│   │   ├── fedavg.py          # FedAvg algorithm
│   │   ├── fedprox.py         # FedProx algorithm
│   │   ├── scaffold.py        # SCAFFOLD algorithm
│   │   └── personalized_fl.py # Personalized FL
│   └── distillation/          # Knowledge distillation strategies
│       ├── vanilla_kd.py      # Basic knowledge distillation
│       ├── ensemble_kd.py     # Ensemble knowledge distillation
│       ├── progressive_kd.py  # Progressive knowledge distillation
│       └── attention_kd.py    # Attention-based knowledge distillation
└── results/                   # Results directory
    ├── logs/                  # Log files
    ├── models/                # Saved models
    └── plots/                 # Visualization plots
```

## Original Implementation

The original implementation is based on LightGBM with Bayesian optimization for hyperparameter tuning. The original scripts are:

- `original-model_save.py`: Trains and saves a model
- `original-model_CVtest.py`: Performs cross-validation

## Optimized Implementation

The optimized implementation adds federated learning and knowledge distillation:

- `model_save_fl_kd.py`: Basic FL+KD implementation
- `model_CVtest_fl_kd.py`: Cross-validation with FL+KD

## Results and Evaluation

The results of different strategies are saved in the `results` directory, including:

- Performance metrics (MAE, MSE, RMSE, R², EVS, MAPE)
- Trained models
- Visualization plots (actual vs. predicted, residuals, feature importance)
- Comparison plots (strategy comparison)

### Evaluation Metrics

The framework evaluates models using the following metrics:

1. **MAE (Mean Absolute Error)**: Average absolute difference between predicted and actual values. Lower is better.
   - Formula: `MAE = (1/n) * Σ|y_true - y_pred|`
   - Range: [0, ∞)
   - Interpretation: Average magnitude of errors in prediction, in the same units as the target variable.

2. **MSE (Mean Squared Error)**: Average squared difference between predicted and actual values. Lower is better.
   - Formula: `MSE = (1/n) * Σ(y_true - y_pred)²`
   - Range: [0, ∞)
   - Interpretation: Penalizes larger errors more than smaller ones.

3. **RMSE (Root Mean Squared Error)**: Square root of MSE. Lower is better.
   - Formula: `RMSE = √MSE`
   - Range: [0, ∞)
   - Interpretation: Standard deviation of prediction errors, in the same units as the target variable.

4. **R² (Coefficient of Determination)**: Proportion of variance in the dependent variable that is predictable from the independent variables. Higher is better.
   - Formula: `R² = 1 - (Σ(y_true - y_pred)² / Σ(y_true - y_mean)²)`
   - Range: (-∞, 1]
   - Interpretation: 1 indicates perfect prediction, 0 indicates the model predicts no better than the mean value.

5. **EVS (Explained Variance Score)**: Proportion of variance in the dependent variable that is explained by the model. Higher is better.
   - Formula: `EVS = 1 - Var(y_true - y_pred) / Var(y_true)`
   - Range: (-∞, 1]
   - Interpretation: 1 indicates perfect prediction, lower values indicate worse prediction.

6. **MAPE (Mean Absolute Percentage Error)**: Average percentage difference between predicted and actual values. Lower is better.
   - Formula: `MAPE = (100/n) * Σ|y_true - y_pred| / |y_true|`
   - Range: [0, ∞)
   - Interpretation: Average percentage error, useful for understanding relative magnitude of errors.

### Result Files

After running the `run_all.py` script, the following files are generated:

1. **all_results.csv**: Contains all evaluation metrics for each strategy.
   - Columns: strategy_name, train_MAE, val_MAE, test_MAE, train_MSE, val_MSE, test_MSE, train_RMSE, val_RMSE, test_RMSE, train_R2, val_R2, test_R2, train_EVS, val_EVS, test_EVS, train_MAPE, val_MAPE, test_MAPE, training_time, inference_time
   - Each row represents a different strategy

2. **Visualization Plots**:
   - `strategy_comparison_mae.png`: Bar chart comparing MAE across strategies
   - `strategy_comparison_mse.png`: Bar chart comparing MSE across strategies
   - `strategy_comparison_rmse.png`: Bar chart comparing RMSE across strategies
   - `strategy_comparison_r2.png`: Bar chart comparing R² across strategies
   - `strategy_comparison_evs.png`: Bar chart comparing EVS across strategies
   - `strategy_comparison_mape.png`: Bar chart comparing MAPE across strategies

3. **Strategy-specific Results**:
   - For each strategy, detailed results are saved in the `results/[strategy_name]` directory
   - Includes model files, prediction plots, and detailed metrics

### Interpreting Results

When comparing strategies, consider the following:

1. **Performance Metrics**: Lower MAE, MSE, RMSE, and MAPE are better; higher R² and EVS are better.
2. **Training Time**: Time taken to train the model (important for resource-constrained environments).
3. **Inference Time**: Time taken to make predictions (important for real-time applications).
4. **Model Size**: Size of the trained model (important for deployment on edge devices).

Different strategies may excel in different aspects. For example:

- Federated learning strategies may have better privacy preservation but slightly lower performance.
- Knowledge distillation strategies may have smaller model sizes but slightly lower performance.
- Combined FL+KD strategies may offer a good balance between privacy, model size, and performance.

## License

TBD

## Citation

If you use this code in your research, please cite:

```bibtex
@article{FLKDDrug25,
  title={Federated Learning and Knowledge Distillation for Drug Solubility Prediction},
  author={Aizierjiang Aiersilan; prefixed-original-scripts & dataset: Xianran Su},
  journal={Privately Revealed},
  year={2025}
}
```

## Acknowledgements

This project is based on the paper "FormulationBCS: A Machine Learning Platform Based on Diverse Molecular Representations for Biopharmaceutical Classification System (BCS) Class Prediction by Zheng Wu et al." and extends it with its dataset (`*.csv` files) along with the initial 2 scripts (those 2 prefixed with "original-") provided by Xianran Su under the support of Distinguished Prof. Jerome YEN at the University of Macau, with my implementation of federated learning and knowledge distillation techniques for testing drug solubility prediction under various strategies combined.
