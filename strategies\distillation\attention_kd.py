"""
Implementation of Attention-based Knowledge Distillation.

This module implements knowledge distillation with attention to important samples
and features based on the teacher model's behavior.
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time
from sklearn.feature_selection import SelectFromModel
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

class AttentionKD:
    """
    Attention-based Knowledge Distillation for regression models.
    
    This implementation uses the teacher model to identify important samples
    and features, then focuses the student's learning on these aspects.
    """
    
    def __init__(self, teacher_model=None, student_model=None, 
                 sample_attention=True, feature_attention=True,
                 attention_threshold=0.8, random_state=42):
        """
        Initialize AttentionKD.
        
        Args:
            teacher_model: Teacher model
            student_model: Student model (default: smaller LGBMRegressor)
            sample_attention: Whether to use sample attention
            feature_attention: Whether to use feature attention
            attention_threshold: Threshold for feature importance
            random_state: Random seed
        """
        self.teacher_model = teacher_model
        
        if student_model is None:
            # Default: smaller LightGBM model
            self.student_model = LGBMRegressor(
                n_estimators=100,
                num_leaves=31,
                learning_rate=0.05,
                random_state=random_state
            )
        else:
            self.student_model = student_model
            
        self.sample_attention = sample_attention
        self.feature_attention = feature_attention
        self.attention_threshold = attention_threshold
        self.random_state = random_state
        self.history = {
            'train_loss': [],
            'val_loss': []
        }
        
        self.feature_selector = None
        self.important_features = None
        
        np.random.seed(random_state)
    
    def fit(self, X_train, y_train, X_val=None, y_val=None, epochs=1):
        """
        Train the student model with attention-based knowledge distillation.
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            epochs: Number of training epochs
            
        Returns:
            self: Trained model
        """
        if self.teacher_model is None:
            raise ValueError("Teacher model is not provided")
        
        start_time = time.time()
        logger.info(f"Starting Attention KD training with sample_attention={self.sample_attention}, "
                   f"feature_attention={self.feature_attention}")
        
        # Get soft targets from teacher
        soft_targets = self.teacher_model.predict(X_train)
        
        # Feature attention
        if self.feature_attention and hasattr(self.teacher_model, 'feature_importances_'):
            # Select important features based on teacher's feature importances
            self.feature_selector = SelectFromModel(
                self.teacher_model, 
                threshold=f"{self.attention_threshold}*mean",
                prefit=True
            )
            
            # Transform data to use only important features
            X_train_selected = self.feature_selector.transform(X_train)
            if X_val is not None:
                X_val_selected = self.feature_selector.transform(X_val)
            
            # Get indices of selected features
            self.important_features = self.feature_selector.get_support(indices=True)
            
            logger.info(f"Selected {len(self.important_features)}/{X_train.shape[1]} features "
                       f"based on teacher's feature importances")
        else:
            X_train_selected = X_train
            X_val_selected = X_val
        
        # Sample attention
        if self.sample_attention:
            # Calculate prediction error of teacher
            teacher_error = np.abs(y_train - soft_targets)
            
            # Normalize error to [0, 1] range to use as sample weights
            if teacher_error.max() > teacher_error.min():
                sample_weights = (teacher_error - teacher_error.min()) / (teacher_error.max() - teacher_error.min())
                # Invert weights so that samples with lower error get higher weight
                sample_weights = 1.0 - sample_weights
                # Scale to mean=1.0
                sample_weights = sample_weights / sample_weights.mean()
            else:
                sample_weights = np.ones_like(teacher_error)
            
            logger.info(f"Sample weights: min={sample_weights.min():.4f}, max={sample_weights.max():.4f}, "
                       f"mean={sample_weights.mean():.4f}")
        else:
            sample_weights = None
        
        # Training loop
        for epoch in range(epochs):
            epoch_start = time.time()
            
            # Train student on soft targets with attention
            self.student_model.fit(
                X_train_selected, soft_targets, 
                sample_weight=sample_weights
            )
            
            # Evaluate
            if X_val is not None and y_val is not None:
                y_pred = self.predict(X_val)
                val_loss = np.mean(np.abs(y_val - y_pred))
                self.history['val_loss'].append(val_loss)
                
                # Also evaluate on training set
                y_pred_train = self.predict(X_train)
                train_loss = np.mean(np.abs(y_train - y_pred_train))
                self.history['train_loss'].append(train_loss)
                
                epoch_time = time.time() - epoch_start
                logger.info(f"Epoch {epoch+1}/{epochs} completed in {epoch_time:.2f}s. "
                           f"Train loss: {train_loss:.4f}, Val loss: {val_loss:.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"Attention KD training completed in {total_time:.2f}s")
        
        return self
    
    def predict(self, X):
        """
        Make predictions using the student model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        if self.feature_attention and self.feature_selector is not None:
            X_selected = self.feature_selector.transform(X)
            return self.student_model.predict(X_selected)
        else:
            return self.student_model.predict(X)
    
    def get_important_features(self, feature_names=None):
        """
        Get important features selected by the teacher.
        
        Args:
            feature_names: List of feature names
            
        Returns:
            list: List of important feature names or indices
        """
        if self.important_features is None:
            return None
        
        if feature_names is not None:
            return [feature_names[i] for i in self.important_features]
        else:
            return self.important_features
