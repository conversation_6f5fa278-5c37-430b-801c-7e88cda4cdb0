"""
Neural network models for GPU-accelerated machine learning.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import logging
from typing import List, Optional, Dict, Any
import time
from sklearn.base import BaseEstimator, RegressorMixin

logger = logging.getLogger(__name__)


class MLPRegressor(nn.Module):
    """
    Multi-layer perceptron for regression tasks.
    """

    def __init__(self, input_dim: int, hidden_dims: List[int],
                 dropout_rate: float = 0.2, activation: str = 'relu',
                 batch_norm: bool = True):
        """
        Initialize MLP regressor.

        Args:
            input_dim: Input feature dimension
            hidden_dims: List of hidden layer dimensions
            dropout_rate: Dropout rate
            activation: Activation function ('relu', 'gelu', 'swish')
            batch_norm: Whether to use batch normalization
        """
        super(MLPRegressor, self).__init__()

        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        self.batch_norm = batch_norm

        # Activation function
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish':
            self.activation = nn.SiLU()
        else:
            raise ValueError(f"Unknown activation: {activation}")

        # Build layers
        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            if batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(self.activation)
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim

        # Output layer
        layers.append(nn.Linear(prev_dim, 1))

        self.network = nn.Sequential(*layers)

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)

    def forward(self, x):
        """Forward pass."""
        return self.network(x).squeeze(-1)


class NeuralNetRegressor(BaseEstimator, RegressorMixin):
    """
    Scikit-learn compatible neural network regressor with GPU acceleration.
    """

    def __init__(self, hidden_dims: List[int] = [256, 128, 64],
                 dropout_rate: float = 0.2, activation: str = 'relu',
                 batch_norm: bool = True, learning_rate: float = 0.001,
                 weight_decay: float = 1e-5, batch_size: int = 64,
                 epochs: int = 100, early_stopping_patience: int = 10,
                 lr_scheduler: str = 'cosine', optimizer: str = 'adamw',
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize neural network regressor.

        Args:
            hidden_dims: Hidden layer dimensions
            dropout_rate: Dropout rate
            activation: Activation function
            batch_norm: Use batch normalization
            learning_rate: Learning rate
            weight_decay: Weight decay
            batch_size: Batch size
            epochs: Number of epochs
            early_stopping_patience: Early stopping patience
            lr_scheduler: Learning rate scheduler
            optimizer: Optimizer type
            device: Device to use
            random_state: Random seed
        """
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        self.activation = activation
        self.batch_norm = batch_norm
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.batch_size = batch_size
        self.epochs = epochs
        self.early_stopping_patience = early_stopping_patience
        self.lr_scheduler = lr_scheduler
        self.optimizer = optimizer
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state

        self.model = None
        self.optimizer_obj = None
        self.scheduler = None
        self.scaler = None
        self.history = {'train_loss': [], 'val_loss': []}

        # Set random seeds
        torch.manual_seed(random_state)
        if self.device.type == 'cuda':
            torch.cuda.manual_seed(random_state)

    def _create_model(self, input_dim: int):
        """Create the neural network model."""
        self.model = MLPRegressor(
            input_dim=input_dim,
            hidden_dims=self.hidden_dims,
            dropout_rate=self.dropout_rate,
            activation=self.activation,
            batch_norm=self.batch_norm
        ).to(self.device)

        # Create optimizer
        if self.optimizer == 'adamw':
            self.optimizer_obj = optim.AdamW(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif self.optimizer == 'adam':
            self.optimizer_obj = optim.Adam(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif self.optimizer == 'sgd':
            self.optimizer_obj = optim.SGD(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay,
                momentum=0.9
            )
        else:
            raise ValueError(f"Unknown optimizer: {self.optimizer}")

        # Create learning rate scheduler
        if self.lr_scheduler == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer_obj, T_max=self.epochs
            )
        elif self.lr_scheduler == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer_obj, step_size=30, gamma=0.1
            )
        elif self.lr_scheduler == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer_obj, mode='min', patience=5, factor=0.5
            )

        # Mixed precision scaler
        if self.device.type == 'cuda':
            self.scaler = torch.cuda.amp.GradScaler()

    def fit(self, X, y, X_val=None, y_val=None, sample_weight=None):
        """
        Fit the neural network.

        Args:
            X: Training features
            y: Training targets
            X_val: Validation features
            y_val: Validation targets
            sample_weight: Sample weights (not implemented)

        Returns:
            self: Fitted model
        """
        start_time = time.time()
        logger.info(f"Training neural network on {self.device}")

        # Convert to tensors and move to device
        if not isinstance(X, torch.Tensor):
            X = torch.from_numpy(X.values if hasattr(X, 'values') else X).float()
        if not isinstance(y, torch.Tensor):
            y = torch.from_numpy(y.values if hasattr(y, 'values') else y).float()

        X = X.to(self.device)
        y = y.to(self.device)

        # Create model
        self._create_model(X.shape[1])

        # Create data loader
        dataset = torch.utils.data.TensorDataset(X, y)
        train_loader = torch.utils.data.DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True
        )

        # Validation data
        if X_val is not None and y_val is not None:
            if not isinstance(X_val, torch.Tensor):
                X_val = torch.from_numpy(X_val.values if hasattr(X_val, 'values') else X_val).float()
            if not isinstance(y_val, torch.Tensor):
                y_val = torch.from_numpy(y_val.values if hasattr(y_val, 'values') else y_val).float()
            X_val = X_val.to(self.device)
            y_val = y_val.to(self.device)

        # Training loop
        best_val_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.epochs):
            # Training
            self.model.train()
            train_loss = 0.0

            for batch_X, batch_y in train_loader:
                self.optimizer_obj.zero_grad()

                if self.scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(batch_X)
                        loss = nn.MSELoss()(outputs, batch_y)

                    self.scaler.scale(loss).backward()
                    self.scaler.step(self.optimizer_obj)
                    self.scaler.update()
                else:
                    outputs = self.model(batch_X)
                    loss = nn.MSELoss()(outputs, batch_y)
                    loss.backward()
                    self.optimizer_obj.step()

                train_loss += loss.item()

            train_loss /= len(train_loader)
            self.history['train_loss'].append(train_loss)

            # Validation
            if X_val is not None and y_val is not None:
                self.model.eval()
                with torch.no_grad():
                    val_outputs = self.model(X_val)
                    val_loss = nn.MSELoss()(val_outputs, y_val).item()
                    self.history['val_loss'].append(val_loss)

                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= self.early_stopping_patience:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break

                # Learning rate scheduling
                if self.lr_scheduler == 'plateau':
                    self.scheduler.step(val_loss)
                elif self.scheduler is not None:
                    self.scheduler.step()

                if (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, "
                               f"Train Loss: {train_loss:.6f}, "
                               f"Val Loss: {val_loss:.6f}")
            else:
                if self.scheduler is not None and self.lr_scheduler != 'plateau':
                    self.scheduler.step()

                if (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, "
                               f"Train Loss: {train_loss:.6f}")

        total_time = time.time() - start_time
        logger.info(f"Neural network training completed in {total_time:.2f}s")

        return self

    def predict(self, X):
        """
        Make predictions.

        Args:
            X: Features

        Returns:
            array: Predictions
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")

        self.model.eval()

        # Convert to tensor and move to device
        if not isinstance(X, torch.Tensor):
            X = torch.from_numpy(X.values if hasattr(X, 'values') else X).float()
        X = X.to(self.device)

        with torch.no_grad():
            predictions = self.model(X)
            predictions = predictions.cpu().numpy().flatten()

            # Handle NaN values
            if np.isnan(predictions).any():
                logger.warning("NaN values detected in predictions, replacing with zeros")
                predictions = np.nan_to_num(predictions, nan=0.0)

        return predictions

    @property
    def feature_importances_(self):
        """
        Compute feature importances using gradient-based method.

        Returns:
            array: Feature importances
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")

        # This is a simplified feature importance calculation
        # In practice, you might want to use more sophisticated methods
        importances = []
        for param in self.model.parameters():
            if param.requires_grad and len(param.shape) == 2:
                # Use the norm of weights in the first layer as importance
                importances.append(torch.norm(param, dim=0).detach().cpu().numpy())
                break

        if importances:
            return importances[0] / np.sum(importances[0])
        else:
            # Return uniform importance if no weights found
            input_dim = getattr(self.model, 'input_dim', 199)  # Default to dataset feature count
            return np.ones(input_dim) / input_dim
