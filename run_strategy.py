"""
<PERSON><PERSON><PERSON> to run a specific strategy.
"""

import os
import argparse
import logging
import pandas as pd
import numpy as np

# Import configuration
import config

# Import utilities
from utils.data_utils import load_data, split_data, create_client_data, preprocess_data
from utils.evaluation import evaluate_model, log_results
from utils.visualization import (
    plot_actual_vs_predicted, plot_residuals, plot_feature_importance,
    plot_client_data_distribution
)
from utils.model_utils import create_model, save_model

# Import federated learning strategies
from strategies.federated.fedavg import FedAvg
from strategies.federated.fedprox import FedProx
from strategies.federated.scaffold import SCAFFOLD
from strategies.federated.personalized_fl import PersonalizedFL

# Import knowledge distillation strategies
from strategies.distillation.vanilla_kd import VanillaKD
from strategies.distillation.ensemble_kd import EnsembleKD
from strategies.distillation.progressive_kd import ProgressiveKD
from strategies.distillation.attention_kd import AttentionKD

# Set up logger
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run a specific strategy')

    parser.add_argument('--fl_strategy', type=str, default=None,
                       choices=['fedavg', 'fedprox', 'scaffold', 'personalized_fl'],
                       help='Federated learning strategy')

    parser.add_argument('--kd_strategy', type=str, default=None,
                       choices=['vanilla_kd', 'ensemble_kd', 'progressive_kd', 'attention_kd'],
                       help='Knowledge distillation strategy')

    parser.add_argument('--n_clients', type=int, default=3,
                       help='Number of clients for federated learning')

    parser.add_argument('--n_rounds', type=int, default=10,
                       help='Number of communication rounds')

    parser.add_argument('--local_epochs', type=int, default=1,
                       help='Number of local epochs')

    parser.add_argument('--alpha', type=float, default=0.5,
                       help='Alpha parameter for knowledge distillation')

    parser.add_argument('--temperature', type=float, default=1.0,
                       help='Temperature parameter for knowledge distillation')

    parser.add_argument('--gpu', type=int, default=-1,
                       help='GPU ID to use (-1 for CPU)')

    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')

    parser.add_argument('--output_dir', type=str, default='results',
                       help='Directory to save results')

    parser.add_argument('--original', action='store_true',
                       help='Run original model as baseline')

    return parser.parse_args()

def run_original_model(X_train, y_train, X_val, y_val, X_test, y_test, gpu_manager=None):
    """Run the original model as baseline with GPU acceleration."""
    logger.info("Running GPU-accelerated neural network baseline model...")

    # Use neural network as default for GPU acceleration
    model_type = config.MODEL_CONFIG.get('default_model_type', 'neural_net')

    if model_type == 'neural_net':
        # Use neural network with GPU acceleration
        model_params = config.MODEL_CONFIG.get('neural_net_teacher', {})

        # Create and train model
        best_model = create_model(
            model_type='neural_net',
            params=model_params.copy(),
            random_state=config.DATA_CONFIG['random_state'],
            device=gpu_manager.device if gpu_manager else None,
            gpu_manager=gpu_manager
        )

        # Train with validation for early stopping
        best_model.fit(X_train, y_train, X_val=X_val, y_val=y_val)
        logger.info("Neural network training completed successfully")

    else:
        # Fallback to LightGBM with hyperparameter optimization
        from lightgbm import LGBMRegressor
        from bayes_opt import BayesianOptimization
        logger.info("Using LightGBM as fallback model with hyperparameter optimization")

        # Define optimization function
        def rf(n_estimators, learning_rate, num_leaves, subsample,
              colsample_bytree, reg_alpha, reg_lambda):
            model_params = {
                'n_estimators': int(n_estimators),
                'learning_rate': learning_rate,
                'num_leaves': int(num_leaves),
                'subsample': subsample,
                'colsample_bytree': colsample_bytree,
                'reg_alpha': reg_alpha,
                'reg_lambda': reg_lambda,
                'random_state': config.DATA_CONFIG['random_state']
            }

            # Enable GPU for LightGBM if available
            if gpu_manager and gpu_manager.device.type == 'cuda':
                model_params.update({
                    'device': 'gpu',
                    'gpu_platform_id': 0,
                    'gpu_device_id': gpu_manager.device.index or 0
                })

            model = LGBMRegressor(**model_params)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            return -np.mean(np.abs(y_val - y_pred))

        # Define parameter bounds
        pbounds = {
            'n_estimators': (100, 1000),
            'learning_rate': (0.001, 0.1),
            'num_leaves': (2, 50),
            'subsample': (0.5, 1.0),
            'colsample_bytree': (0.1, 1.0),
            'reg_alpha': (0.0, 5.0),
            'reg_lambda': (0.0, 10.0)
        }

        # Run Bayesian optimization
        optimizer = BayesianOptimization(
            f=rf,
            pbounds=pbounds,
            random_state=config.DATA_CONFIG['random_state']
        )

        optimizer.maximize(
            init_points=config.HYPEROPT_CONFIG['init_points'],
            n_iter=config.HYPEROPT_CONFIG['n_iter']
        )

        # Get best parameters
        params = optimizer.max['params']
        logger.info(f"Best parameters: {params}")

        # Create and train model with best parameters
        best_model = LGBMRegressor(
            n_estimators=int(params['n_estimators']),
            learning_rate=params['learning_rate'],
            num_leaves=int(params['num_leaves']),
            subsample=params['subsample'],
            colsample_bytree=params['colsample_bytree'],
            reg_alpha=params['reg_alpha'],
            reg_lambda=params['reg_lambda'],
            random_state=config.DATA_CONFIG['random_state']
        )

        best_model.fit(X_train, y_train)
        best_model = best_model

    # Evaluate model
    train_metrics, train_preds = evaluate_model(best_model, X_train, y_train, prefix="train_")
    val_metrics, val_preds = evaluate_model(best_model, X_val, y_val, prefix="val_")
    test_metrics, test_preds = evaluate_model(best_model, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}

    # Log results
    log_file = log_results(metrics, "original", config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(best_model, os.path.join(config.OUTPUT_CONFIG['models_dir'], 'original_model.pkl'))

    # Create plots
    if config.OUTPUT_CONFIG['save_plots']:
        plot_actual_vs_predicted(
            y_test, test_preds,
            title="Original Model: Actual vs Predicted",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'original_actual_vs_pred.png'),
            show=False
        )

        plot_residuals(
            y_test, test_preds,
            title="Original Model: Residuals",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'original_residuals.png'),
            show=False
        )

        plot_feature_importance(
            best_model, X_train.columns,
            title="Original Model: Feature Importance",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'original_feature_importance.png'),
            show=False
        )

    return best_model, metrics

def run_federated_learning(strategy_name, X_train, y_train, X_val, y_val, X_test, y_test, client_data, args, gpu_manager=None):
    """Run a federated learning strategy with GPU acceleration."""
    logger.info(f"Running federated learning strategy: {strategy_name}")

    # Use neural network as default for GPU acceleration
    model_type = config.MODEL_CONFIG.get('default_model_type', 'neural_net')
    model_params = config.MODEL_CONFIG.get(f'{model_type}_teacher', {})

    # Create base model
    base_model = create_model(
        model_type=model_type,
        params=model_params.copy(),
        random_state=config.DATA_CONFIG['random_state'],
        device=gpu_manager.device if gpu_manager else None,
        gpu_manager=gpu_manager
    )

    # Initialize strategy
    if strategy_name == 'fedavg':
        strategy = FedAvg(
            base_model=base_model,
            n_clients=args.n_clients,
            client_data=client_data,
            n_rounds=args.n_rounds,
            local_epochs=args.local_epochs,
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            random_state=args.seed
        )
    elif strategy_name == 'fedprox':
        strategy = FedProx(
            base_model=base_model,
            n_clients=args.n_clients,
            client_data=client_data,
            n_rounds=args.n_rounds,
            local_epochs=args.local_epochs,
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            mu=0.01,  # Proximal term coefficient
            random_state=args.seed
        )
    elif strategy_name == 'scaffold':
        strategy = SCAFFOLD(
            base_model=base_model,
            n_clients=args.n_clients,
            client_data=client_data,
            n_rounds=args.n_rounds,
            local_epochs=args.local_epochs,
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            learning_rate=0.1,  # Learning rate for control variate updates
            random_state=args.seed
        )
    elif strategy_name == 'personalized_fl':
        strategy = PersonalizedFL(
            base_model=base_model,
            n_clients=args.n_clients,
            client_data=client_data,
            n_rounds=args.n_rounds,
            local_epochs=args.local_epochs,
            client_sample_ratio=config.FL_CONFIG['client_sample_ratio'],
            personalization_alpha=0.5,  # Weight of global model
            random_state=args.seed
        )
    else:
        raise ValueError(f"Unknown federated learning strategy: {strategy_name}")

    # Train strategy
    strategy.fit(X_val, y_val)

    # Evaluate strategy
    train_metrics, train_preds = evaluate_model(strategy, X_train, y_train, prefix="train_")
    val_metrics, val_preds = evaluate_model(strategy, X_val, y_val, prefix="val_")
    test_metrics, test_preds = evaluate_model(strategy, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}

    # Log results
    log_file = log_results(metrics, strategy_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(strategy, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{strategy_name}_model.pkl'))

    # Create plots
    if config.OUTPUT_CONFIG['save_plots']:
        plot_actual_vs_predicted(
            y_test, test_preds,
            title=f"{strategy_name}: Actual vs Predicted",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_actual_vs_pred.png'),
            show=False
        )

        plot_residuals(
            y_test, test_preds,
            title=f"{strategy_name}: Residuals",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_residuals.png'),
            show=False
        )

    return strategy, metrics

def run_knowledge_distillation(strategy_name, teacher_model, X_train, y_train, X_val, y_val, X_test, y_test, args, gpu_manager=None):
    """Run a knowledge distillation strategy with GPU acceleration."""
    logger.info(f"Running knowledge distillation strategy: {strategy_name}")

    # Use neural network as default for GPU acceleration
    model_type = config.MODEL_CONFIG.get('default_model_type', 'neural_net')
    model_params = config.MODEL_CONFIG.get(f'{model_type}_student', {})

    # Create student model
    student_model = create_model(
        model_type=model_type,
        params=model_params.copy(),
        random_state=config.DATA_CONFIG['random_state'],
        device=gpu_manager.device if gpu_manager else None,
        gpu_manager=gpu_manager
    )

    # Initialize strategy
    if strategy_name == 'vanilla_kd':
        strategy = VanillaKD(
            teacher_model=teacher_model,
            student_model=student_model,
            alpha=args.alpha,
            temperature=args.temperature,
            random_state=args.seed
        )
    elif strategy_name == 'ensemble_kd':
        # For ensemble KD, teacher_model should be a list of models
        if not isinstance(teacher_model, list):
            teacher_model = [teacher_model]

        strategy = EnsembleKD(
            teacher_models=teacher_model,
            student_model=student_model,
            alpha=args.alpha,
            random_state=args.seed
        )
    elif strategy_name == 'progressive_kd':
        strategy = ProgressiveKD(
            teacher_model=teacher_model,
            student_model=student_model,
            n_stages=config.KD_CONFIG['n_stages'],
            random_state=args.seed
        )
    elif strategy_name == 'attention_kd':
        strategy = AttentionKD(
            teacher_model=teacher_model,
            student_model=student_model,
            sample_attention=True,
            feature_attention=True,
            attention_threshold=config.KD_CONFIG['attention_threshold'],
            random_state=args.seed
        )
    else:
        raise ValueError(f"Unknown knowledge distillation strategy: {strategy_name}")

    # Train strategy
    strategy.fit(X_train, y_train, X_val, y_val, epochs=config.KD_CONFIG['epochs'])

    # Evaluate strategy
    train_metrics, train_preds = evaluate_model(strategy, X_train, y_train, prefix="train_")
    val_metrics, val_preds = evaluate_model(strategy, X_val, y_val, prefix="val_")
    test_metrics, test_preds = evaluate_model(strategy, X_test, y_test, prefix="test_")

    # Combine metrics
    metrics = {**train_metrics, **val_metrics, **test_metrics}

    # Log results
    log_file = log_results(metrics, strategy_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(strategy, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{strategy_name}_model.pkl'))

    # Create plots
    if config.OUTPUT_CONFIG['save_plots']:
        plot_actual_vs_predicted(
            y_test, test_preds,
            title=f"{strategy_name}: Actual vs Predicted",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_actual_vs_pred.png'),
            show=False
        )

        plot_residuals(
            y_test, test_preds,
            title=f"{strategy_name}: Residuals",
            save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], f'{strategy_name}_residuals.png'),
            show=False
        )

    return strategy, metrics

def run_combined_strategy(fl_strategy_name, kd_strategy_name, X_train, y_train, X_val, y_val, X_test, y_test, client_data, args, gpu_manager=None):
    """Run a combined federated learning and knowledge distillation strategy with GPU acceleration."""
    logger.info(f"Running combined strategy: {fl_strategy_name}+{kd_strategy_name}")

    # First run federated learning
    fl_model, _ = run_federated_learning(fl_strategy_name, X_train, y_train, X_val, y_val, X_test, y_test, client_data, args, gpu_manager)

    # Then run knowledge distillation with FL model as teacher
    combined_model, metrics = run_knowledge_distillation(kd_strategy_name, fl_model, X_train, y_train, X_val, y_val, X_test, y_test, args, gpu_manager)

    # Update strategy name in metrics
    combined_name = f"{fl_strategy_name}_{kd_strategy_name}"
    metrics['strategy'] = combined_name

    # Log results
    log_file = log_results(metrics, combined_name, config.OUTPUT_CONFIG['logs_dir'])

    # Save model
    if config.OUTPUT_CONFIG['save_models']:
        save_model(combined_model, os.path.join(config.OUTPUT_CONFIG['models_dir'], f'{combined_name}_model.pkl'))

    return combined_model, metrics

def main():
    """Main function to run a specific strategy."""
    # Parse command line arguments
    args = parse_args()

    # Update configuration with command line arguments
    config.FL_CONFIG['n_clients'] = args.n_clients
    config.FL_CONFIG['n_rounds'] = args.n_rounds
    config.FL_CONFIG['local_epochs'] = args.local_epochs
    config.KD_CONFIG['alpha'] = args.alpha
    config.KD_CONFIG['temperature'] = args.temperature
    config.GPU_CONFIG['gpu_id'] = args.gpu
    config.GPU_CONFIG['use_gpu'] = args.gpu >= 0
    config.DATA_CONFIG['random_state'] = args.seed
    config.OUTPUT_CONFIG['results_dir'] = args.output_dir

    # Configure GPU if available
    from utils.gpu_utils import GPUManager
    gpu_manager = GPUManager(config.GPU_CONFIG)

    if config.GPU_CONFIG['use_gpu']:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(config.GPU_CONFIG['gpu_id'])
        logger.info(f"Using GPU {config.GPU_CONFIG['gpu_id']}: {gpu_manager.device}")
        gpu_manager.log_memory_usage()
    else:
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        logger.info("Using CPU")

    # Load data
    logger.info("Loading data...")
    if gpu_manager.device.type == 'cuda':
        # Use GPU-accelerated data loading for better performance
        from utils.data_utils import load_data_gpu, preprocess_data_gpu

        # Load data to CPU first, then move to GPU after preprocessing
        X, y = load_data(
            primary_file=config.DATA_CONFIG['primary_file'],
            secondary_file=config.DATA_CONFIG['secondary_file'],
            combine=config.DATA_CONFIG['combine_data'],
            target_col='logS' if config.DATA_CONFIG['secondary_file'] == 'logS.csv' else 'value'
        )

        # Split data
        logger.info("Splitting data...")
        X_train, X_val, X_test, y_train, y_val, y_test = split_data(
            X, y,
            test_size=config.DATA_CONFIG['test_size'],
            val_size=config.DATA_CONFIG['val_size'],
            random_state=config.DATA_CONFIG['random_state']
        )

        # Preprocess data on GPU
        logger.info("Preprocessing data on GPU...")
        X_train, X_val, X_test, y_train, y_val, y_test = preprocess_data_gpu(
            X_train, X_val, X_test, y_train, y_val, y_test,
            device=gpu_manager.device,
            scale=config.DATA_CONFIG['scale_features']
        )

        # Convert back to numpy/pandas for compatibility with existing code
        X_train = pd.DataFrame(X_train.cpu().numpy(), columns=X.columns)
        X_val = pd.DataFrame(X_val.cpu().numpy(), columns=X.columns)
        X_test = pd.DataFrame(X_test.cpu().numpy(), columns=X.columns)
        y_train = pd.Series(y_train.cpu().numpy())
        y_val = pd.Series(y_val.cpu().numpy())
        y_test = pd.Series(y_test.cpu().numpy())

        scaler = None  # Scaling done on GPU

        gpu_manager.log_memory_usage()
    else:
        # Use CPU-based data loading
        X, y = load_data(
            primary_file=config.DATA_CONFIG['primary_file'],
            secondary_file=config.DATA_CONFIG['secondary_file'],
            combine=config.DATA_CONFIG['combine_data'],
            target_col='logS' if config.DATA_CONFIG['secondary_file'] == 'logS.csv' else 'value'
        )

        # Split data
        logger.info("Splitting data...")
        X_train, X_val, X_test, y_train, y_val, y_test = split_data(
            X, y,
            test_size=config.DATA_CONFIG['test_size'],
            val_size=config.DATA_CONFIG['val_size'],
            random_state=config.DATA_CONFIG['random_state']
        )

        # Preprocess data
        logger.info("Preprocessing data...")
        X_train, X_val, X_test, scaler = preprocess_data(
            X_train, X_val, X_test,
            scale=config.DATA_CONFIG['scale_features'],
            handle_outliers=config.DATA_CONFIG['handle_outliers']
        )

    # Create client data for federated learning if needed
    client_data = None
    if args.fl_strategy is not None:
        logger.info(f"Creating client data for {args.n_clients} clients...")
        client_data = create_client_data(
            X_train, y_train,
            n_clients=args.n_clients,
            method=config.FL_CONFIG['client_split_method'],
            alpha=config.FL_CONFIG['dirichlet_alpha'],
            random_state=config.DATA_CONFIG['random_state']
        )

        # Plot client data distribution
        if config.OUTPUT_CONFIG['save_plots']:
            plot_client_data_distribution(
                client_data,
                title="Client Data Distribution",
                save_path=os.path.join(config.OUTPUT_CONFIG['plots_dir'], 'client_data_distribution.png'),
                show=False
            )

    # Run original model as baseline if requested
    original_model = None
    if args.original:
        original_model, original_metrics = run_original_model(
            X_train, y_train, X_val, y_val, X_test, y_test, gpu_manager
        )

    # Run the specified strategy
    if args.fl_strategy is not None and args.kd_strategy is not None:
        # Combined strategy
        model, metrics = run_combined_strategy(
            args.fl_strategy, args.kd_strategy,
            X_train, y_train, X_val, y_val, X_test, y_test, client_data, args, gpu_manager
        )
    elif args.fl_strategy is not None:
        # Federated learning strategy
        model, metrics = run_federated_learning(
            args.fl_strategy,
            X_train, y_train, X_val, y_val, X_test, y_test, client_data, args, gpu_manager
        )
    elif args.kd_strategy is not None:
        # Knowledge distillation strategy
        teacher = original_model
        if teacher is None:
            # If original model not run, create a default teacher
            logger.info("Creating default teacher model...")
            teacher = create_model(
                model_type='lgbm',
                params=config.MODEL_CONFIG['lgbm_teacher']
            )
            teacher.fit(X_train, y_train)

        model, metrics = run_knowledge_distillation(
            args.kd_strategy, teacher,
            X_train, y_train, X_val, y_val, X_test, y_test, args, gpu_manager
        )
    else:
        logger.error("No strategy specified. Use --fl_strategy and/or --kd_strategy.")
        return

    # Print final results
    logger.info(f"Final results for strategy:")
    for k, v in metrics.items():
        if k.startswith('test_'):
            logger.info(f"  {k}: {v:.4f}")

    logger.info(f"Results saved to {config.OUTPUT_CONFIG['results_dir']}")

if __name__ == "__main__":
    main()
