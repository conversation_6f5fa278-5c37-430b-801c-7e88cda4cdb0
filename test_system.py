#!/usr/bin/env python3
"""
Comprehensive System Test Script for FLKDDrug Platform
Windows-compatible testing suite for all strategies and components.
"""

import os
import sys
import time
import logging
import traceback
import subprocess
import platform
from datetime import datetime
import psutil
import numpy as np
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_results.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemTester:
    """Comprehensive system testing class."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        
    def log_system_info(self):
        """Log system information."""
        logger.info("=" * 60)
        logger.info("SYSTEM INFORMATION")
        logger.info("=" * 60)
        logger.info(f"Platform: {platform.platform()}")
        logger.info(f"Python Version: {sys.version}")
        logger.info(f"CPU Count: {psutil.cpu_count()}")
        logger.info(f"Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
        
        # Check GPU availability
        try:
            import torch
            if torch.cuda.is_available():
                logger.info(f"CUDA Available: Yes")
                logger.info(f"GPU Count: {torch.cuda.device_count()}")
                for i in range(torch.cuda.device_count()):
                    logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            else:
                logger.info("CUDA Available: No")
        except ImportError:
            logger.info("PyTorch not installed")
            
        logger.info("=" * 60)
    
    def test_dependencies(self):
        """Test all required dependencies."""
        logger.info("Testing Dependencies...")
        
        required_packages = [
            'numpy', 'pandas', 'scikit-learn', 'lightgbm', 
            'torch', 'matplotlib', 'seaborn', 'bayes_opt',
            'psutil', 'tqdm'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"✓ {package}")
            except ImportError:
                logger.error(f"✗ {package} - MISSING")
                missing_packages.append(package)
        
        self.test_results['dependencies'] = len(missing_packages) == 0
        return len(missing_packages) == 0
    
    def test_data_loading(self):
        """Test data loading functionality."""
        logger.info("Testing Data Loading...")
        
        try:
            from utils.data_utils import load_data, preprocess_data
            
            # Test data loading
            X, y = load_data()
            logger.info(f"✓ Data loaded: X shape {X.shape}, y shape {y.shape}")
            
            # Test preprocessing
            X_processed = preprocess_data(X)
            logger.info(f"✓ Data preprocessed: shape {X_processed.shape}")
            
            self.test_results['data_loading'] = True
            return True
            
        except Exception as e:
            logger.error(f"✗ Data loading failed: {e}")
            self.test_results['data_loading'] = False
            return False
    
    def test_gpu_functionality(self):
        """Test GPU functionality."""
        logger.info("Testing GPU Functionality...")
        
        try:
            from utils.gpu_utils import GPUManager
            import config
            
            gpu_manager = GPUManager(config.GPU_CONFIG)
            logger.info(f"✓ GPU Manager initialized")
            
            # Test GPU memory check
            if gpu_manager.is_available():
                memory_info = gpu_manager.get_memory_info()
                logger.info(f"✓ GPU Memory: {memory_info}")
            else:
                logger.info("! GPU not available, using CPU")
            
            self.test_results['gpu'] = True
            return True
            
        except Exception as e:
            logger.error(f"✗ GPU test failed: {e}")
            self.test_results['gpu'] = False
            return False
    
    def test_strategy_imports(self):
        """Test all strategy imports."""
        logger.info("Testing Strategy Imports...")
        
        strategies_to_test = [
            ('strategies.federated.fedavg', 'FedAvg'),
            ('strategies.federated.fedprox', 'FedProx'),
            ('strategies.federated.scaffold', 'SCAFFOLD'),
            ('strategies.federated.personalized_fl', 'PersonalizedFL'),
            ('strategies.federated.fedopt', 'FedOpt'),
            ('strategies.distillation.vanilla_kd', 'VanillaKD'),
            ('strategies.distillation.ensemble_kd', 'EnsembleKD'),
            ('strategies.distillation.progressive_kd', 'ProgressiveKD'),
            ('strategies.distillation.attention_kd', 'AttentionKD'),
            ('strategies.contrastive.simclr', 'SimCLR'),
            ('strategies.transfer.domain_adaptation', 'DomainAdaptation'),
        ]
        
        failed_imports = []
        for module_name, class_name in strategies_to_test:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                logger.info(f"✓ {module_name}.{class_name}")
            except Exception as e:
                logger.error(f"✗ {module_name}.{class_name}: {e}")
                failed_imports.append(f"{module_name}.{class_name}")
        
        self.test_results['strategy_imports'] = len(failed_imports) == 0
        return len(failed_imports) == 0
    
    def test_quick_training(self):
        """Test quick training with minimal parameters."""
        logger.info("Testing Quick Training...")
        
        try:
            # Test original model training
            cmd = [sys.executable, 'run_strategy.py', '--original', '--gpu', '0', '--epochs', '2']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✓ Original model training successful")
            else:
                logger.error(f"✗ Original model training failed: {result.stderr}")
                self.test_results['quick_training'] = False
                return False
            
            # Test federated learning
            cmd = [sys.executable, 'run_strategy.py', '--fl_strategy', 'fedavg', 
                   '--gpu', '0', '--n_rounds', '2', '--n_clients', '2']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✓ Federated learning training successful")
            else:
                logger.error(f"✗ Federated learning training failed: {result.stderr}")
                self.test_results['quick_training'] = False
                return False
            
            self.test_results['quick_training'] = True
            return True
            
        except subprocess.TimeoutExpired:
            logger.error("✗ Training test timed out")
            self.test_results['quick_training'] = False
            return False
        except Exception as e:
            logger.error(f"✗ Training test failed: {e}")
            self.test_results['quick_training'] = False
            return False
    
    def run_all_tests(self):
        """Run all tests."""
        logger.info("Starting Comprehensive System Tests...")
        logger.info("=" * 60)
        
        self.log_system_info()
        
        tests = [
            ('Dependencies', self.test_dependencies),
            ('Data Loading', self.test_data_loading),
            ('GPU Functionality', self.test_gpu_functionality),
            ('Strategy Imports', self.test_strategy_imports),
            ('Quick Training', self.test_quick_training),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Running {test_name} Test ---")
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✓ {test_name} PASSED")
                else:
                    logger.error(f"✗ {test_name} FAILED")
            except Exception as e:
                logger.error(f"✗ {test_name} FAILED with exception: {e}")
                logger.error(traceback.format_exc())
        
        # Final report
        logger.info("\n" + "=" * 60)
        logger.info("FINAL TEST REPORT")
        logger.info("=" * 60)
        logger.info(f"Tests Passed: {passed_tests}/{total_tests}")
        logger.info(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        logger.info(f"Total Time: {time.time() - self.start_time:.1f} seconds")
        
        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED! System is ready for use.")
        else:
            logger.warning("⚠️  Some tests failed. Check logs for details.")
        
        return passed_tests == total_tests

if __name__ == "__main__":
    tester = SystemTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
