"""
DQN (Deep Q-Network) for model selection and feature selection.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import logging
from typing import List, Tuple, Dict, Any, Optional
from collections import deque
import random
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.model_selection import cross_val_score
import time

logger = logging.getLogger(__name__)


class DQNNetwork(nn.Module):
    """Deep Q-Network for decision making."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 128]):
        super(DQNNetwork, self).__init__()
        
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, action_dim))
        self.network = nn.Sequential(*layers)
        
    def forward(self, state):
        return self.network(state)


class ReplayBuffer:
    """Experience replay buffer for DQN."""
    
    def __init__(self, capacity: int = 10000):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """Add experience to buffer."""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int):
        """Sample batch from buffer."""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)


class DQNAgent:
    """DQN agent for decision making."""
    
    def __init__(self, state_dim: int, action_dim: int, lr: float = 1e-3,
                 gamma: float = 0.99, epsilon: float = 1.0, epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01, buffer_size: int = 10000,
                 batch_size: int = 32, target_update: int = 100,
                 device: torch.device = None):
        """
        Initialize DQN agent.
        
        Args:
            state_dim: State dimension
            action_dim: Action dimension
            lr: Learning rate
            gamma: Discount factor
            epsilon: Initial exploration rate
            epsilon_decay: Epsilon decay rate
            epsilon_min: Minimum epsilon
            buffer_size: Replay buffer size
            batch_size: Training batch size
            target_update: Target network update frequency
            device: Device to use
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.batch_size = batch_size
        self.target_update = target_update
        self.device = device if device is not None else torch.device('cpu')
        
        # Networks
        self.q_network = DQNNetwork(state_dim, action_dim).to(self.device)
        self.target_network = DQNNetwork(state_dim, action_dim).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=lr)
        
        # Replay buffer
        self.memory = ReplayBuffer(buffer_size)
        
        # Update target network
        self.update_target_network()
        
        self.step_count = 0
    
    def update_target_network(self):
        """Update target network."""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def get_action(self, state, training: bool = True):
        """Get action using epsilon-greedy policy."""
        if training and random.random() < self.epsilon:
            return random.randint(0, self.action_dim - 1)
        
        state = torch.tensor(state, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            q_values = self.q_network(state)
        
        return q_values.argmax().item()
    
    def store_experience(self, state, action, reward, next_state, done):
        """Store experience in replay buffer."""
        self.memory.push(state, action, reward, next_state, done)
    
    def train(self):
        """Train the DQN."""
        if len(self.memory) < self.batch_size:
            return
        
        # Sample batch
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)
        
        # Convert to tensors
        states = torch.tensor(states, dtype=torch.float32, device=self.device)
        actions = torch.tensor(actions, dtype=torch.long, device=self.device)
        rewards = torch.tensor(rewards, dtype=torch.float32, device=self.device)
        next_states = torch.tensor(next_states, dtype=torch.float32, device=self.device)
        dones = torch.tensor(dones, dtype=torch.bool, device=self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        # Update epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        # Update target network
        self.step_count += 1
        if self.step_count % self.target_update == 0:
            self.update_target_network()


class DQNModelSelector(BaseEstimator):
    """DQN-based model selector."""
    
    def __init__(self, model_classes: List[Any], n_episodes: int = 100,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize DQN model selector.
        
        Args:
            model_classes: List of model classes to choose from
            n_episodes: Number of training episodes
            device: Device to use
            random_state: Random seed
        """
        self.model_classes = model_classes
        self.n_episodes = n_episodes
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        # DQN parameters
        self.state_dim = 10  # Problem features
        self.action_dim = len(model_classes)
        
        # Initialize DQN agent
        self.agent = DQNAgent(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            device=self.device
        )
        
        self.best_model_idx = 0
        self.best_score = -np.inf
        self.history = []
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
        random.seed(random_state)
    
    def _get_state(self, X, y):
        """Extract state features from data."""
        state = np.array([
            X.shape[0],  # Number of samples
            X.shape[1],  # Number of features
            np.mean(X.values if hasattr(X, 'values') else X),
            np.std(X.values if hasattr(X, 'values') else X),
            np.mean(y.values if hasattr(y, 'values') else y),
            np.std(y.values if hasattr(y, 'values') else y),
            np.corrcoef(X.values.T if hasattr(X, 'values') else X.T).mean(),
            np.min(X.values if hasattr(X, 'values') else X),
            np.max(X.values if hasattr(X, 'values') else X),
            len(np.unique(y.values if hasattr(y, 'values') else y)) / len(y)
        ])
        
        # Normalize state
        state = (state - np.mean(state)) / (np.std(state) + 1e-8)
        return state
    
    def _evaluate_model(self, model_idx, X, y):
        """Evaluate a model using cross-validation."""
        try:
            model_class = self.model_classes[model_idx]
            
            # Create model with default parameters
            if hasattr(model_class, '__name__') and 'Neural' in model_class.__name__:
                model = model_class(device=self.device, epochs=50)  # Reduced for speed
            else:
                model = model_class()
            
            # Evaluate using cross-validation
            scores = cross_val_score(model, X, y, cv=3, scoring='neg_mean_absolute_error')
            score = np.mean(scores)
            
            return score
        except Exception as e:
            logger.warning(f"Error evaluating model {model_idx}: {e}")
            return -np.inf
    
    def fit(self, X, y):
        """Train DQN to select best model."""
        logger.info(f"Training DQN model selector for {self.n_episodes} episodes")
        
        state = self._get_state(X, y)
        
        for episode in range(self.n_episodes):
            current_state = state.copy()
            episode_reward = 0
            
            # Select action (model)
            action = self.agent.get_action(current_state, training=True)
            
            # Evaluate selected model
            score = self._evaluate_model(action, X, y)
            reward = score  # Use negative MAE as reward
            
            # Update best model
            if score > self.best_score:
                self.best_score = score
                self.best_model_idx = action
            
            # Next state (add some noise for exploration)
            next_state = state + np.random.normal(0, 0.1, size=state.shape)
            done = True  # Each episode is one step
            
            # Store experience
            self.agent.store_experience(current_state, action, reward, next_state, done)
            
            # Train agent
            self.agent.train()
            
            episode_reward = reward
            self.history.append(episode_reward)
            
            if (episode + 1) % 20 == 0:
                logger.info(f"Episode {episode + 1}/{self.n_episodes}, "
                           f"Reward: {episode_reward:.4f}, "
                           f"Best Score: {self.best_score:.4f}, "
                           f"Best Model: {self.model_classes[self.best_model_idx].__name__}")
        
        logger.info(f"DQN model selection completed. Best model: {self.model_classes[self.best_model_idx].__name__}")
        return self
    
    def get_best_model(self):
        """Get the best selected model class."""
        return self.model_classes[self.best_model_idx]


class DQNFeatureSelector(BaseEstimator, TransformerMixin):
    """DQN-based feature selector."""
    
    def __init__(self, max_features: int = None, n_episodes: int = 100,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize DQN feature selector.
        
        Args:
            max_features: Maximum number of features to select
            n_episodes: Number of training episodes
            device: Device to use
            random_state: Random seed
        """
        self.max_features = max_features
        self.n_episodes = n_episodes
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        self.selected_features = None
        self.feature_scores = None
        self.history = []
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
        random.seed(random_state)
    
    def _get_state(self, X, y, current_features):
        """Get state representation."""
        if len(current_features) == 0:
            return np.zeros(10)
        
        X_selected = X.iloc[:, current_features] if hasattr(X, 'iloc') else X[:, current_features]
        
        state = np.array([
            len(current_features),  # Number of selected features
            X_selected.shape[1],    # Same as above
            np.mean(X_selected.values if hasattr(X_selected, 'values') else X_selected),
            np.std(X_selected.values if hasattr(X_selected, 'values') else X_selected),
            np.mean(y.values if hasattr(y, 'values') else y),
            np.std(y.values if hasattr(y, 'values') else y),
            np.corrcoef(X_selected.values.T if hasattr(X_selected, 'values') else X_selected.T).mean() if X_selected.shape[1] > 1 else 0,
            np.min(X_selected.values if hasattr(X_selected, 'values') else X_selected),
            np.max(X_selected.values if hasattr(X_selected, 'values') else X_selected),
            len(current_features) / X.shape[1]  # Feature selection ratio
        ])
        
        # Handle NaN values
        state = np.nan_to_num(state)
        
        # Normalize state
        state = (state - np.mean(state)) / (np.std(state) + 1e-8)
        return state
    
    def _evaluate_features(self, X, y, features):
        """Evaluate feature subset."""
        if len(features) == 0:
            return -np.inf
        
        try:
            X_selected = X.iloc[:, features] if hasattr(X, 'iloc') else X[:, features]
            
            # Use a simple model for evaluation
            from sklearn.linear_model import Ridge
            model = Ridge()
            
            scores = cross_val_score(model, X_selected, y, cv=3, scoring='neg_mean_absolute_error')
            score = np.mean(scores)
            
            # Penalize too many features
            penalty = len(features) / X.shape[1] * 0.1
            return score - penalty
            
        except Exception as e:
            logger.warning(f"Error evaluating features {features}: {e}")
            return -np.inf
    
    def fit(self, X, y):
        """Train DQN to select features."""
        logger.info(f"Training DQN feature selector for {self.n_episodes} episodes")
        
        n_features = X.shape[1]
        max_features = self.max_features if self.max_features is not None else min(n_features, 20)
        
        # Initialize DQN agent
        agent = DQNAgent(
            state_dim=10,
            action_dim=n_features,
            device=self.device
        )
        
        best_features = []
        best_score = -np.inf
        
        for episode in range(self.n_episodes):
            current_features = []
            episode_reward = 0
            
            for step in range(max_features):
                # Get current state
                state = self._get_state(X, y, current_features)
                
                # Select action (feature to add)
                action = agent.get_action(state, training=True)
                
                # Add feature if not already selected
                if action not in current_features:
                    current_features.append(action)
                
                # Evaluate current feature set
                score = self._evaluate_features(X, y, current_features)
                reward = score
                
                # Next state
                next_state = self._get_state(X, y, current_features)
                done = (step == max_features - 1)
                
                # Store experience
                agent.store_experience(state, action, reward, next_state, done)
                
                # Train agent
                agent.train()
                
                episode_reward += reward
            
            # Update best features
            final_score = self._evaluate_features(X, y, current_features)
            if final_score > best_score:
                best_score = final_score
                best_features = current_features.copy()
            
            self.history.append(episode_reward)
            
            if (episode + 1) % 20 == 0:
                logger.info(f"Episode {episode + 1}/{self.n_episodes}, "
                           f"Reward: {episode_reward:.4f}, "
                           f"Best Score: {best_score:.4f}, "
                           f"Features: {len(best_features)}")
        
        self.selected_features = best_features
        logger.info(f"DQN feature selection completed. Selected {len(self.selected_features)} features")
        return self
    
    def transform(self, X):
        """Transform data using selected features."""
        if self.selected_features is None:
            raise ValueError("Feature selector has not been fitted yet")
        
        if hasattr(X, 'iloc'):
            return X.iloc[:, self.selected_features]
        else:
            return X[:, self.selected_features]
    
    def fit_transform(self, X, y=None):
        """Fit and transform data."""
        return self.fit(X, y).transform(X)
