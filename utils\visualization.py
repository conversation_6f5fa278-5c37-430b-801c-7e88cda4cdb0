"""
Visualization utilities for the FLKDDrug project.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import os
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.manifold import TSNE
import logging

logger = logging.getLogger(__name__)

def set_plotting_style():
    """Set consistent plotting style for all visualizations."""
    sns.set(style="whitegrid")
    plt.rcParams['figure.figsize'] = (10, 6)
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    plt.rcParams['figure.titlesize'] = 18

def plot_actual_vs_predicted(y_true, y_pred, title="Actual vs Predicted", 
                            save_path=None, show=True, color='blue', 
                            add_metrics=True, xlim=None, ylim=None):
    """
    Plot actual vs predicted values.
    
    Args:
        y_true: True target values
        y_pred: Predicted target values
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
        color: Color of scatter points
        add_metrics: Whether to add metrics to the plot
        xlim: x-axis limits
        ylim: y-axis limits
    """
    set_plotting_style()
    
    plt.figure(figsize=(10, 8))
    plt.scatter(y_true, y_pred, alpha=0.6, color=color)
    
    # Add perfect prediction line
    min_val = min(min(y_true), min(y_pred))
    max_val = max(max(y_true), max(y_pred))
    buffer = (max_val - min_val) * 0.05
    
    if xlim is None:
        xlim = (min_val - buffer, max_val + buffer)
    if ylim is None:
        ylim = (min_val - buffer, max_val + buffer)
    
    plt.plot([min_val - buffer, max_val + buffer], 
             [min_val - buffer, max_val + buffer], 
             'k--', lw=2)
    
    plt.xlim(xlim)
    plt.ylim(ylim)
    plt.xlabel('Actual Values')
    plt.ylabel('Predicted Values')
    plt.title(title)
    
    if add_metrics:
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        plt.annotate(f'MAE: {mae:.4f}\nR²: {r2:.4f}', 
                    xy=(0.05, 0.95), xycoords='axes fraction',
                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()

def plot_residuals(y_true, y_pred, title="Residual Plot", 
                  save_path=None, show=True, color='blue'):
    """
    Plot residuals (actual - predicted).
    
    Args:
        y_true: True target values
        y_pred: Predicted target values
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
        color: Color of scatter points
    """
    set_plotting_style()
    
    residuals = y_true - y_pred
    
    plt.figure(figsize=(10, 6))
    plt.scatter(y_pred, residuals, alpha=0.6, color=color)
    plt.axhline(y=0, color='r', linestyle='-', lw=2)
    
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title(title)
    
    # Add residual statistics
    mean_residual = np.mean(residuals)
    std_residual = np.std(residuals)
    plt.annotate(f'Mean: {mean_residual:.4f}\nStd: {std_residual:.4f}', 
                xy=(0.05, 0.95), xycoords='axes fraction',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()

def plot_feature_importance(model, feature_names, top_n=20, title="Feature Importance", 
                           save_path=None, show=True):
    """
    Plot feature importance for tree-based models.
    
    Args:
        model: Trained model with feature_importances_ attribute
        feature_names: List of feature names
        top_n: Number of top features to show
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
    """
    set_plotting_style()
    
    # Check if model has feature_importances_ attribute
    if not hasattr(model, 'feature_importances_'):
        logger.warning("Model does not have feature_importances_ attribute")
        return
    
    # Get feature importances
    importances = model.feature_importances_
    
    # Create DataFrame for plotting
    importance_df = pd.DataFrame({
        'Feature': feature_names,
        'Importance': importances
    })
    
    # Sort by importance and get top N
    importance_df = importance_df.sort_values('Importance', ascending=False).head(top_n)
    
    plt.figure(figsize=(10, 8))
    sns.barplot(x='Importance', y='Feature', data=importance_df)
    plt.title(title)
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()
    
    return importance_df

def plot_training_history(history, title="Training History", 
                         save_path=None, show=True):
    """
    Plot training history (loss curves).
    
    Args:
        history: Dictionary with training metrics
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
    """
    set_plotting_style()
    
    plt.figure(figsize=(12, 6))
    
    for metric, values in history.items():
        plt.plot(values, label=metric)
    
    plt.xlabel('Epoch')
    plt.ylabel('Metric Value')
    plt.title(title)
    plt.legend()
    plt.grid(True)
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()

def plot_strategy_comparison(results_df, metric='test_MAE', 
                            title="Strategy Comparison", 
                            save_path=None, show=True):
    """
    Plot comparison of different strategies.
    
    Args:
        results_df: DataFrame with results for different strategies
        metric: Metric to compare
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
    """
    set_plotting_style()
    
    # Sort by the specified metric
    if metric.startswith('test_MAE') or metric.startswith('test_MSE') or metric.startswith('test_RMSE'):
        # Lower is better for these metrics
        sorted_df = results_df.sort_values(by=metric)
    else:
        # Higher is better for R2, EVS
        sorted_df = results_df.sort_values(by=metric, ascending=False)
    
    plt.figure(figsize=(12, 8))
    
    # Create bar plot
    ax = sns.barplot(x=sorted_df.index, y=sorted_df[metric])
    
    # Add value labels on top of bars
    for i, v in enumerate(sorted_df[metric]):
        ax.text(i, v + 0.01, f'{v:.4f}', ha='center')
    
    plt.xlabel('Strategy')
    plt.ylabel(metric)
    plt.title(title)
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()

def plot_client_data_distribution(client_data, title="Client Data Distribution", 
                                 save_path=None, show=True):
    """
    Plot distribution of data across clients.
    
    Args:
        client_data: List of (X, y) tuples for each client
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
    """
    set_plotting_style()
    
    plt.figure(figsize=(12, 6))
    
    # Plot histogram of target values for each client
    for i, (_, y) in enumerate(client_data):
        sns.kdeplot(y, label=f'Client {i+1}')
    
    plt.xlabel('Target Value')
    plt.ylabel('Density')
    plt.title(title)
    plt.legend()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()

def plot_feature_tsne(X, y, title="t-SNE Visualization", 
                     save_path=None, show=True, perplexity=30, n_iter=1000):
    """
    Plot t-SNE visualization of features colored by target.
    
    Args:
        X: Features
        y: Target values
        title: Plot title
        save_path: Path to save the plot
        show: Whether to display the plot
        perplexity: t-SNE perplexity parameter
        n_iter: Number of iterations for t-SNE
    """
    set_plotting_style()
    
    # Apply t-SNE
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42)
    X_tsne = tsne.fit_transform(X)
    
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(X_tsne[:, 0], X_tsne[:, 1], c=y, cmap='viridis', alpha=0.6)
    plt.colorbar(scatter, label='Target Value')
    plt.xlabel('t-SNE Component 1')
    plt.ylabel('t-SNE Component 2')
    plt.title(title)
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()
