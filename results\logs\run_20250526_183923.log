2025-05-26 18:39:29,275 - utils.gpu_utils - INFO - Using GPU: NVIDIA GeForce GTX 1050
2025-05-26 18:39:29,275 - utils.gpu_utils - INFO - GPU Memory: 4.3 GB
2025-05-26 18:39:29,276 - utils.gpu_utils - INFO - Applying Windows-specific GPU optimizations
2025-05-26 18:39:29,276 - utils.gpu_utils - INFO - Enabled Flash SDP for Windows
2025-05-26 18:39:29,277 - utils.gpu_utils - INFO - Mixed precision training enabled
2025-05-26 18:39:29,277 - utils.gpu_utils - INFO - CUDNN benchmark mode enabled
2025-05-26 18:39:29,277 - utils.gpu_utils - INFO - TF32 enabled for Ampere GPUs
2025-05-26 18:39:29,278 - __main__ - INFO - Using GPU 0: cuda:0
2025-05-26 18:39:29,278 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.00GB, Free: 4.29GB, Utilization: 0.0%
2025-05-26 18:39:29,320 - __main__ - INFO - Loading data...
2025-05-26 18:39:29,751 - utils.data_utils - INFO - Loaded primary data from logS_des.csv: 14594 samples, 199 features
2025-05-26 18:39:29,752 - __main__ - INFO - Splitting data...
2025-05-26 18:39:29,820 - utils.data_utils - INFO - Data split: train=9340, val=2335, test=2919
2025-05-26 18:39:29,822 - __main__ - INFO - Preprocessing data...
2025-05-26 18:39:29,884 - utils.data_utils - INFO - Found 62 NaN values in training data
2025-05-26 18:39:30,122 - utils.data_utils - INFO - NaN values filled with median values
2025-05-26 18:39:30,298 - utils.data_utils - INFO - Data scaled using RobustScaler
2025-05-26 18:39:30,301 - __main__ - INFO - Creating client data for 5 clients...
2025-05-26 18:39:30,344 - utils.data_utils - INFO - Client 1: 1868 samples, mean(y)=-2.7965, std(y)=2.1408
2025-05-26 18:39:30,346 - utils.data_utils - INFO - Client 2: 1868 samples, mean(y)=-2.8348, std(y)=2.1821
2025-05-26 18:39:30,347 - utils.data_utils - INFO - Client 3: 1868 samples, mean(y)=-2.8803, std(y)=2.2216
2025-05-26 18:39:30,347 - utils.data_utils - INFO - Client 4: 1868 samples, mean(y)=-2.8153, std(y)=2.2115
2025-05-26 18:39:30,348 - utils.data_utils - INFO - Client 5: 1868 samples, mean(y)=-3.0071, std(y)=2.2530
2025-05-26 18:39:33,219 - utils.visualization - INFO - Plot saved to results/plots\client_data_distribution.png
2025-05-26 18:39:33,219 - __main__ - INFO - Running original LightGBM model with Bayesian optimization (replicating original-model_save.py)...
2025-05-26 18:43:26,960 - __main__ - INFO - Best score: -0.622
2025-05-26 18:43:26,960 - __main__ - INFO - Best parameters: {'colsample_bynode': 0.48969969825279, 'colsample_bytree': 0.5644500931858639, 'learning_rate': 0.08617718010173743, 'n_estimators': 864.0675448038132, 'num_leaves': 28.729359730923406, 'reg_alpha': 1.2579751732344184, 'reg_lambda': 4.871114454720413, 'subsample': 0.8231506064041114, 'subsample_freq': 2.2184844380486153}
2025-05-26 18:43:48,014 - __main__ - INFO - Original LightGBM model training completed successfully
2025-05-26 18:43:48,196 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.2464, train_MSE: 0.1142, train_RMSE: 0.3379, train_R2: 0.9765, train_EVS: 0.9765, train_MAPE: 71685760883.3817
2025-05-26 18:43:48,249 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.2474, val_MSE: 0.1198, val_RMSE: 0.3461, val_R2: 0.9758, val_EVS: 0.9758, val_MAPE: 659209941284.7767
2025-05-26 18:43:48,319 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.5912, test_MSE: 0.7777, test_RMSE: 0.8818, test_R2: 0.8321, test_EVS: 0.8321, test_MAPE: 0.9511
2025-05-26 18:43:48,322 - utils.evaluation - INFO - Results logged to results/logs\original_20250526_184348.json
2025-05-26 18:43:48,522 - utils.model_utils - INFO - Model saved to results/models\original_model.pkl
2025-05-26 18:43:49,240 - utils.visualization - INFO - Plot saved to results/plots\original_actual_vs_pred.png
2025-05-26 18:43:49,845 - utils.visualization - INFO - Plot saved to results/plots\original_residuals.png
2025-05-26 18:43:50,511 - utils.visualization - INFO - Plot saved to results/plots\original_feature_importance.png
2025-05-26 18:43:50,514 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 18:43:50,514 - strategies.federated.fedavg - INFO - Starting FedAvg training with 5 clients and 15 rounds
2025-05-26 18:43:50,518 - strategies.federated.fedavg - INFO - Round 1/15
2025-05-26 18:44:19,499 - strategies.federated.fedavg - INFO - Round 1 completed in 28.98s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 18:44:19,499 - strategies.federated.fedavg - INFO - Round 2/15
2025-05-26 18:44:48,589 - strategies.federated.fedavg - INFO - Round 2 completed in 29.09s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:44:48,589 - strategies.federated.fedavg - INFO - Round 3/15
2025-05-26 18:45:17,284 - strategies.federated.fedavg - INFO - Round 3 completed in 28.69s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 18:45:17,284 - strategies.federated.fedavg - INFO - Round 4/15
2025-05-26 18:45:46,155 - strategies.federated.fedavg - INFO - Round 4 completed in 28.87s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 18:45:46,157 - strategies.federated.fedavg - INFO - Round 5/15
2025-05-26 18:46:14,986 - strategies.federated.fedavg - INFO - Round 5 completed in 28.83s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:46:14,986 - strategies.federated.fedavg - INFO - Round 6/15
2025-05-26 18:46:43,677 - strategies.federated.fedavg - INFO - Round 6 completed in 28.69s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 18:46:43,677 - strategies.federated.fedavg - INFO - Round 7/15
2025-05-26 18:47:12,278 - strategies.federated.fedavg - INFO - Round 7 completed in 28.60s. Global loss: 0.6687, Avg client loss: 0.7356
2025-05-26 18:47:12,278 - strategies.federated.fedavg - INFO - Round 8/15
2025-05-26 18:47:46,073 - strategies.federated.fedavg - INFO - Round 8 completed in 33.79s. Global loss: 0.6688, Avg client loss: 0.7357
2025-05-26 18:47:46,073 - strategies.federated.fedavg - INFO - Round 9/15
2025-05-26 18:48:24,203 - strategies.federated.fedavg - INFO - Round 9 completed in 38.13s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 18:48:24,205 - strategies.federated.fedavg - INFO - Round 10/15
2025-05-26 18:48:57,976 - strategies.federated.fedavg - INFO - Round 10 completed in 33.77s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:48:57,977 - strategies.federated.fedavg - INFO - Round 11/15
2025-05-26 18:49:28,311 - strategies.federated.fedavg - INFO - Round 11 completed in 30.33s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:49:28,311 - strategies.federated.fedavg - INFO - Round 12/15
2025-05-26 18:49:57,273 - strategies.federated.fedavg - INFO - Round 12 completed in 28.96s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:49:57,273 - strategies.federated.fedavg - INFO - Round 13/15
2025-05-26 18:50:27,334 - strategies.federated.fedavg - INFO - Round 13 completed in 30.06s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:50:27,334 - strategies.federated.fedavg - INFO - Round 14/15
2025-05-26 18:50:56,015 - strategies.federated.fedavg - INFO - Round 14 completed in 28.68s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:50:56,015 - strategies.federated.fedavg - INFO - Round 15/15
2025-05-26 18:51:24,496 - strategies.federated.fedavg - INFO - Round 15 completed in 28.48s. Global loss: 0.6688, Avg client loss: 0.7359
2025-05-26 18:51:24,497 - strategies.federated.fedavg - INFO - FedAvg training completed in 453.98s
2025-05-26 18:51:25,222 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210914333528.7505
2025-05-26 18:51:25,427 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6688, val_MSE: 0.9354, val_RMSE: 0.9672, val_R2: 0.8108, val_EVS: 0.8108, val_MAPE: 1344549813399.0596
2025-05-26 18:51:25,685 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9281, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1350
2025-05-26 18:51:25,687 - utils.evaluation - INFO - Results logged to results/logs\fedavg_20250526_185125.json
2025-05-26 18:51:27,267 - utils.model_utils - INFO - Model saved to results/models\fedavg_model.pkl
2025-05-26 18:51:28,544 - utils.visualization - INFO - Plot saved to results/plots\fedavg_actual_vs_pred.png
2025-05-26 18:51:29,229 - utils.visualization - INFO - Plot saved to results/plots\fedavg_residuals.png
2025-05-26 18:51:29,229 - __main__ - INFO - Running federated learning strategy: fedprox
2025-05-26 18:51:29,229 - strategies.federated.fedprox - INFO - Starting FedProx training with 5 clients, 15 rounds, and mu=0.01
2025-05-26 18:51:29,235 - strategies.federated.fedprox - INFO - Round 1/15
2025-05-26 18:51:59,961 - strategies.federated.fedprox - INFO - Round 1 completed in 30.73s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:51:59,962 - strategies.federated.fedprox - INFO - Round 2/15
2025-05-26 18:52:30,822 - strategies.federated.fedprox - INFO - Round 2 completed in 30.86s. Global loss: 0.6713, Avg client loss: 0.7365
2025-05-26 18:52:30,822 - strategies.federated.fedprox - INFO - Round 3/15
2025-05-26 18:53:02,269 - strategies.federated.fedprox - INFO - Round 3 completed in 31.45s. Global loss: 0.6692, Avg client loss: 0.7358
2025-05-26 18:53:02,269 - strategies.federated.fedprox - INFO - Round 4/15
2025-05-26 18:53:33,946 - strategies.federated.fedprox - INFO - Round 4 completed in 31.68s. Global loss: 0.6708, Avg client loss: 0.7360
2025-05-26 18:53:33,947 - strategies.federated.fedprox - INFO - Round 5/15
2025-05-26 18:54:04,968 - strategies.federated.fedprox - INFO - Round 5 completed in 31.02s. Global loss: 0.6705, Avg client loss: 0.7352
2025-05-26 18:54:04,969 - strategies.federated.fedprox - INFO - Round 6/15
2025-05-26 18:54:36,277 - strategies.federated.fedprox - INFO - Round 6 completed in 31.31s. Global loss: 0.6706, Avg client loss: 0.7373
2025-05-26 18:54:36,277 - strategies.federated.fedprox - INFO - Round 7/15
2025-05-26 18:55:07,596 - strategies.federated.fedprox - INFO - Round 7 completed in 31.32s. Global loss: 0.6724, Avg client loss: 0.7375
2025-05-26 18:55:07,596 - strategies.federated.fedprox - INFO - Round 8/15
2025-05-26 18:55:38,924 - strategies.federated.fedprox - INFO - Round 8 completed in 31.33s. Global loss: 0.6696, Avg client loss: 0.7355
2025-05-26 18:55:38,924 - strategies.federated.fedprox - INFO - Round 9/15
2025-05-26 18:56:10,551 - strategies.federated.fedprox - INFO - Round 9 completed in 31.63s. Global loss: 0.6714, Avg client loss: 0.7368
2025-05-26 18:56:10,551 - strategies.federated.fedprox - INFO - Round 10/15
2025-05-26 18:56:42,915 - strategies.federated.fedprox - INFO - Round 10 completed in 32.36s. Global loss: 0.6699, Avg client loss: 0.7366
2025-05-26 18:56:42,915 - strategies.federated.fedprox - INFO - Round 11/15
2025-05-26 18:57:14,335 - strategies.federated.fedprox - INFO - Round 11 completed in 31.42s. Global loss: 0.6706, Avg client loss: 0.7376
2025-05-26 18:57:14,336 - strategies.federated.fedprox - INFO - Round 12/15
2025-05-26 18:57:46,123 - strategies.federated.fedprox - INFO - Round 12 completed in 31.79s. Global loss: 0.6708, Avg client loss: 0.7353
2025-05-26 18:57:46,124 - strategies.federated.fedprox - INFO - Round 13/15
2025-05-26 18:58:17,230 - strategies.federated.fedprox - INFO - Round 13 completed in 31.11s. Global loss: 0.6713, Avg client loss: 0.7387
2025-05-26 18:58:17,231 - strategies.federated.fedprox - INFO - Round 14/15
2025-05-26 18:58:48,441 - strategies.federated.fedprox - INFO - Round 14 completed in 31.21s. Global loss: 0.6705, Avg client loss: 0.7370
2025-05-26 18:58:48,441 - strategies.federated.fedprox - INFO - Round 15/15
2025-05-26 18:59:22,616 - strategies.federated.fedprox - INFO - Round 15 completed in 34.17s. Global loss: 0.6697, Avg client loss: 0.7357
2025-05-26 18:59:22,617 - strategies.federated.fedprox - INFO - FedProx training completed in 473.39s
2025-05-26 18:59:23,418 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5639, train_MSE: 0.6539, train_RMSE: 0.8086, train_R2: 0.8653, train_EVS: 0.8653, train_MAPE: 216979495302.2939
2025-05-26 18:59:23,636 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6697, val_MSE: 0.9363, val_RMSE: 0.9676, val_R2: 0.8107, val_EVS: 0.8107, val_MAPE: 1552594318548.3279
2025-05-26 18:59:23,900 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9293, test_RMSE: 0.9640, test_R2: 0.7993, test_EVS: 0.7994, test_MAPE: 1.1882
2025-05-26 18:59:23,914 - utils.evaluation - INFO - Results logged to results/logs\fedprox_20250526_185923.json
2025-05-26 18:59:24,194 - utils.model_utils - INFO - Model saved to results/models\fedprox_model.pkl
2025-05-26 18:59:25,031 - utils.visualization - INFO - Plot saved to results/plots\fedprox_actual_vs_pred.png
2025-05-26 18:59:25,716 - utils.visualization - INFO - Plot saved to results/plots\fedprox_residuals.png
2025-05-26 18:59:25,721 - __main__ - INFO - Running federated learning strategy: scaffold
2025-05-26 18:59:25,722 - strategies.federated.scaffold - INFO - Starting SCAFFOLD training with 5 clients and 15 rounds
2025-05-26 18:59:25,724 - strategies.federated.scaffold - INFO - Round 1/15
2025-05-26 18:59:59,184 - strategies.federated.scaffold - INFO - Round 1 completed in 33.46s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:59:59,184 - strategies.federated.scaffold - INFO - Round 2/15
2025-05-26 19:00:31,638 - strategies.federated.scaffold - INFO - Round 2 completed in 32.45s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:00:31,638 - strategies.federated.scaffold - INFO - Round 3/15
2025-05-26 19:01:05,127 - strategies.federated.scaffold - INFO - Round 3 completed in 33.49s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:01:05,128 - strategies.federated.scaffold - INFO - Round 4/15
2025-05-26 19:01:39,269 - strategies.federated.scaffold - INFO - Round 4 completed in 34.14s. Global loss: 0.6688, Avg client loss: 0.7357
2025-05-26 19:01:39,269 - strategies.federated.scaffold - INFO - Round 5/15
2025-05-26 19:02:12,006 - strategies.federated.scaffold - INFO - Round 5 completed in 32.74s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 19:02:12,006 - strategies.federated.scaffold - INFO - Round 6/15
2025-05-26 19:02:44,930 - strategies.federated.scaffold - INFO - Round 6 completed in 32.92s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 19:02:44,930 - strategies.federated.scaffold - INFO - Round 7/15
2025-05-26 19:03:17,937 - strategies.federated.scaffold - INFO - Round 7 completed in 33.01s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:03:17,937 - strategies.federated.scaffold - INFO - Round 8/15
2025-05-26 19:03:50,493 - strategies.federated.scaffold - INFO - Round 8 completed in 32.56s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:03:50,494 - strategies.federated.scaffold - INFO - Round 9/15
2025-05-26 19:04:19,700 - strategies.federated.scaffold - INFO - Round 9 completed in 29.21s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:04:19,700 - strategies.federated.scaffold - INFO - Round 10/15
2025-05-26 19:04:48,848 - strategies.federated.scaffold - INFO - Round 10 completed in 29.15s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:04:48,849 - strategies.federated.scaffold - INFO - Round 11/15
2025-05-26 19:05:19,010 - strategies.federated.scaffold - INFO - Round 11 completed in 30.16s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:05:19,011 - strategies.federated.scaffold - INFO - Round 12/15
2025-05-26 19:05:49,380 - strategies.federated.scaffold - INFO - Round 12 completed in 30.37s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:05:49,381 - strategies.federated.scaffold - INFO - Round 13/15
2025-05-26 19:06:20,650 - strategies.federated.scaffold - INFO - Round 13 completed in 31.27s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 19:06:20,651 - strategies.federated.scaffold - INFO - Round 14/15
2025-05-26 19:06:54,347 - strategies.federated.scaffold - INFO - Round 14 completed in 33.70s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:06:54,347 - strategies.federated.scaffold - INFO - Round 15/15
2025-05-26 19:07:23,009 - strategies.federated.scaffold - INFO - Round 15 completed in 28.66s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:07:23,010 - strategies.federated.scaffold - INFO - SCAFFOLD training completed in 477.29s
2025-05-26 19:07:23,782 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210691290732.0958
2025-05-26 19:07:23,995 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9670, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1337487212945.0444
2025-05-26 19:07:24,257 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9280, test_RMSE: 0.9633, test_R2: 0.7996, test_EVS: 0.7997, test_MAPE: 1.1392
2025-05-26 19:07:24,260 - utils.evaluation - INFO - Results logged to results/logs\scaffold_20250526_190724.json
2025-05-26 19:07:24,531 - utils.model_utils - INFO - Model saved to results/models\scaffold_model.pkl
2025-05-26 19:07:25,233 - utils.visualization - INFO - Plot saved to results/plots\scaffold_actual_vs_pred.png
2025-05-26 19:07:25,807 - utils.visualization - INFO - Plot saved to results/plots\scaffold_residuals.png
2025-05-26 19:07:25,814 - __main__ - INFO - Running federated learning strategy: personalized_fl
2025-05-26 19:07:25,814 - strategies.federated.personalized_fl - INFO - Starting PersonalizedFL training with 5 clients, 15 rounds, and alpha=0.5
2025-05-26 19:07:25,816 - strategies.federated.personalized_fl - INFO - Round 1/15
2025-05-26 19:07:54,462 - strategies.federated.personalized_fl - INFO - Round 1 completed in 28.65s. Global loss: 0.6688, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:07:54,462 - strategies.federated.personalized_fl - INFO - Round 2/15
2025-05-26 19:08:23,042 - strategies.federated.personalized_fl - INFO - Round 2 completed in 28.58s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:08:23,042 - strategies.federated.personalized_fl - INFO - Round 3/15
2025-05-26 19:08:51,761 - strategies.federated.personalized_fl - INFO - Round 3 completed in 28.72s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:08:51,762 - strategies.federated.personalized_fl - INFO - Round 4/15
2025-05-26 19:09:20,843 - strategies.federated.personalized_fl - INFO - Round 4 completed in 29.08s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:09:20,843 - strategies.federated.personalized_fl - INFO - Round 5/15
2025-05-26 19:09:49,484 - strategies.federated.personalized_fl - INFO - Round 5 completed in 28.64s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:09:49,485 - strategies.federated.personalized_fl - INFO - Round 6/15
2025-05-26 19:10:18,147 - strategies.federated.personalized_fl - INFO - Round 6 completed in 28.66s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:10:18,148 - strategies.federated.personalized_fl - INFO - Round 7/15
2025-05-26 19:10:47,030 - strategies.federated.personalized_fl - INFO - Round 7 completed in 28.88s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:10:47,031 - strategies.federated.personalized_fl - INFO - Round 8/15
2025-05-26 19:11:15,552 - strategies.federated.personalized_fl - INFO - Round 8 completed in 28.52s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:11:15,553 - strategies.federated.personalized_fl - INFO - Round 9/15
2025-05-26 19:11:46,133 - strategies.federated.personalized_fl - INFO - Round 9 completed in 30.58s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:11:46,133 - strategies.federated.personalized_fl - INFO - Round 10/15
2025-05-26 19:12:18,002 - strategies.federated.personalized_fl - INFO - Round 10 completed in 31.87s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:12:18,002 - strategies.federated.personalized_fl - INFO - Round 11/15
2025-05-26 19:12:54,011 - strategies.federated.personalized_fl - INFO - Round 11 completed in 36.01s. Global loss: 0.6688, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:12:54,012 - strategies.federated.personalized_fl - INFO - Round 12/15
2025-05-26 19:13:26,548 - strategies.federated.personalized_fl - INFO - Round 12 completed in 32.54s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:13:26,548 - strategies.federated.personalized_fl - INFO - Round 13/15
2025-05-26 19:14:13,566 - strategies.federated.personalized_fl - INFO - Round 13 completed in 47.02s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:14:13,566 - strategies.federated.personalized_fl - INFO - Round 14/15
2025-05-26 19:14:48,004 - strategies.federated.personalized_fl - INFO - Round 14 completed in 34.44s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 19:14:48,004 - strategies.federated.personalized_fl - INFO - Round 15/15
2025-05-26 19:15:20,435 - strategies.federated.personalized_fl - INFO - Round 15 completed in 32.43s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 19:15:20,436 - strategies.federated.personalized_fl - INFO - PersonalizedFL training completed in 474.62s
2025-05-26 19:15:21,180 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6521, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210404771441.5220
2025-05-26 19:15:21,391 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6686, val_MSE: 0.9351, val_RMSE: 0.9670, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1347910746337.1321
2025-05-26 19:15:21,653 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9281, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1351
2025-05-26 19:15:21,655 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_20250526_191521.json
2025-05-26 19:15:21,926 - utils.model_utils - INFO - Model saved to results/models\personalized_fl_model.pkl
2025-05-26 19:15:22,736 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_actual_vs_pred.png
2025-05-26 19:15:23,738 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_residuals.png
2025-05-26 19:15:23,742 - __main__ - INFO - Running knowledge distillation strategy: vanilla_kd
2025-05-26 19:15:23,743 - strategies.distillation.vanilla_kd - INFO - Starting Vanilla KD training with alpha=0.5, temperature=1.0
2025-05-26 19:15:25,153 - strategies.distillation.vanilla_kd - INFO - Epoch 1/1 completed in 1.22s. Train loss: 0.6811, Val loss: 0.7345
2025-05-26 19:15:25,154 - strategies.distillation.vanilla_kd - INFO - Vanilla KD training completed in 1.41s
2025-05-26 19:15:25,193 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6811, train_MSE: 0.8577, train_RMSE: 0.9261, train_R2: 0.8232, train_EVS: 0.8232, train_MAPE: 284263936283.3046
2025-05-26 19:15:25,211 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7345, val_MSE: 1.0438, val_RMSE: 1.0217, val_R2: 0.7889, val_EVS: 0.7890, val_MAPE: 2229362359786.7036
2025-05-26 19:15:25,225 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7198, test_MSE: 1.0039, test_RMSE: 1.0019, test_R2: 0.7832, test_EVS: 0.7832, test_MAPE: 1.5973
2025-05-26 19:15:25,226 - utils.evaluation - INFO - Results logged to results/logs\vanilla_kd_20250526_191525.json
2025-05-26 19:15:25,327 - utils.model_utils - INFO - Model saved to results/models\vanilla_kd_model.pkl
2025-05-26 19:15:26,220 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_actual_vs_pred.png
2025-05-26 19:15:26,886 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_residuals.png
2025-05-26 19:15:26,891 - __main__ - INFO - Running knowledge distillation strategy: ensemble_kd
2025-05-26 19:15:26,891 - strategies.distillation.ensemble_kd - INFO - Starting Ensemble KD training with 1 teachers and alpha=0.5
2025-05-26 19:15:28,240 - strategies.distillation.ensemble_kd - INFO - Epoch 1/1 completed in 1.16s. Train loss: 0.6800, Val loss: 0.7183
2025-05-26 19:15:28,241 - strategies.distillation.ensemble_kd - INFO - Ensemble KD training completed in 1.35s
2025-05-26 19:15:28,283 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6800, train_MSE: 0.8773, train_RMSE: 0.9366, train_R2: 0.8192, train_EVS: 0.8192, train_MAPE: 215068283254.2875
2025-05-26 19:15:28,298 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7183, val_MSE: 1.0142, val_RMSE: 1.0071, val_R2: 0.7949, val_EVS: 0.7950, val_MAPE: 1851560227851.3738
2025-05-26 19:15:28,317 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7178, test_MSE: 1.0117, test_RMSE: 1.0059, test_R2: 0.7816, test_EVS: 0.7816, test_MAPE: 1.4281
2025-05-26 19:15:28,320 - utils.evaluation - INFO - Results logged to results/logs\ensemble_kd_20250526_191528.json
2025-05-26 19:15:28,430 - utils.model_utils - INFO - Model saved to results/models\ensemble_kd_model.pkl
2025-05-26 19:15:29,191 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_actual_vs_pred.png
2025-05-26 19:15:29,787 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_residuals.png
2025-05-26 19:15:29,788 - __main__ - INFO - Running knowledge distillation strategy: progressive_kd
2025-05-26 19:15:29,790 - __main__ - ERROR - Error running strategy progressive_kd: ProgressiveKD.fit() got an unexpected keyword argument 'epochs'
2025-05-26 19:15:29,791 - __main__ - INFO - Running knowledge distillation strategy: attention_kd
2025-05-26 19:15:29,791 - strategies.distillation.attention_kd - INFO - Starting Attention KD training with sample_attention=True, feature_attention=True
2025-05-26 19:15:29,997 - strategies.distillation.attention_kd - INFO - Selected 81/199 features based on teacher's feature importances
2025-05-26 19:15:30,000 - strategies.distillation.attention_kd - INFO - Sample weights: min=0.0000, max=1.0933, mean=1.0000
2025-05-26 19:15:31,932 - strategies.distillation.attention_kd - INFO - Epoch 1/1 completed in 1.93s. Train loss: 0.6898, Val loss: 0.7278
2025-05-26 19:15:31,932 - strategies.distillation.attention_kd - INFO - Attention KD training completed in 2.14s
2025-05-26 19:15:31,966 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6898, train_MSE: 0.9063, train_RMSE: 0.9520, train_R2: 0.8132, train_EVS: 0.8132, train_MAPE: 241605263669.3861
2025-05-26 19:15:31,988 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7278, val_MSE: 1.0371, val_RMSE: 1.0184, val_R2: 0.7903, val_EVS: 0.7903, val_MAPE: 2605318309921.2974
2025-05-26 19:15:32,012 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7211, test_MSE: 1.0253, test_RMSE: 1.0126, test_R2: 0.7786, test_EVS: 0.7786, test_MAPE: 1.4977
2025-05-26 19:15:32,013 - utils.evaluation - INFO - Results logged to results/logs\attention_kd_20250526_191532.json
2025-05-26 19:15:32,480 - utils.model_utils - INFO - Model saved to results/models\attention_kd_model.pkl
2025-05-26 19:15:33,237 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_actual_vs_pred.png
2025-05-26 19:15:33,820 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_residuals.png
2025-05-26 19:15:33,821 - __main__ - INFO - Running combined strategy: fedavg+vanilla_kd
2025-05-26 19:15:33,822 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 19:15:33,823 - strategies.federated.fedavg - INFO - Starting FedAvg training with 5 clients and 15 rounds
2025-05-26 19:15:33,824 - strategies.federated.fedavg - INFO - Round 1/15
2025-05-26 19:16:03,587 - strategies.federated.fedavg - INFO - Round 1 completed in 29.76s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:16:03,587 - strategies.federated.fedavg - INFO - Round 2/15
2025-05-26 19:16:34,691 - strategies.federated.fedavg - INFO - Round 2 completed in 31.10s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:16:34,691 - strategies.federated.fedavg - INFO - Round 3/15
2025-05-26 19:17:05,645 - strategies.federated.fedavg - INFO - Round 3 completed in 30.95s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:17:05,645 - strategies.federated.fedavg - INFO - Round 4/15
2025-05-26 19:17:37,486 - strategies.federated.fedavg - INFO - Round 4 completed in 31.84s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:17:37,487 - strategies.federated.fedavg - INFO - Round 5/15
2025-05-26 19:18:07,657 - strategies.federated.fedavg - INFO - Round 5 completed in 30.17s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:18:07,658 - strategies.federated.fedavg - INFO - Round 6/15
2025-05-26 19:18:37,207 - strategies.federated.fedavg - INFO - Round 6 completed in 29.55s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:18:37,207 - strategies.federated.fedavg - INFO - Round 7/15
2025-05-26 19:19:04,679 - strategies.federated.fedavg - INFO - Round 7 completed in 27.47s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:19:04,679 - strategies.federated.fedavg - INFO - Round 8/15
2025-05-26 19:19:31,778 - strategies.federated.fedavg - INFO - Round 8 completed in 27.10s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:19:31,778 - strategies.federated.fedavg - INFO - Round 9/15
2025-05-26 19:19:59,581 - strategies.federated.fedavg - INFO - Round 9 completed in 27.80s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:19:59,581 - strategies.federated.fedavg - INFO - Round 10/15
2025-05-26 19:20:27,246 - strategies.federated.fedavg - INFO - Round 10 completed in 27.66s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:20:27,247 - strategies.federated.fedavg - INFO - Round 11/15
2025-05-26 19:20:54,687 - strategies.federated.fedavg - INFO - Round 11 completed in 27.44s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:20:54,687 - strategies.federated.fedavg - INFO - Round 12/15
2025-05-26 19:21:22,262 - strategies.federated.fedavg - INFO - Round 12 completed in 27.58s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:21:22,263 - strategies.federated.fedavg - INFO - Round 13/15
2025-05-26 19:21:49,678 - strategies.federated.fedavg - INFO - Round 13 completed in 27.42s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:21:49,678 - strategies.federated.fedavg - INFO - Round 14/15
2025-05-26 19:22:17,073 - strategies.federated.fedavg - INFO - Round 14 completed in 27.39s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:22:17,073 - strategies.federated.fedavg - INFO - Round 15/15
2025-05-26 19:22:44,238 - strategies.federated.fedavg - INFO - Round 15 completed in 27.17s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:22:44,238 - strategies.federated.fedavg - INFO - FedAvg training completed in 430.42s
2025-05-26 19:22:44,969 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6521, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210691281315.7474
2025-05-26 19:22:45,174 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9670, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1347910743592.9451
2025-05-26 19:22:45,425 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9281, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1394
2025-05-26 19:22:45,428 - utils.evaluation - INFO - Results logged to results/logs\fedavg_20250526_192245.json
2025-05-26 19:22:45,429 - utils.model_utils - WARNING - File results/models\fedavg_model.pkl already exists and overwrite=False
2025-05-26 19:22:46,150 - utils.visualization - INFO - Plot saved to results/plots\fedavg_actual_vs_pred.png
2025-05-26 19:22:46,735 - utils.visualization - INFO - Plot saved to results/plots\fedavg_residuals.png
2025-05-26 19:22:46,736 - __main__ - INFO - Running knowledge distillation strategy: vanilla_kd
2025-05-26 19:22:46,738 - strategies.distillation.vanilla_kd - INFO - Starting Vanilla KD training with alpha=0.5, temperature=1.0
2025-05-26 19:22:48,556 - strategies.distillation.vanilla_kd - INFO - Epoch 1/1 completed in 1.09s. Train loss: 0.6790, Val loss: 0.7350
2025-05-26 19:22:48,557 - strategies.distillation.vanilla_kd - INFO - Vanilla KD training completed in 1.82s
2025-05-26 19:22:48,592 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6790, train_MSE: 0.8128, train_RMSE: 0.9016, train_R2: 0.8325, train_EVS: 0.8325, train_MAPE: 309020137694.3346
2025-05-26 19:22:48,612 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7350, val_MSE: 1.0296, val_RMSE: 1.0147, val_R2: 0.7918, val_EVS: 0.7918, val_MAPE: 2010566862693.9238
2025-05-26 19:22:48,633 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7271, test_MSE: 1.0070, test_RMSE: 1.0035, test_R2: 0.7826, test_EVS: 0.7826, test_MAPE: 1.6912
2025-05-26 19:22:48,634 - utils.evaluation - INFO - Results logged to results/logs\vanilla_kd_20250526_192248.json
2025-05-26 19:22:48,635 - utils.model_utils - WARNING - File results/models\vanilla_kd_model.pkl already exists and overwrite=False
2025-05-26 19:22:49,361 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_actual_vs_pred.png
2025-05-26 19:22:49,983 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_residuals.png
2025-05-26 19:22:49,987 - utils.evaluation - INFO - Results logged to results/logs\fedavg_vanilla_kd_20250526_192249.json
2025-05-26 19:22:50,189 - utils.model_utils - INFO - Model saved to results/models\fedavg_vanilla_kd_model.pkl
2025-05-26 19:22:50,189 - __main__ - INFO - Running combined strategy: fedavg+ensemble_kd
2025-05-26 19:22:50,191 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 19:22:50,191 - strategies.federated.fedavg - INFO - Starting FedAvg training with 5 clients and 15 rounds
2025-05-26 19:22:50,193 - strategies.federated.fedavg - INFO - Round 1/15
2025-05-26 19:23:17,676 - strategies.federated.fedavg - INFO - Round 1 completed in 27.48s. Global loss: 0.6687, Avg client loss: 0.7356
2025-05-26 19:23:17,676 - strategies.federated.fedavg - INFO - Round 2/15
2025-05-26 19:23:45,232 - strategies.federated.fedavg - INFO - Round 2 completed in 27.56s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:23:45,232 - strategies.federated.fedavg - INFO - Round 3/15
2025-05-26 19:24:12,878 - strategies.federated.fedavg - INFO - Round 3 completed in 27.65s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:24:12,878 - strategies.federated.fedavg - INFO - Round 4/15
2025-05-26 19:24:40,255 - strategies.federated.fedavg - INFO - Round 4 completed in 27.38s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:24:40,255 - strategies.federated.fedavg - INFO - Round 5/15
2025-05-26 19:25:07,524 - strategies.federated.fedavg - INFO - Round 5 completed in 27.27s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:25:07,524 - strategies.federated.fedavg - INFO - Round 6/15
2025-05-26 19:25:34,766 - strategies.federated.fedavg - INFO - Round 6 completed in 27.24s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:25:34,767 - strategies.federated.fedavg - INFO - Round 7/15
2025-05-26 19:26:02,301 - strategies.federated.fedavg - INFO - Round 7 completed in 27.53s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:26:02,301 - strategies.federated.fedavg - INFO - Round 8/15
2025-05-26 19:26:29,760 - strategies.federated.fedavg - INFO - Round 8 completed in 27.46s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:26:29,760 - strategies.federated.fedavg - INFO - Round 9/15
2025-05-26 19:26:57,056 - strategies.federated.fedavg - INFO - Round 9 completed in 27.30s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:26:57,056 - strategies.federated.fedavg - INFO - Round 10/15
2025-05-26 19:27:24,336 - strategies.federated.fedavg - INFO - Round 10 completed in 27.28s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 19:27:24,336 - strategies.federated.fedavg - INFO - Round 11/15
2025-05-26 19:27:52,110 - strategies.federated.fedavg - INFO - Round 11 completed in 27.77s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:27:52,110 - strategies.federated.fedavg - INFO - Round 12/15
2025-05-26 19:28:19,584 - strategies.federated.fedavg - INFO - Round 12 completed in 27.47s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:28:19,584 - strategies.federated.fedavg - INFO - Round 13/15
2025-05-26 19:28:46,962 - strategies.federated.fedavg - INFO - Round 13 completed in 27.38s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:28:46,963 - strategies.federated.fedavg - INFO - Round 14/15
2025-05-26 19:29:14,517 - strategies.federated.fedavg - INFO - Round 14 completed in 27.55s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:29:14,518 - strategies.federated.fedavg - INFO - Round 15/15
2025-05-26 19:29:41,752 - strategies.federated.fedavg - INFO - Round 15 completed in 27.23s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:29:41,753 - strategies.federated.fedavg - INFO - FedAvg training completed in 411.56s
2025-05-26 19:29:42,468 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 211200832042.8369
2025-05-26 19:29:42,665 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9353, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1347910737759.7178
2025-05-26 19:29:42,918 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9281, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1343
2025-05-26 19:29:42,920 - utils.evaluation - INFO - Results logged to results/logs\fedavg_20250526_192942.json
2025-05-26 19:29:42,921 - utils.model_utils - WARNING - File results/models\fedavg_model.pkl already exists and overwrite=False
2025-05-26 19:29:43,620 - utils.visualization - INFO - Plot saved to results/plots\fedavg_actual_vs_pred.png
2025-05-26 19:29:44,181 - utils.visualization - INFO - Plot saved to results/plots\fedavg_residuals.png
2025-05-26 19:29:44,182 - __main__ - INFO - Running knowledge distillation strategy: ensemble_kd
2025-05-26 19:29:44,183 - strategies.distillation.ensemble_kd - INFO - Starting Ensemble KD training with 1 teachers and alpha=0.5
2025-05-26 19:29:45,976 - strategies.distillation.ensemble_kd - INFO - Epoch 1/1 completed in 1.08s. Train loss: 0.7069, Val loss: 0.7377
2025-05-26 19:29:45,976 - strategies.distillation.ensemble_kd - INFO - Ensemble KD training completed in 1.79s
2025-05-26 19:29:46,006 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7069, train_MSE: 0.9589, train_RMSE: 0.9792, train_R2: 0.8024, train_EVS: 0.8024, train_MAPE: 289766778498.3513
2025-05-26 19:29:46,019 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7377, val_MSE: 1.0666, val_RMSE: 1.0328, val_R2: 0.7843, val_EVS: 0.7844, val_MAPE: 2562423520384.1782
2025-05-26 19:29:46,034 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7313, test_MSE: 1.0490, test_RMSE: 1.0242, test_R2: 0.7735, test_EVS: 0.7735, test_MAPE: 1.4904
2025-05-26 19:29:46,036 - utils.evaluation - INFO - Results logged to results/logs\ensemble_kd_20250526_192946.json
2025-05-26 19:29:46,037 - utils.model_utils - WARNING - File results/models\ensemble_kd_model.pkl already exists and overwrite=False
2025-05-26 19:29:46,745 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_actual_vs_pred.png
2025-05-26 19:29:47,310 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_residuals.png
2025-05-26 19:29:47,312 - utils.evaluation - INFO - Results logged to results/logs\fedavg_ensemble_kd_20250526_192947.json
2025-05-26 19:29:47,568 - utils.model_utils - INFO - Model saved to results/models\fedavg_ensemble_kd_model.pkl
2025-05-26 19:29:47,574 - __main__ - INFO - Running combined strategy: fedavg+progressive_kd
2025-05-26 19:29:47,575 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 19:29:47,575 - strategies.federated.fedavg - INFO - Starting FedAvg training with 5 clients and 15 rounds
2025-05-26 19:29:47,580 - strategies.federated.fedavg - INFO - Round 1/15
2025-05-26 19:30:15,039 - strategies.federated.fedavg - INFO - Round 1 completed in 27.46s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:30:15,039 - strategies.federated.fedavg - INFO - Round 2/15
2025-05-26 19:30:42,676 - strategies.federated.fedavg - INFO - Round 2 completed in 27.64s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:30:42,677 - strategies.federated.fedavg - INFO - Round 3/15
2025-05-26 19:31:10,229 - strategies.federated.fedavg - INFO - Round 3 completed in 27.55s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:31:10,229 - strategies.federated.fedavg - INFO - Round 4/15
2025-05-26 19:31:37,383 - strategies.federated.fedavg - INFO - Round 4 completed in 27.15s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:31:37,384 - strategies.federated.fedavg - INFO - Round 5/15
2025-05-26 19:32:04,744 - strategies.federated.fedavg - INFO - Round 5 completed in 27.36s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:32:04,745 - strategies.federated.fedavg - INFO - Round 6/15
2025-05-26 19:32:32,182 - strategies.federated.fedavg - INFO - Round 6 completed in 27.44s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 19:32:32,182 - strategies.federated.fedavg - INFO - Round 7/15
2025-05-26 19:32:59,597 - strategies.federated.fedavg - INFO - Round 7 completed in 27.41s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:32:59,597 - strategies.federated.fedavg - INFO - Round 8/15
2025-05-26 19:33:27,105 - strategies.federated.fedavg - INFO - Round 8 completed in 27.51s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:33:27,105 - strategies.federated.fedavg - INFO - Round 9/15
2025-05-26 19:33:54,434 - strategies.federated.fedavg - INFO - Round 9 completed in 27.33s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:33:54,434 - strategies.federated.fedavg - INFO - Round 10/15
2025-05-26 19:34:21,750 - strategies.federated.fedavg - INFO - Round 10 completed in 27.32s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:34:21,750 - strategies.federated.fedavg - INFO - Round 11/15
2025-05-26 19:34:48,853 - strategies.federated.fedavg - INFO - Round 11 completed in 27.10s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:34:48,853 - strategies.federated.fedavg - INFO - Round 12/15
2025-05-26 19:35:16,370 - strategies.federated.fedavg - INFO - Round 12 completed in 27.52s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:35:16,370 - strategies.federated.fedavg - INFO - Round 13/15
2025-05-26 19:35:43,690 - strategies.federated.fedavg - INFO - Round 13 completed in 27.32s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:35:43,691 - strategies.federated.fedavg - INFO - Round 14/15
2025-05-26 19:36:12,259 - strategies.federated.fedavg - INFO - Round 14 completed in 28.57s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:36:12,259 - strategies.federated.fedavg - INFO - Round 15/15
2025-05-26 19:36:40,025 - strategies.federated.fedavg - INFO - Round 15 completed in 27.77s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:36:40,026 - strategies.federated.fedavg - INFO - FedAvg training completed in 412.45s
2025-05-26 19:36:40,788 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210404770049.1056
2025-05-26 19:36:40,995 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1347910740299.2922
2025-05-26 19:36:41,242 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9280, test_RMSE: 0.9633, test_R2: 0.7996, test_EVS: 0.7997, test_MAPE: 1.1357
2025-05-26 19:36:41,244 - utils.evaluation - INFO - Results logged to results/logs\fedavg_20250526_193641.json
2025-05-26 19:36:41,245 - utils.model_utils - WARNING - File results/models\fedavg_model.pkl already exists and overwrite=False
2025-05-26 19:36:41,959 - utils.visualization - INFO - Plot saved to results/plots\fedavg_actual_vs_pred.png
2025-05-26 19:36:42,522 - utils.visualization - INFO - Plot saved to results/plots\fedavg_residuals.png
2025-05-26 19:36:42,522 - __main__ - INFO - Running knowledge distillation strategy: progressive_kd
2025-05-26 19:36:42,523 - __main__ - ERROR - Error running strategy fedavg_progressive_kd: ProgressiveKD.fit() got an unexpected keyword argument 'epochs'
2025-05-26 19:36:42,526 - __main__ - INFO - Running combined strategy: fedavg+attention_kd
2025-05-26 19:36:42,526 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 19:36:42,526 - strategies.federated.fedavg - INFO - Starting FedAvg training with 5 clients and 15 rounds
2025-05-26 19:36:42,528 - strategies.federated.fedavg - INFO - Round 1/15
2025-05-26 19:37:10,076 - strategies.federated.fedavg - INFO - Round 1 completed in 27.55s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:37:10,076 - strategies.federated.fedavg - INFO - Round 2/15
2025-05-26 19:37:37,357 - strategies.federated.fedavg - INFO - Round 2 completed in 27.28s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:37:37,357 - strategies.federated.fedavg - INFO - Round 3/15
2025-05-26 19:38:05,248 - strategies.federated.fedavg - INFO - Round 3 completed in 27.89s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:38:05,248 - strategies.federated.fedavg - INFO - Round 4/15
2025-05-26 19:38:32,719 - strategies.federated.fedavg - INFO - Round 4 completed in 27.47s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:38:32,719 - strategies.federated.fedavg - INFO - Round 5/15
2025-05-26 19:39:00,384 - strategies.federated.fedavg - INFO - Round 5 completed in 27.67s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 19:39:00,385 - strategies.federated.fedavg - INFO - Round 6/15
2025-05-26 19:39:27,832 - strategies.federated.fedavg - INFO - Round 6 completed in 27.45s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:39:27,832 - strategies.federated.fedavg - INFO - Round 7/15
2025-05-26 19:39:55,307 - strategies.federated.fedavg - INFO - Round 7 completed in 27.48s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:39:55,307 - strategies.federated.fedavg - INFO - Round 8/15
2025-05-26 19:40:22,374 - strategies.federated.fedavg - INFO - Round 8 completed in 27.07s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:40:22,375 - strategies.federated.fedavg - INFO - Round 9/15
2025-05-26 19:40:49,940 - strategies.federated.fedavg - INFO - Round 9 completed in 27.57s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:40:49,940 - strategies.federated.fedavg - INFO - Round 10/15
2025-05-26 19:41:17,359 - strategies.federated.fedavg - INFO - Round 10 completed in 27.42s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 19:41:17,359 - strategies.federated.fedavg - INFO - Round 11/15
2025-05-26 19:41:44,714 - strategies.federated.fedavg - INFO - Round 11 completed in 27.36s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 19:41:44,714 - strategies.federated.fedavg - INFO - Round 12/15
2025-05-26 19:42:11,833 - strategies.federated.fedavg - INFO - Round 12 completed in 27.12s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:42:11,834 - strategies.federated.fedavg - INFO - Round 13/15
2025-05-26 19:42:39,246 - strategies.federated.fedavg - INFO - Round 13 completed in 27.41s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 19:42:39,246 - strategies.federated.fedavg - INFO - Round 14/15
2025-05-26 19:43:06,405 - strategies.federated.fedavg - INFO - Round 14 completed in 27.16s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 19:43:06,405 - strategies.federated.fedavg - INFO - Round 15/15
2025-05-26 19:43:33,786 - strategies.federated.fedavg - INFO - Round 15 completed in 27.38s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:43:33,786 - strategies.federated.fedavg - INFO - FedAvg training completed in 411.26s
2025-05-26 19:43:34,522 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 211200835278.5765
2025-05-26 19:43:34,724 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9351, val_RMSE: 0.9670, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1347910747875.3804
2025-05-26 19:43:34,965 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9280, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1392
2025-05-26 19:43:34,966 - utils.evaluation - INFO - Results logged to results/logs\fedavg_20250526_194334.json
2025-05-26 19:43:34,968 - utils.model_utils - WARNING - File results/models\fedavg_model.pkl already exists and overwrite=False
2025-05-26 19:43:35,692 - utils.visualization - INFO - Plot saved to results/plots\fedavg_actual_vs_pred.png
2025-05-26 19:43:36,272 - utils.visualization - INFO - Plot saved to results/plots\fedavg_residuals.png
2025-05-26 19:43:36,273 - __main__ - INFO - Running knowledge distillation strategy: attention_kd
2025-05-26 19:43:36,275 - strategies.distillation.attention_kd - INFO - Starting Attention KD training with sample_attention=True, feature_attention=True
2025-05-26 19:43:36,994 - strategies.distillation.attention_kd - INFO - Sample weights: min=0.0000, max=1.0870, mean=1.0000
2025-05-26 19:43:38,120 - strategies.distillation.attention_kd - INFO - Epoch 1/1 completed in 1.13s. Train loss: 0.7384, Val loss: 0.7514
2025-05-26 19:43:38,120 - strategies.distillation.attention_kd - INFO - Attention KD training completed in 1.85s
2025-05-26 19:43:38,150 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7384, train_MSE: 1.0730, train_RMSE: 1.0359, train_R2: 0.7789, train_EVS: 0.7789, train_MAPE: 264992045559.5897
2025-05-26 19:43:38,165 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7514, val_MSE: 1.1206, val_RMSE: 1.0586, val_R2: 0.7734, val_EVS: 0.7734, val_MAPE: 2959550104758.9673
2025-05-26 19:43:38,180 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7481, test_MSE: 1.1077, test_RMSE: 1.0525, test_R2: 0.7608, test_EVS: 0.7608, test_MAPE: 1.4024
2025-05-26 19:43:38,182 - utils.evaluation - INFO - Results logged to results/logs\attention_kd_20250526_194338.json
2025-05-26 19:43:38,182 - utils.model_utils - WARNING - File results/models\attention_kd_model.pkl already exists and overwrite=False
2025-05-26 19:43:38,939 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_actual_vs_pred.png
2025-05-26 19:43:39,651 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_residuals.png
2025-05-26 19:43:39,653 - utils.evaluation - INFO - Results logged to results/logs\fedavg_attention_kd_20250526_194339.json
2025-05-26 19:43:40,356 - utils.model_utils - INFO - Model saved to results/models\fedavg_attention_kd_model.pkl
2025-05-26 19:43:40,362 - __main__ - INFO - Running combined strategy: fedprox+vanilla_kd
2025-05-26 19:43:40,363 - __main__ - INFO - Running federated learning strategy: fedprox
2025-05-26 19:43:40,363 - strategies.federated.fedprox - INFO - Starting FedProx training with 5 clients, 15 rounds, and mu=0.01
2025-05-26 19:43:40,368 - strategies.federated.fedprox - INFO - Round 1/15
2025-05-26 19:44:08,745 - strategies.federated.fedprox - INFO - Round 1 completed in 28.38s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:44:08,746 - strategies.federated.fedprox - INFO - Round 2/15
2025-05-26 19:44:37,010 - strategies.federated.fedprox - INFO - Round 2 completed in 28.26s. Global loss: 0.6712, Avg client loss: 0.7356
2025-05-26 19:44:37,010 - strategies.federated.fedprox - INFO - Round 3/15
2025-05-26 19:45:05,041 - strategies.federated.fedprox - INFO - Round 3 completed in 28.03s. Global loss: 0.6699, Avg client loss: 0.7352
2025-05-26 19:45:05,041 - strategies.federated.fedprox - INFO - Round 4/15
2025-05-26 19:45:33,660 - strategies.federated.fedprox - INFO - Round 4 completed in 28.62s. Global loss: 0.6716, Avg client loss: 0.7364
2025-05-26 19:45:33,660 - strategies.federated.fedprox - INFO - Round 5/15
2025-05-26 19:46:02,360 - strategies.federated.fedprox - INFO - Round 5 completed in 28.70s. Global loss: 0.6735, Avg client loss: 0.7381
2025-05-26 19:46:02,360 - strategies.federated.fedprox - INFO - Round 6/15
2025-05-26 19:46:30,835 - strategies.federated.fedprox - INFO - Round 6 completed in 28.48s. Global loss: 0.6682, Avg client loss: 0.7341
2025-05-26 19:46:30,835 - strategies.federated.fedprox - INFO - Round 7/15
2025-05-26 19:47:00,080 - strategies.federated.fedprox - INFO - Round 7 completed in 29.25s. Global loss: 0.6709, Avg client loss: 0.7360
2025-05-26 19:47:00,081 - strategies.federated.fedprox - INFO - Round 8/15
2025-05-26 19:47:28,899 - strategies.federated.fedprox - INFO - Round 8 completed in 28.82s. Global loss: 0.6707, Avg client loss: 0.7368
2025-05-26 19:47:28,899 - strategies.federated.fedprox - INFO - Round 9/15
2025-05-26 19:47:57,336 - strategies.federated.fedprox - INFO - Round 9 completed in 28.44s. Global loss: 0.6708, Avg client loss: 0.7372
2025-05-26 19:47:57,337 - strategies.federated.fedprox - INFO - Round 10/15
2025-05-26 19:48:26,320 - strategies.federated.fedprox - INFO - Round 10 completed in 28.98s. Global loss: 0.6707, Avg client loss: 0.7358
2025-05-26 19:48:26,320 - strategies.federated.fedprox - INFO - Round 11/15
2025-05-26 19:48:55,162 - strategies.federated.fedprox - INFO - Round 11 completed in 28.84s. Global loss: 0.6688, Avg client loss: 0.7345
2025-05-26 19:48:55,163 - strategies.federated.fedprox - INFO - Round 12/15
2025-05-26 19:49:23,590 - strategies.federated.fedprox - INFO - Round 12 completed in 28.43s. Global loss: 0.6693, Avg client loss: 0.7350
2025-05-26 19:49:23,590 - strategies.federated.fedprox - INFO - Round 13/15
2025-05-26 19:49:52,332 - strategies.federated.fedprox - INFO - Round 13 completed in 28.74s. Global loss: 0.6693, Avg client loss: 0.7351
2025-05-26 19:49:52,332 - strategies.federated.fedprox - INFO - Round 14/15
2025-05-26 19:50:20,777 - strategies.federated.fedprox - INFO - Round 14 completed in 28.44s. Global loss: 0.6689, Avg client loss: 0.7343
2025-05-26 19:50:20,778 - strategies.federated.fedprox - INFO - Round 15/15
2025-05-26 19:50:49,167 - strategies.federated.fedprox - INFO - Round 15 completed in 28.39s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:50:49,167 - strategies.federated.fedprox - INFO - FedProx training completed in 428.80s
2025-05-26 19:50:49,875 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5635, train_MSE: 0.6519, train_RMSE: 0.8074, train_R2: 0.8657, train_EVS: 0.8657, train_MAPE: 193347220918.7083
2025-05-26 19:50:50,075 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9376, val_RMSE: 0.9683, val_R2: 0.8104, val_EVS: 0.8104, val_MAPE: 1225600789149.8699
2025-05-26 19:50:50,321 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6699, test_MSE: 0.9329, test_RMSE: 0.9659, test_R2: 0.7986, test_EVS: 0.7986, test_MAPE: 1.0604
2025-05-26 19:50:50,324 - utils.evaluation - INFO - Results logged to results/logs\fedprox_20250526_195050.json
2025-05-26 19:50:50,327 - utils.model_utils - WARNING - File results/models\fedprox_model.pkl already exists and overwrite=False
2025-05-26 19:50:51,035 - utils.visualization - INFO - Plot saved to results/plots\fedprox_actual_vs_pred.png
2025-05-26 19:50:51,610 - utils.visualization - INFO - Plot saved to results/plots\fedprox_residuals.png
2025-05-26 19:50:51,611 - __main__ - INFO - Running knowledge distillation strategy: vanilla_kd
2025-05-26 19:50:51,612 - strategies.distillation.vanilla_kd - INFO - Starting Vanilla KD training with alpha=0.5, temperature=1.0
2025-05-26 19:50:53,399 - strategies.distillation.vanilla_kd - INFO - Epoch 1/1 completed in 1.07s. Train loss: 0.6814, Val loss: 0.7395
2025-05-26 19:50:53,400 - strategies.distillation.vanilla_kd - INFO - Vanilla KD training completed in 1.79s
2025-05-26 19:50:53,439 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6814, train_MSE: 0.8131, train_RMSE: 0.9017, train_R2: 0.8324, train_EVS: 0.8324, train_MAPE: 278522213542.7306
2025-05-26 19:50:53,458 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7395, val_MSE: 1.0374, val_RMSE: 1.0185, val_R2: 0.7902, val_EVS: 0.7903, val_MAPE: 2101607362114.5559
2025-05-26 19:50:53,478 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7278, test_MSE: 1.0040, test_RMSE: 1.0020, test_R2: 0.7832, test_EVS: 0.7832, test_MAPE: 1.5165
2025-05-26 19:50:53,480 - utils.evaluation - INFO - Results logged to results/logs\vanilla_kd_20250526_195053.json
2025-05-26 19:50:53,481 - utils.model_utils - WARNING - File results/models\vanilla_kd_model.pkl already exists and overwrite=False
2025-05-26 19:50:54,189 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_actual_vs_pred.png
2025-05-26 19:50:54,772 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_residuals.png
2025-05-26 19:50:54,774 - utils.evaluation - INFO - Results logged to results/logs\fedprox_vanilla_kd_20250526_195054.json
2025-05-26 19:50:55,005 - utils.model_utils - INFO - Model saved to results/models\fedprox_vanilla_kd_model.pkl
2025-05-26 19:50:55,009 - __main__ - INFO - Running combined strategy: fedprox+ensemble_kd
2025-05-26 19:50:55,009 - __main__ - INFO - Running federated learning strategy: fedprox
2025-05-26 19:50:55,010 - strategies.federated.fedprox - INFO - Starting FedProx training with 5 clients, 15 rounds, and mu=0.01
2025-05-26 19:50:55,012 - strategies.federated.fedprox - INFO - Round 1/15
2025-05-26 19:51:23,406 - strategies.federated.fedprox - INFO - Round 1 completed in 28.39s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 19:51:23,407 - strategies.federated.fedprox - INFO - Round 2/15
2025-05-26 19:51:52,724 - strategies.federated.fedprox - INFO - Round 2 completed in 29.32s. Global loss: 0.6703, Avg client loss: 0.7351
2025-05-26 19:51:52,725 - strategies.federated.fedprox - INFO - Round 3/15
2025-05-26 19:52:21,215 - strategies.federated.fedprox - INFO - Round 3 completed in 28.49s. Global loss: 0.6708, Avg client loss: 0.7370
2025-05-26 19:52:21,215 - strategies.federated.fedprox - INFO - Round 4/15
2025-05-26 19:52:49,911 - strategies.federated.fedprox - INFO - Round 4 completed in 28.70s. Global loss: 0.6702, Avg client loss: 0.7363
2025-05-26 19:52:49,912 - strategies.federated.fedprox - INFO - Round 5/15
2025-05-26 19:53:18,720 - strategies.federated.fedprox - INFO - Round 5 completed in 28.81s. Global loss: 0.6707, Avg client loss: 0.7370
2025-05-26 19:53:18,720 - strategies.federated.fedprox - INFO - Round 6/15
2025-05-26 19:53:47,874 - strategies.federated.fedprox - INFO - Round 6 completed in 29.15s. Global loss: 0.6690, Avg client loss: 0.7353
2025-05-26 19:53:47,875 - strategies.federated.fedprox - INFO - Round 7/15
2025-05-26 19:54:16,820 - strategies.federated.fedprox - INFO - Round 7 completed in 28.95s. Global loss: 0.6698, Avg client loss: 0.7359
2025-05-26 19:54:16,820 - strategies.federated.fedprox - INFO - Round 8/15
2025-05-26 19:54:45,629 - strategies.federated.fedprox - INFO - Round 8 completed in 28.81s. Global loss: 0.6697, Avg client loss: 0.7355
2025-05-26 19:54:45,629 - strategies.federated.fedprox - INFO - Round 9/15
2025-05-26 19:55:14,433 - strategies.federated.fedprox - INFO - Round 9 completed in 28.80s. Global loss: 0.6704, Avg client loss: 0.7361
2025-05-26 19:55:14,433 - strategies.federated.fedprox - INFO - Round 10/15
2025-05-26 19:55:43,084 - strategies.federated.fedprox - INFO - Round 10 completed in 28.65s. Global loss: 0.6704, Avg client loss: 0.7366
2025-05-26 19:55:43,084 - strategies.federated.fedprox - INFO - Round 11/15
2025-05-26 19:56:11,729 - strategies.federated.fedprox - INFO - Round 11 completed in 28.65s. Global loss: 0.6704, Avg client loss: 0.7364
2025-05-26 19:56:11,730 - strategies.federated.fedprox - INFO - Round 12/15
2025-05-26 19:56:39,959 - strategies.federated.fedprox - INFO - Round 12 completed in 28.23s. Global loss: 0.6709, Avg client loss: 0.7360
2025-05-26 19:56:39,960 - strategies.federated.fedprox - INFO - Round 13/15
2025-05-26 19:57:09,772 - strategies.federated.fedprox - INFO - Round 13 completed in 29.81s. Global loss: 0.6695, Avg client loss: 0.7364
2025-05-26 19:57:09,772 - strategies.federated.fedprox - INFO - Round 14/15
2025-05-26 19:57:39,119 - strategies.federated.fedprox - INFO - Round 14 completed in 29.35s. Global loss: 0.6722, Avg client loss: 0.7376
2025-05-26 19:57:39,119 - strategies.federated.fedprox - INFO - Round 15/15
2025-05-26 19:58:07,440 - strategies.federated.fedprox - INFO - Round 15 completed in 28.32s. Global loss: 0.6699, Avg client loss: 0.7368
2025-05-26 19:58:07,441 - strategies.federated.fedprox - INFO - FedProx training completed in 432.43s
2025-05-26 19:58:08,157 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5631, train_MSE: 0.6523, train_RMSE: 0.8077, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 211574044624.9848
2025-05-26 19:58:08,366 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6699, val_MSE: 0.9384, val_RMSE: 0.9687, val_R2: 0.8102, val_EVS: 0.8102, val_MAPE: 1485840438059.0969
2025-05-26 19:58:08,622 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6694, test_MSE: 0.9288, test_RMSE: 0.9637, test_R2: 0.7995, test_EVS: 0.7995, test_MAPE: 1.1830
2025-05-26 19:58:08,625 - utils.evaluation - INFO - Results logged to results/logs\fedprox_20250526_195808.json
2025-05-26 19:58:08,626 - utils.model_utils - WARNING - File results/models\fedprox_model.pkl already exists and overwrite=False
2025-05-26 19:58:09,338 - utils.visualization - INFO - Plot saved to results/plots\fedprox_actual_vs_pred.png
2025-05-26 19:58:09,917 - utils.visualization - INFO - Plot saved to results/plots\fedprox_residuals.png
2025-05-26 19:58:09,918 - __main__ - INFO - Running knowledge distillation strategy: ensemble_kd
2025-05-26 19:58:09,920 - strategies.distillation.ensemble_kd - INFO - Starting Ensemble KD training with 1 teachers and alpha=0.5
2025-05-26 19:58:11,741 - strategies.distillation.ensemble_kd - INFO - Epoch 1/1 completed in 1.11s. Train loss: 0.7056, Val loss: 0.7389
2025-05-26 19:58:11,741 - strategies.distillation.ensemble_kd - INFO - Ensemble KD training completed in 1.82s
2025-05-26 19:58:11,772 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7056, train_MSE: 0.9546, train_RMSE: 0.9770, train_R2: 0.8033, train_EVS: 0.8033, train_MAPE: 286859673040.3260
2025-05-26 19:58:11,791 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7389, val_MSE: 1.0671, val_RMSE: 1.0330, val_R2: 0.7842, val_EVS: 0.7843, val_MAPE: 2641060250191.6519
2025-05-26 19:58:11,810 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7299, test_MSE: 1.0460, test_RMSE: 1.0228, test_R2: 0.7742, test_EVS: 0.7742, test_MAPE: 1.4929
2025-05-26 19:58:11,812 - utils.evaluation - INFO - Results logged to results/logs\ensemble_kd_20250526_195811.json
2025-05-26 19:58:11,813 - utils.model_utils - WARNING - File results/models\ensemble_kd_model.pkl already exists and overwrite=False
2025-05-26 19:58:12,534 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_actual_vs_pred.png
2025-05-26 19:58:13,203 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_residuals.png
2025-05-26 19:58:13,206 - utils.evaluation - INFO - Results logged to results/logs\fedprox_ensemble_kd_20250526_195813.json
2025-05-26 19:58:13,458 - utils.model_utils - INFO - Model saved to results/models\fedprox_ensemble_kd_model.pkl
2025-05-26 19:58:13,467 - __main__ - INFO - Running combined strategy: fedprox+progressive_kd
2025-05-26 19:58:13,467 - __main__ - INFO - Running federated learning strategy: fedprox
2025-05-26 19:58:13,468 - strategies.federated.fedprox - INFO - Starting FedProx training with 5 clients, 15 rounds, and mu=0.01
2025-05-26 19:58:13,470 - strategies.federated.fedprox - INFO - Round 1/15
2025-05-26 19:58:41,899 - strategies.federated.fedprox - INFO - Round 1 completed in 28.43s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 19:58:41,899 - strategies.federated.fedprox - INFO - Round 2/15
2025-05-26 19:59:10,823 - strategies.federated.fedprox - INFO - Round 2 completed in 28.92s. Global loss: 0.6718, Avg client loss: 0.7356
2025-05-26 19:59:10,823 - strategies.federated.fedprox - INFO - Round 3/15
2025-05-26 19:59:39,391 - strategies.federated.fedprox - INFO - Round 3 completed in 28.57s. Global loss: 0.6694, Avg client loss: 0.7350
2025-05-26 19:59:39,391 - strategies.federated.fedprox - INFO - Round 4/15
2025-05-26 20:00:10,042 - strategies.federated.fedprox - INFO - Round 4 completed in 30.65s. Global loss: 0.6710, Avg client loss: 0.7363
2025-05-26 20:00:10,042 - strategies.federated.fedprox - INFO - Round 5/15
2025-05-26 20:00:39,666 - strategies.federated.fedprox - INFO - Round 5 completed in 29.62s. Global loss: 0.6710, Avg client loss: 0.7356
2025-05-26 20:00:39,666 - strategies.federated.fedprox - INFO - Round 6/15
2025-05-26 20:01:08,361 - strategies.federated.fedprox - INFO - Round 6 completed in 28.69s. Global loss: 0.6712, Avg client loss: 0.7371
2025-05-26 20:01:08,361 - strategies.federated.fedprox - INFO - Round 7/15
2025-05-26 20:01:36,972 - strategies.federated.fedprox - INFO - Round 7 completed in 28.61s. Global loss: 0.6720, Avg client loss: 0.7376
2025-05-26 20:01:36,972 - strategies.federated.fedprox - INFO - Round 8/15
2025-05-26 20:02:06,832 - strategies.federated.fedprox - INFO - Round 8 completed in 29.86s. Global loss: 0.6706, Avg client loss: 0.7381
2025-05-26 20:02:06,832 - strategies.federated.fedprox - INFO - Round 9/15
2025-05-26 20:02:36,951 - strategies.federated.fedprox - INFO - Round 9 completed in 30.12s. Global loss: 0.6700, Avg client loss: 0.7354
2025-05-26 20:02:36,952 - strategies.federated.fedprox - INFO - Round 10/15
2025-05-26 20:03:06,300 - strategies.federated.fedprox - INFO - Round 10 completed in 29.35s. Global loss: 0.6708, Avg client loss: 0.7365
2025-05-26 20:03:06,301 - strategies.federated.fedprox - INFO - Round 11/15
2025-05-26 20:03:37,849 - strategies.federated.fedprox - INFO - Round 11 completed in 31.55s. Global loss: 0.6694, Avg client loss: 0.7351
2025-05-26 20:03:37,849 - strategies.federated.fedprox - INFO - Round 12/15
2025-05-26 20:04:07,759 - strategies.federated.fedprox - INFO - Round 12 completed in 29.91s. Global loss: 0.6706, Avg client loss: 0.7364
2025-05-26 20:04:07,760 - strategies.federated.fedprox - INFO - Round 13/15
2025-05-26 20:04:38,204 - strategies.federated.fedprox - INFO - Round 13 completed in 30.44s. Global loss: 0.6698, Avg client loss: 0.7366
2025-05-26 20:04:38,204 - strategies.federated.fedprox - INFO - Round 14/15
2025-05-26 20:05:08,849 - strategies.federated.fedprox - INFO - Round 14 completed in 30.64s. Global loss: 0.6683, Avg client loss: 0.7350
2025-05-26 20:05:08,849 - strategies.federated.fedprox - INFO - Round 15/15
2025-05-26 20:05:39,419 - strategies.federated.fedprox - INFO - Round 15 completed in 30.57s. Global loss: 0.6707, Avg client loss: 0.7361
2025-05-26 20:05:39,420 - strategies.federated.fedprox - INFO - FedProx training completed in 445.95s
2025-05-26 20:05:40,151 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5639, train_MSE: 0.6530, train_RMSE: 0.8081, train_R2: 0.8654, train_EVS: 0.8654, train_MAPE: 186270521079.4672
2025-05-26 20:05:40,344 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6707, val_MSE: 0.9387, val_RMSE: 0.9688, val_R2: 0.8102, val_EVS: 0.8102, val_MAPE: 1270298470641.5327
2025-05-26 20:05:40,603 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6694, test_MSE: 0.9313, test_RMSE: 0.9650, test_R2: 0.7989, test_EVS: 0.7989, test_MAPE: 1.0999
2025-05-26 20:05:40,605 - utils.evaluation - INFO - Results logged to results/logs\fedprox_20250526_200540.json
2025-05-26 20:05:40,607 - utils.model_utils - WARNING - File results/models\fedprox_model.pkl already exists and overwrite=False
2025-05-26 20:05:41,327 - utils.visualization - INFO - Plot saved to results/plots\fedprox_actual_vs_pred.png
2025-05-26 20:05:41,875 - utils.visualization - INFO - Plot saved to results/plots\fedprox_residuals.png
2025-05-26 20:05:41,875 - __main__ - INFO - Running knowledge distillation strategy: progressive_kd
2025-05-26 20:05:41,877 - __main__ - ERROR - Error running strategy fedprox_progressive_kd: ProgressiveKD.fit() got an unexpected keyword argument 'epochs'
2025-05-26 20:05:41,879 - __main__ - INFO - Running combined strategy: fedprox+attention_kd
2025-05-26 20:05:41,880 - __main__ - INFO - Running federated learning strategy: fedprox
2025-05-26 20:05:41,880 - strategies.federated.fedprox - INFO - Starting FedProx training with 5 clients, 15 rounds, and mu=0.01
2025-05-26 20:05:41,882 - strategies.federated.fedprox - INFO - Round 1/15
2025-05-26 20:06:09,941 - strategies.federated.fedprox - INFO - Round 1 completed in 28.06s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:06:09,942 - strategies.federated.fedprox - INFO - Round 2/15
2025-05-26 20:06:38,957 - strategies.federated.fedprox - INFO - Round 2 completed in 29.01s. Global loss: 0.6715, Avg client loss: 0.7359
2025-05-26 20:06:38,958 - strategies.federated.fedprox - INFO - Round 3/15
2025-05-26 20:07:07,847 - strategies.federated.fedprox - INFO - Round 3 completed in 28.89s. Global loss: 0.6698, Avg client loss: 0.7363
2025-05-26 20:07:07,847 - strategies.federated.fedprox - INFO - Round 4/15
2025-05-26 20:07:36,697 - strategies.federated.fedprox - INFO - Round 4 completed in 28.85s. Global loss: 0.6692, Avg client loss: 0.7369
2025-05-26 20:07:36,697 - strategies.federated.fedprox - INFO - Round 5/15
2025-05-26 20:08:05,983 - strategies.federated.fedprox - INFO - Round 5 completed in 29.29s. Global loss: 0.6715, Avg client loss: 0.7370
2025-05-26 20:08:05,983 - strategies.federated.fedprox - INFO - Round 6/15
2025-05-26 20:08:35,146 - strategies.federated.fedprox - INFO - Round 6 completed in 29.16s. Global loss: 0.6696, Avg client loss: 0.7352
2025-05-26 20:08:35,146 - strategies.federated.fedprox - INFO - Round 7/15
2025-05-26 20:09:03,493 - strategies.federated.fedprox - INFO - Round 7 completed in 28.35s. Global loss: 0.6692, Avg client loss: 0.7345
2025-05-26 20:09:03,494 - strategies.federated.fedprox - INFO - Round 8/15
2025-05-26 20:09:32,593 - strategies.federated.fedprox - INFO - Round 8 completed in 29.10s. Global loss: 0.6687, Avg client loss: 0.7346
2025-05-26 20:09:32,594 - strategies.federated.fedprox - INFO - Round 9/15
2025-05-26 20:10:01,529 - strategies.federated.fedprox - INFO - Round 9 completed in 28.94s. Global loss: 0.6683, Avg client loss: 0.7347
2025-05-26 20:10:01,529 - strategies.federated.fedprox - INFO - Round 10/15
2025-05-26 20:10:30,023 - strategies.federated.fedprox - INFO - Round 10 completed in 28.49s. Global loss: 0.6702, Avg client loss: 0.7358
2025-05-26 20:10:30,024 - strategies.federated.fedprox - INFO - Round 11/15
2025-05-26 20:10:59,935 - strategies.federated.fedprox - INFO - Round 11 completed in 29.91s. Global loss: 0.6716, Avg client loss: 0.7369
2025-05-26 20:10:59,935 - strategies.federated.fedprox - INFO - Round 12/15
2025-05-26 20:11:30,128 - strategies.federated.fedprox - INFO - Round 12 completed in 30.19s. Global loss: 0.6695, Avg client loss: 0.7350
2025-05-26 20:11:30,128 - strategies.federated.fedprox - INFO - Round 13/15
2025-05-26 20:11:59,546 - strategies.federated.fedprox - INFO - Round 13 completed in 29.42s. Global loss: 0.6710, Avg client loss: 0.7379
2025-05-26 20:11:59,546 - strategies.federated.fedprox - INFO - Round 14/15
2025-05-26 20:12:28,990 - strategies.federated.fedprox - INFO - Round 14 completed in 29.44s. Global loss: 0.6698, Avg client loss: 0.7340
2025-05-26 20:12:28,991 - strategies.federated.fedprox - INFO - Round 15/15
2025-05-26 20:12:58,372 - strategies.federated.fedprox - INFO - Round 15 completed in 29.38s. Global loss: 0.6682, Avg client loss: 0.7352
2025-05-26 20:12:58,374 - strategies.federated.fedprox - INFO - FedProx training completed in 436.49s
2025-05-26 20:12:59,111 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5618, train_MSE: 0.6507, train_RMSE: 0.8067, train_R2: 0.8659, train_EVS: 0.8659, train_MAPE: 215485077612.6807
2025-05-26 20:12:59,329 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6682, val_MSE: 0.9350, val_RMSE: 0.9669, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1297404047323.6794
2025-05-26 20:12:59,591 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6694, test_MSE: 0.9310, test_RMSE: 0.9649, test_R2: 0.7990, test_EVS: 0.7990, test_MAPE: 1.1867
2025-05-26 20:12:59,592 - utils.evaluation - INFO - Results logged to results/logs\fedprox_20250526_201259.json
2025-05-26 20:12:59,594 - utils.model_utils - WARNING - File results/models\fedprox_model.pkl already exists and overwrite=False
2025-05-26 20:13:00,326 - utils.visualization - INFO - Plot saved to results/plots\fedprox_actual_vs_pred.png
2025-05-26 20:13:00,904 - utils.visualization - INFO - Plot saved to results/plots\fedprox_residuals.png
2025-05-26 20:13:00,905 - __main__ - INFO - Running knowledge distillation strategy: attention_kd
2025-05-26 20:13:00,906 - strategies.distillation.attention_kd - INFO - Starting Attention KD training with sample_attention=True, feature_attention=True
2025-05-26 20:13:01,627 - strategies.distillation.attention_kd - INFO - Sample weights: min=0.0000, max=1.0877, mean=1.0000
2025-05-26 20:13:02,732 - strategies.distillation.attention_kd - INFO - Epoch 1/1 completed in 1.11s. Train loss: 0.7378, Val loss: 0.7512
2025-05-26 20:13:02,732 - strategies.distillation.attention_kd - INFO - Attention KD training completed in 1.83s
2025-05-26 20:13:02,766 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7378, train_MSE: 1.0719, train_RMSE: 1.0353, train_R2: 0.7791, train_EVS: 0.7791, train_MAPE: 236076332891.9573
2025-05-26 20:13:02,779 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7512, val_MSE: 1.1209, val_RMSE: 1.0587, val_R2: 0.7733, val_EVS: 0.7734, val_MAPE: 2957247655123.1484
2025-05-26 20:13:02,802 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7479, test_MSE: 1.1043, test_RMSE: 1.0509, test_R2: 0.7616, test_EVS: 0.7616, test_MAPE: 1.5131
2025-05-26 20:13:02,925 - utils.evaluation - INFO - Results logged to results/logs\attention_kd_20250526_201302.json
2025-05-26 20:13:02,926 - utils.model_utils - WARNING - File results/models\attention_kd_model.pkl already exists and overwrite=False
2025-05-26 20:13:03,648 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_actual_vs_pred.png
2025-05-26 20:13:04,228 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_residuals.png
2025-05-26 20:13:04,231 - utils.evaluation - INFO - Results logged to results/logs\fedprox_attention_kd_20250526_201304.json
2025-05-26 20:13:04,478 - utils.model_utils - INFO - Model saved to results/models\fedprox_attention_kd_model.pkl
2025-05-26 20:13:04,486 - __main__ - INFO - Running combined strategy: scaffold+vanilla_kd
2025-05-26 20:13:04,486 - __main__ - INFO - Running federated learning strategy: scaffold
2025-05-26 20:13:04,487 - strategies.federated.scaffold - INFO - Starting SCAFFOLD training with 5 clients and 15 rounds
2025-05-26 20:13:04,490 - strategies.federated.scaffold - INFO - Round 1/15
2025-05-26 20:13:32,382 - strategies.federated.scaffold - INFO - Round 1 completed in 27.89s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:13:32,383 - strategies.federated.scaffold - INFO - Round 2/15
2025-05-26 20:14:01,095 - strategies.federated.scaffold - INFO - Round 2 completed in 28.71s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:14:01,096 - strategies.federated.scaffold - INFO - Round 3/15
2025-05-26 20:14:30,090 - strategies.federated.scaffold - INFO - Round 3 completed in 28.99s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:14:30,090 - strategies.federated.scaffold - INFO - Round 4/15
2025-05-26 20:14:58,241 - strategies.federated.scaffold - INFO - Round 4 completed in 28.15s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:14:58,241 - strategies.federated.scaffold - INFO - Round 5/15
2025-05-26 20:15:27,222 - strategies.federated.scaffold - INFO - Round 5 completed in 28.98s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:15:27,222 - strategies.federated.scaffold - INFO - Round 6/15
2025-05-26 20:15:56,157 - strategies.federated.scaffold - INFO - Round 6 completed in 28.93s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:15:56,157 - strategies.federated.scaffold - INFO - Round 7/15
2025-05-26 20:16:24,634 - strategies.federated.scaffold - INFO - Round 7 completed in 28.48s. Global loss: 0.6688, Avg client loss: 0.7357
2025-05-26 20:16:24,634 - strategies.federated.scaffold - INFO - Round 8/15
2025-05-26 20:16:53,391 - strategies.federated.scaffold - INFO - Round 8 completed in 28.76s. Global loss: 0.6688, Avg client loss: 0.7357
2025-05-26 20:16:53,392 - strategies.federated.scaffold - INFO - Round 9/15
2025-05-26 20:17:21,871 - strategies.federated.scaffold - INFO - Round 9 completed in 28.48s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:17:21,872 - strategies.federated.scaffold - INFO - Round 10/15
2025-05-26 20:17:51,095 - strategies.federated.scaffold - INFO - Round 10 completed in 29.22s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:17:51,095 - strategies.federated.scaffold - INFO - Round 11/15
2025-05-26 20:18:19,770 - strategies.federated.scaffold - INFO - Round 11 completed in 28.68s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:18:19,771 - strategies.federated.scaffold - INFO - Round 12/15
2025-05-26 20:18:48,619 - strategies.federated.scaffold - INFO - Round 12 completed in 28.85s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:18:48,619 - strategies.federated.scaffold - INFO - Round 13/15
2025-05-26 20:19:16,792 - strategies.federated.scaffold - INFO - Round 13 completed in 28.17s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 20:19:16,793 - strategies.federated.scaffold - INFO - Round 14/15
2025-05-26 20:19:45,593 - strategies.federated.scaffold - INFO - Round 14 completed in 28.80s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:19:45,593 - strategies.federated.scaffold - INFO - Round 15/15
2025-05-26 20:20:14,440 - strategies.federated.scaffold - INFO - Round 15 completed in 28.85s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:20:14,441 - strategies.federated.scaffold - INFO - SCAFFOLD training completed in 429.95s
2025-05-26 20:20:15,167 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 209833347192.3056
2025-05-26 20:20:15,371 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9353, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1337487211142.2083
2025-05-26 20:20:15,626 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9280, test_RMSE: 0.9633, test_R2: 0.7996, test_EVS: 0.7997, test_MAPE: 1.1363
2025-05-26 20:20:15,629 - utils.evaluation - INFO - Results logged to results/logs\scaffold_20250526_202015.json
2025-05-26 20:20:15,630 - utils.model_utils - WARNING - File results/models\scaffold_model.pkl already exists and overwrite=False
2025-05-26 20:20:16,351 - utils.visualization - INFO - Plot saved to results/plots\scaffold_actual_vs_pred.png
2025-05-26 20:20:16,915 - utils.visualization - INFO - Plot saved to results/plots\scaffold_residuals.png
2025-05-26 20:20:16,915 - __main__ - INFO - Running knowledge distillation strategy: vanilla_kd
2025-05-26 20:20:16,916 - strategies.distillation.vanilla_kd - INFO - Starting Vanilla KD training with alpha=0.5, temperature=1.0
2025-05-26 20:20:18,726 - strategies.distillation.vanilla_kd - INFO - Epoch 1/1 completed in 1.08s. Train loss: 0.6801, Val loss: 0.7394
2025-05-26 20:20:18,726 - strategies.distillation.vanilla_kd - INFO - Vanilla KD training completed in 1.81s
2025-05-26 20:20:18,755 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6801, train_MSE: 0.8129, train_RMSE: 0.9016, train_R2: 0.8325, train_EVS: 0.8325, train_MAPE: 229063090232.4509
2025-05-26 20:20:18,772 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7394, val_MSE: 1.0364, val_RMSE: 1.0180, val_R2: 0.7904, val_EVS: 0.7905, val_MAPE: 2029189650254.2588
2025-05-26 20:20:18,783 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7292, test_MSE: 1.0098, test_RMSE: 1.0049, test_R2: 0.7820, test_EVS: 0.7820, test_MAPE: 1.5791
2025-05-26 20:20:18,785 - utils.evaluation - INFO - Results logged to results/logs\vanilla_kd_20250526_202018.json
2025-05-26 20:20:18,787 - utils.model_utils - WARNING - File results/models\vanilla_kd_model.pkl already exists and overwrite=False
2025-05-26 20:20:19,507 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_actual_vs_pred.png
2025-05-26 20:20:20,097 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_residuals.png
2025-05-26 20:20:20,104 - utils.evaluation - INFO - Results logged to results/logs\scaffold_vanilla_kd_20250526_202020.json
2025-05-26 20:20:20,323 - utils.model_utils - INFO - Model saved to results/models\scaffold_vanilla_kd_model.pkl
2025-05-26 20:20:20,327 - __main__ - INFO - Running combined strategy: scaffold+ensemble_kd
2025-05-26 20:20:20,327 - __main__ - INFO - Running federated learning strategy: scaffold
2025-05-26 20:20:20,327 - strategies.federated.scaffold - INFO - Starting SCAFFOLD training with 5 clients and 15 rounds
2025-05-26 20:20:20,329 - strategies.federated.scaffold - INFO - Round 1/15
2025-05-26 20:20:48,383 - strategies.federated.scaffold - INFO - Round 1 completed in 28.05s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:20:48,383 - strategies.federated.scaffold - INFO - Round 2/15
2025-05-26 20:21:16,584 - strategies.federated.scaffold - INFO - Round 2 completed in 28.20s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:21:16,585 - strategies.federated.scaffold - INFO - Round 3/15
2025-05-26 20:21:45,523 - strategies.federated.scaffold - INFO - Round 3 completed in 28.94s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:21:45,523 - strategies.federated.scaffold - INFO - Round 4/15
2025-05-26 20:22:14,141 - strategies.federated.scaffold - INFO - Round 4 completed in 28.62s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:22:14,141 - strategies.federated.scaffold - INFO - Round 5/15
2025-05-26 20:22:42,716 - strategies.federated.scaffold - INFO - Round 5 completed in 28.58s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 20:22:42,716 - strategies.federated.scaffold - INFO - Round 6/15
2025-05-26 20:23:14,728 - strategies.federated.scaffold - INFO - Round 6 completed in 32.01s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:23:14,729 - strategies.federated.scaffold - INFO - Round 7/15
2025-05-26 20:23:48,119 - strategies.federated.scaffold - INFO - Round 7 completed in 33.39s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:23:48,119 - strategies.federated.scaffold - INFO - Round 8/15
2025-05-26 20:24:22,359 - strategies.federated.scaffold - INFO - Round 8 completed in 34.24s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:24:22,360 - strategies.federated.scaffold - INFO - Round 9/15
2025-05-26 20:24:54,971 - strategies.federated.scaffold - INFO - Round 9 completed in 32.61s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:24:54,971 - strategies.federated.scaffold - INFO - Round 10/15
2025-05-26 20:25:25,251 - strategies.federated.scaffold - INFO - Round 10 completed in 30.28s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:25:25,252 - strategies.federated.scaffold - INFO - Round 11/15
2025-05-26 20:25:54,201 - strategies.federated.scaffold - INFO - Round 11 completed in 28.95s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:25:54,201 - strategies.federated.scaffold - INFO - Round 12/15
2025-05-26 20:26:23,539 - strategies.federated.scaffold - INFO - Round 12 completed in 29.34s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:26:23,540 - strategies.federated.scaffold - INFO - Round 13/15
2025-05-26 20:26:52,247 - strategies.federated.scaffold - INFO - Round 13 completed in 28.71s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 20:26:52,248 - strategies.federated.scaffold - INFO - Round 14/15
2025-05-26 20:27:20,950 - strategies.federated.scaffold - INFO - Round 14 completed in 28.70s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:27:20,950 - strategies.federated.scaffold - INFO - Round 15/15
2025-05-26 20:27:49,900 - strategies.federated.scaffold - INFO - Round 15 completed in 28.95s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:27:49,901 - strategies.federated.scaffold - INFO - SCAFFOLD training completed in 449.57s
2025-05-26 20:27:50,627 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 209323794025.7627
2025-05-26 20:27:50,851 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6688, val_MSE: 0.9353, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1337487206144.5549
2025-05-26 20:27:51,096 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9281, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1375
2025-05-26 20:27:51,098 - utils.evaluation - INFO - Results logged to results/logs\scaffold_20250526_202751.json
2025-05-26 20:27:51,099 - utils.model_utils - WARNING - File results/models\scaffold_model.pkl already exists and overwrite=False
2025-05-26 20:27:51,802 - utils.visualization - INFO - Plot saved to results/plots\scaffold_actual_vs_pred.png
2025-05-26 20:27:52,386 - utils.visualization - INFO - Plot saved to results/plots\scaffold_residuals.png
2025-05-26 20:27:52,386 - __main__ - INFO - Running knowledge distillation strategy: ensemble_kd
2025-05-26 20:27:52,388 - strategies.distillation.ensemble_kd - INFO - Starting Ensemble KD training with 1 teachers and alpha=0.5
2025-05-26 20:27:54,169 - strategies.distillation.ensemble_kd - INFO - Epoch 1/1 completed in 1.03s. Train loss: 0.7067, Val loss: 0.7370
2025-05-26 20:27:54,169 - strategies.distillation.ensemble_kd - INFO - Ensemble KD training completed in 1.78s
2025-05-26 20:27:54,197 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7067, train_MSE: 0.9583, train_RMSE: 0.9789, train_R2: 0.8025, train_EVS: 0.8025, train_MAPE: 277330855447.4569
2025-05-26 20:27:54,209 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7370, val_MSE: 1.0689, val_RMSE: 1.0339, val_R2: 0.7838, val_EVS: 0.7839, val_MAPE: 2648933284528.4751
2025-05-26 20:27:54,225 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7313, test_MSE: 1.0483, test_RMSE: 1.0239, test_R2: 0.7737, test_EVS: 0.7737, test_MAPE: 1.5140
2025-05-26 20:27:54,228 - utils.evaluation - INFO - Results logged to results/logs\ensemble_kd_20250526_202754.json
2025-05-26 20:27:54,229 - utils.model_utils - WARNING - File results/models\ensemble_kd_model.pkl already exists and overwrite=False
2025-05-26 20:27:55,081 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_actual_vs_pred.png
2025-05-26 20:27:55,652 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_residuals.png
2025-05-26 20:27:55,655 - utils.evaluation - INFO - Results logged to results/logs\scaffold_ensemble_kd_20250526_202755.json
2025-05-26 20:27:56,019 - utils.model_utils - INFO - Model saved to results/models\scaffold_ensemble_kd_model.pkl
2025-05-26 20:27:56,025 - __main__ - INFO - Running combined strategy: scaffold+progressive_kd
2025-05-26 20:27:56,025 - __main__ - INFO - Running federated learning strategy: scaffold
2025-05-26 20:27:56,025 - strategies.federated.scaffold - INFO - Starting SCAFFOLD training with 5 clients and 15 rounds
2025-05-26 20:27:56,028 - strategies.federated.scaffold - INFO - Round 1/15
2025-05-26 20:28:24,198 - strategies.federated.scaffold - INFO - Round 1 completed in 28.17s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:28:24,198 - strategies.federated.scaffold - INFO - Round 2/15
2025-05-26 20:28:52,549 - strategies.federated.scaffold - INFO - Round 2 completed in 28.35s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:28:52,549 - strategies.federated.scaffold - INFO - Round 3/15
2025-05-26 20:29:21,549 - strategies.federated.scaffold - INFO - Round 3 completed in 29.00s. Global loss: 0.6686, Avg client loss: 0.7356
2025-05-26 20:29:21,549 - strategies.federated.scaffold - INFO - Round 4/15
2025-05-26 20:29:50,099 - strategies.federated.scaffold - INFO - Round 4 completed in 28.55s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:29:50,099 - strategies.federated.scaffold - INFO - Round 5/15
2025-05-26 20:30:21,299 - strategies.federated.scaffold - INFO - Round 5 completed in 31.20s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:30:21,300 - strategies.federated.scaffold - INFO - Round 6/15
2025-05-26 20:30:51,800 - strategies.federated.scaffold - INFO - Round 6 completed in 30.50s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:30:51,800 - strategies.federated.scaffold - INFO - Round 7/15
2025-05-26 20:31:23,870 - strategies.federated.scaffold - INFO - Round 7 completed in 32.07s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:31:23,871 - strategies.federated.scaffold - INFO - Round 8/15
2025-05-26 20:31:54,699 - strategies.federated.scaffold - INFO - Round 8 completed in 30.83s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:31:54,699 - strategies.federated.scaffold - INFO - Round 9/15
2025-05-26 20:32:23,866 - strategies.federated.scaffold - INFO - Round 9 completed in 29.17s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:32:23,866 - strategies.federated.scaffold - INFO - Round 10/15
2025-05-26 20:32:52,698 - strategies.federated.scaffold - INFO - Round 10 completed in 28.83s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:32:52,698 - strategies.federated.scaffold - INFO - Round 11/15
2025-05-26 20:33:23,800 - strategies.federated.scaffold - INFO - Round 11 completed in 31.10s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:33:23,800 - strategies.federated.scaffold - INFO - Round 12/15
2025-05-26 20:33:52,897 - strategies.federated.scaffold - INFO - Round 12 completed in 29.10s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:33:52,897 - strategies.federated.scaffold - INFO - Round 13/15
2025-05-26 20:34:22,301 - strategies.federated.scaffold - INFO - Round 13 completed in 29.40s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:34:22,302 - strategies.federated.scaffold - INFO - Round 14/15
2025-05-26 20:34:52,066 - strategies.federated.scaffold - INFO - Round 14 completed in 29.77s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:34:52,067 - strategies.federated.scaffold - INFO - Round 15/15
2025-05-26 20:35:22,080 - strategies.federated.scaffold - INFO - Round 15 completed in 30.01s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:35:22,080 - strategies.federated.scaffold - INFO - SCAFFOLD training completed in 446.06s
2025-05-26 20:35:22,834 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210691285092.0242
2025-05-26 20:35:23,043 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1344549813855.5063
2025-05-26 20:35:23,286 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6690, test_MSE: 0.9279, test_RMSE: 0.9633, test_R2: 0.7997, test_EVS: 0.7997, test_MAPE: 1.1384
2025-05-26 20:35:23,290 - utils.evaluation - INFO - Results logged to results/logs\scaffold_20250526_203523.json
2025-05-26 20:35:23,291 - utils.model_utils - WARNING - File results/models\scaffold_model.pkl already exists and overwrite=False
2025-05-26 20:35:23,999 - utils.visualization - INFO - Plot saved to results/plots\scaffold_actual_vs_pred.png
2025-05-26 20:35:24,566 - utils.visualization - INFO - Plot saved to results/plots\scaffold_residuals.png
2025-05-26 20:35:24,567 - __main__ - INFO - Running knowledge distillation strategy: progressive_kd
2025-05-26 20:35:24,569 - __main__ - ERROR - Error running strategy scaffold_progressive_kd: ProgressiveKD.fit() got an unexpected keyword argument 'epochs'
2025-05-26 20:35:24,575 - __main__ - INFO - Running combined strategy: scaffold+attention_kd
2025-05-26 20:35:24,575 - __main__ - INFO - Running federated learning strategy: scaffold
2025-05-26 20:35:24,575 - strategies.federated.scaffold - INFO - Starting SCAFFOLD training with 5 clients and 15 rounds
2025-05-26 20:35:24,577 - strategies.federated.scaffold - INFO - Round 1/15
2025-05-26 20:35:54,145 - strategies.federated.scaffold - INFO - Round 1 completed in 29.57s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:35:54,146 - strategies.federated.scaffold - INFO - Round 2/15
2025-05-26 20:36:23,439 - strategies.federated.scaffold - INFO - Round 2 completed in 29.29s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:36:23,440 - strategies.federated.scaffold - INFO - Round 3/15
2025-05-26 20:36:52,213 - strategies.federated.scaffold - INFO - Round 3 completed in 28.77s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:36:52,213 - strategies.federated.scaffold - INFO - Round 4/15
2025-05-26 20:37:20,758 - strategies.federated.scaffold - INFO - Round 4 completed in 28.54s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:37:20,758 - strategies.federated.scaffold - INFO - Round 5/15
2025-05-26 20:37:51,642 - strategies.federated.scaffold - INFO - Round 5 completed in 30.88s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 20:37:51,643 - strategies.federated.scaffold - INFO - Round 6/15
2025-05-26 20:38:22,692 - strategies.federated.scaffold - INFO - Round 6 completed in 31.05s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:38:22,692 - strategies.federated.scaffold - INFO - Round 7/15
2025-05-26 20:38:55,815 - strategies.federated.scaffold - INFO - Round 7 completed in 33.12s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:38:55,815 - strategies.federated.scaffold - INFO - Round 8/15
2025-05-26 20:39:28,405 - strategies.federated.scaffold - INFO - Round 8 completed in 32.59s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:39:28,405 - strategies.federated.scaffold - INFO - Round 9/15
2025-05-26 20:39:58,942 - strategies.federated.scaffold - INFO - Round 9 completed in 30.54s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:39:58,943 - strategies.federated.scaffold - INFO - Round 10/15
2025-05-26 20:40:29,841 - strategies.federated.scaffold - INFO - Round 10 completed in 30.90s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:40:29,841 - strategies.federated.scaffold - INFO - Round 11/15
2025-05-26 20:41:00,292 - strategies.federated.scaffold - INFO - Round 11 completed in 30.45s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:41:00,293 - strategies.federated.scaffold - INFO - Round 12/15
2025-05-26 20:41:29,939 - strategies.federated.scaffold - INFO - Round 12 completed in 29.65s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:41:29,940 - strategies.federated.scaffold - INFO - Round 13/15
2025-05-26 20:42:00,253 - strategies.federated.scaffold - INFO - Round 13 completed in 30.31s. Global loss: 0.6688, Avg client loss: 0.7357
2025-05-26 20:42:00,253 - strategies.federated.scaffold - INFO - Round 14/15
2025-05-26 20:42:31,278 - strategies.federated.scaffold - INFO - Round 14 completed in 31.02s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 20:42:31,279 - strategies.federated.scaffold - INFO - Round 15/15
2025-05-26 20:43:01,284 - strategies.federated.scaffold - INFO - Round 15 completed in 30.01s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 20:43:01,285 - strategies.federated.scaffold - INFO - SCAFFOLD training completed in 456.71s
2025-05-26 20:43:02,044 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210691283194.1866
2025-05-26 20:43:02,254 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9353, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1337487216801.8425
2025-05-26 20:43:02,507 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9281, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1368
2025-05-26 20:43:02,510 - utils.evaluation - INFO - Results logged to results/logs\scaffold_20250526_204302.json
2025-05-26 20:43:02,511 - utils.model_utils - WARNING - File results/models\scaffold_model.pkl already exists and overwrite=False
2025-05-26 20:43:03,205 - utils.visualization - INFO - Plot saved to results/plots\scaffold_actual_vs_pred.png
2025-05-26 20:43:03,797 - utils.visualization - INFO - Plot saved to results/plots\scaffold_residuals.png
2025-05-26 20:43:03,797 - __main__ - INFO - Running knowledge distillation strategy: attention_kd
2025-05-26 20:43:03,799 - strategies.distillation.attention_kd - INFO - Starting Attention KD training with sample_attention=True, feature_attention=True
2025-05-26 20:43:04,560 - strategies.distillation.attention_kd - INFO - Sample weights: min=0.0000, max=1.0870, mean=1.0000
2025-05-26 20:43:05,754 - strategies.distillation.attention_kd - INFO - Epoch 1/1 completed in 1.19s. Train loss: 0.7384, Val loss: 0.7525
2025-05-26 20:43:05,754 - strategies.distillation.attention_kd - INFO - Attention KD training completed in 1.95s
2025-05-26 20:43:05,782 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7384, train_MSE: 1.0725, train_RMSE: 1.0356, train_R2: 0.7790, train_EVS: 0.7790, train_MAPE: 253913686872.0969
2025-05-26 20:43:05,799 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7525, val_MSE: 1.1206, val_RMSE: 1.0586, val_R2: 0.7734, val_EVS: 0.7734, val_MAPE: 2867994740799.0005
2025-05-26 20:43:05,815 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7478, test_MSE: 1.1025, test_RMSE: 1.0500, test_R2: 0.7620, test_EVS: 0.7620, test_MAPE: 1.4391
2025-05-26 20:43:05,816 - utils.evaluation - INFO - Results logged to results/logs\attention_kd_20250526_204305.json
2025-05-26 20:43:05,817 - utils.model_utils - WARNING - File results/models\attention_kd_model.pkl already exists and overwrite=False
2025-05-26 20:43:06,597 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_actual_vs_pred.png
2025-05-26 20:43:07,229 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_residuals.png
2025-05-26 20:43:07,251 - utils.evaluation - INFO - Results logged to results/logs\scaffold_attention_kd_20250526_204307.json
2025-05-26 20:43:07,526 - utils.model_utils - INFO - Model saved to results/models\scaffold_attention_kd_model.pkl
2025-05-26 20:43:07,531 - __main__ - INFO - Running combined strategy: personalized_fl+vanilla_kd
2025-05-26 20:43:07,531 - __main__ - INFO - Running federated learning strategy: personalized_fl
2025-05-26 20:43:07,531 - strategies.federated.personalized_fl - INFO - Starting PersonalizedFL training with 5 clients, 15 rounds, and alpha=0.5
2025-05-26 20:43:07,537 - strategies.federated.personalized_fl - INFO - Round 1/15
2025-05-26 20:43:38,865 - strategies.federated.personalized_fl - INFO - Round 1 completed in 31.33s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:43:38,865 - strategies.federated.personalized_fl - INFO - Round 2/15
2025-05-26 20:44:10,444 - strategies.federated.personalized_fl - INFO - Round 2 completed in 31.58s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 20:44:10,444 - strategies.federated.personalized_fl - INFO - Round 3/15
2025-05-26 20:44:41,560 - strategies.federated.personalized_fl - INFO - Round 3 completed in 31.12s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 20:44:41,561 - strategies.federated.personalized_fl - INFO - Round 4/15
2025-05-26 20:45:12,689 - strategies.federated.personalized_fl - INFO - Round 4 completed in 31.13s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:45:12,690 - strategies.federated.personalized_fl - INFO - Round 5/15
2025-05-26 20:45:48,381 - strategies.federated.personalized_fl - INFO - Round 5 completed in 35.69s. Global loss: 0.6688, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 20:45:48,381 - strategies.federated.personalized_fl - INFO - Round 6/15
2025-05-26 20:46:21,779 - strategies.federated.personalized_fl - INFO - Round 6 completed in 33.40s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:46:21,779 - strategies.federated.personalized_fl - INFO - Round 7/15
2025-05-26 20:46:53,408 - strategies.federated.personalized_fl - INFO - Round 7 completed in 31.63s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:46:53,408 - strategies.federated.personalized_fl - INFO - Round 8/15
2025-05-26 20:47:28,585 - strategies.federated.personalized_fl - INFO - Round 8 completed in 35.18s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:47:28,586 - strategies.federated.personalized_fl - INFO - Round 9/15
2025-05-26 20:48:00,408 - strategies.federated.personalized_fl - INFO - Round 9 completed in 31.82s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:48:00,409 - strategies.federated.personalized_fl - INFO - Round 10/15
2025-05-26 20:48:32,298 - strategies.federated.personalized_fl - INFO - Round 10 completed in 31.89s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:48:32,299 - strategies.federated.personalized_fl - INFO - Round 11/15
2025-05-26 20:49:04,342 - strategies.federated.personalized_fl - INFO - Round 11 completed in 32.04s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:49:04,343 - strategies.federated.personalized_fl - INFO - Round 12/15
2025-05-26 20:49:37,021 - strategies.federated.personalized_fl - INFO - Round 12 completed in 32.68s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:49:37,021 - strategies.federated.personalized_fl - INFO - Round 13/15
2025-05-26 20:50:13,886 - strategies.federated.personalized_fl - INFO - Round 13 completed in 36.86s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:50:13,886 - strategies.federated.personalized_fl - INFO - Round 14/15
2025-05-26 20:50:57,562 - strategies.federated.personalized_fl - INFO - Round 14 completed in 43.68s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:50:57,563 - strategies.federated.personalized_fl - INFO - Round 15/15
2025-05-26 20:51:44,960 - strategies.federated.personalized_fl - INFO - Round 15 completed in 47.40s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:51:44,960 - strategies.federated.personalized_fl - INFO - PersonalizedFL training completed in 517.43s
2025-05-26 20:51:45,691 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6521, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 209037275298.2494
2025-05-26 20:51:45,898 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1347910770472.7585
2025-05-26 20:51:46,153 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9280, test_RMSE: 0.9633, test_R2: 0.7996, test_EVS: 0.7997, test_MAPE: 1.1379
2025-05-26 20:51:46,156 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_20250526_205146.json
2025-05-26 20:51:46,159 - utils.model_utils - WARNING - File results/models\personalized_fl_model.pkl already exists and overwrite=False
2025-05-26 20:51:47,011 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_actual_vs_pred.png
2025-05-26 20:51:47,616 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_residuals.png
2025-05-26 20:51:47,617 - __main__ - INFO - Running knowledge distillation strategy: vanilla_kd
2025-05-26 20:51:47,619 - strategies.distillation.vanilla_kd - INFO - Starting Vanilla KD training with alpha=0.5, temperature=1.0
2025-05-26 20:51:49,554 - strategies.distillation.vanilla_kd - INFO - Epoch 1/1 completed in 1.19s. Train loss: 0.6789, Val loss: 0.7345
2025-05-26 20:51:49,554 - strategies.distillation.vanilla_kd - INFO - Vanilla KD training completed in 1.94s
2025-05-26 20:51:49,583 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.6789, train_MSE: 0.8115, train_RMSE: 0.9009, train_R2: 0.8328, train_EVS: 0.8328, train_MAPE: 314248271172.6435
2025-05-26 20:51:49,597 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7345, val_MSE: 1.0274, val_RMSE: 1.0136, val_R2: 0.7922, val_EVS: 0.7923, val_MAPE: 1781787774008.0222
2025-05-26 20:51:49,614 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7272, test_MSE: 1.0090, test_RMSE: 1.0045, test_R2: 0.7821, test_EVS: 0.7821, test_MAPE: 1.7021
2025-05-26 20:51:49,615 - utils.evaluation - INFO - Results logged to results/logs\vanilla_kd_20250526_205149.json
2025-05-26 20:51:49,616 - utils.model_utils - WARNING - File results/models\vanilla_kd_model.pkl already exists and overwrite=False
2025-05-26 20:51:50,395 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_actual_vs_pred.png
2025-05-26 20:51:51,125 - utils.visualization - INFO - Plot saved to results/plots\vanilla_kd_residuals.png
2025-05-26 20:51:51,234 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_vanilla_kd_20250526_205151.json
2025-05-26 20:51:51,525 - utils.model_utils - INFO - Model saved to results/models\personalized_fl_vanilla_kd_model.pkl
2025-05-26 20:51:51,529 - __main__ - INFO - Running combined strategy: personalized_fl+ensemble_kd
2025-05-26 20:51:51,529 - __main__ - INFO - Running federated learning strategy: personalized_fl
2025-05-26 20:51:51,529 - strategies.federated.personalized_fl - INFO - Starting PersonalizedFL training with 5 clients, 15 rounds, and alpha=0.5
2025-05-26 20:51:51,531 - strategies.federated.personalized_fl - INFO - Round 1/15
2025-05-26 20:52:28,035 - strategies.federated.personalized_fl - INFO - Round 1 completed in 36.50s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:52:28,035 - strategies.federated.personalized_fl - INFO - Round 2/15
2025-05-26 20:53:03,437 - strategies.federated.personalized_fl - INFO - Round 2 completed in 35.40s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:53:03,437 - strategies.federated.personalized_fl - INFO - Round 3/15
2025-05-26 20:53:34,172 - strategies.federated.personalized_fl - INFO - Round 3 completed in 30.73s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:53:34,172 - strategies.federated.personalized_fl - INFO - Round 4/15
2025-05-26 20:54:08,186 - strategies.federated.personalized_fl - INFO - Round 4 completed in 34.01s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:54:08,187 - strategies.federated.personalized_fl - INFO - Round 5/15
2025-05-26 20:54:44,990 - strategies.federated.personalized_fl - INFO - Round 5 completed in 36.80s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 20:54:44,990 - strategies.federated.personalized_fl - INFO - Round 6/15
2025-05-26 20:55:16,052 - strategies.federated.personalized_fl - INFO - Round 6 completed in 31.06s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:55:16,052 - strategies.federated.personalized_fl - INFO - Round 7/15
2025-05-26 20:56:00,318 - strategies.federated.personalized_fl - INFO - Round 7 completed in 44.27s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 20:56:00,318 - strategies.federated.personalized_fl - INFO - Round 8/15
2025-05-26 20:56:32,157 - strategies.federated.personalized_fl - INFO - Round 8 completed in 31.84s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:56:32,157 - strategies.federated.personalized_fl - INFO - Round 9/15
2025-05-26 20:57:04,943 - strategies.federated.personalized_fl - INFO - Round 9 completed in 32.79s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 20:57:04,943 - strategies.federated.personalized_fl - INFO - Round 10/15
2025-05-26 20:57:36,732 - strategies.federated.personalized_fl - INFO - Round 10 completed in 31.79s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:57:36,733 - strategies.federated.personalized_fl - INFO - Round 11/15
2025-05-26 20:58:11,696 - strategies.federated.personalized_fl - INFO - Round 11 completed in 34.96s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 20:58:11,696 - strategies.federated.personalized_fl - INFO - Round 12/15
2025-05-26 20:58:49,478 - strategies.federated.personalized_fl - INFO - Round 12 completed in 37.78s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:58:49,479 - strategies.federated.personalized_fl - INFO - Round 13/15
2025-05-26 20:59:38,315 - strategies.federated.personalized_fl - INFO - Round 13 completed in 48.84s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 20:59:38,315 - strategies.federated.personalized_fl - INFO - Round 14/15
2025-05-26 21:00:15,576 - strategies.federated.personalized_fl - INFO - Round 14 completed in 37.26s. Global loss: 0.6686, Avg client loss: 0.7356, Avg personalized loss: 0.6857
2025-05-26 21:00:15,576 - strategies.federated.personalized_fl - INFO - Round 15/15
2025-05-26 21:00:52,770 - strategies.federated.personalized_fl - INFO - Round 15 completed in 37.19s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 21:00:52,770 - strategies.federated.personalized_fl - INFO - PersonalizedFL training completed in 541.24s
2025-05-26 21:00:53,654 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6521, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210404768417.3336
2025-05-26 21:00:53,872 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9670, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1337487210467.5002
2025-05-26 21:00:54,137 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9282, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1336
2025-05-26 21:00:54,139 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_20250526_210054.json
2025-05-26 21:00:54,140 - utils.model_utils - WARNING - File results/models\personalized_fl_model.pkl already exists and overwrite=False
2025-05-26 21:00:55,037 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_actual_vs_pred.png
2025-05-26 21:00:55,704 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_residuals.png
2025-05-26 21:00:55,705 - __main__ - INFO - Running knowledge distillation strategy: ensemble_kd
2025-05-26 21:00:55,707 - strategies.distillation.ensemble_kd - INFO - Starting Ensemble KD training with 1 teachers and alpha=0.5
2025-05-26 21:00:57,821 - strategies.distillation.ensemble_kd - INFO - Epoch 1/1 completed in 1.36s. Train loss: 0.7070, Val loss: 0.7385
2025-05-26 21:00:57,822 - strategies.distillation.ensemble_kd - INFO - Ensemble KD training completed in 2.11s
2025-05-26 21:00:57,863 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7070, train_MSE: 0.9607, train_RMSE: 0.9802, train_R2: 0.8020, train_EVS: 0.8020, train_MAPE: 268812488178.0745
2025-05-26 21:00:57,883 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7385, val_MSE: 1.0708, val_RMSE: 1.0348, val_R2: 0.7835, val_EVS: 0.7835, val_MAPE: 2503598999439.0098
2025-05-26 21:00:57,902 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7332, test_MSE: 1.0529, test_RMSE: 1.0261, test_R2: 0.7727, test_EVS: 0.7727, test_MAPE: 1.4021
2025-05-26 21:00:57,904 - utils.evaluation - INFO - Results logged to results/logs\ensemble_kd_20250526_210057.json
2025-05-26 21:00:57,905 - utils.model_utils - WARNING - File results/models\ensemble_kd_model.pkl already exists and overwrite=False
2025-05-26 21:00:58,802 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_actual_vs_pred.png
2025-05-26 21:00:59,481 - utils.visualization - INFO - Plot saved to results/plots\ensemble_kd_residuals.png
2025-05-26 21:00:59,484 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_ensemble_kd_20250526_210059.json
2025-05-26 21:00:59,979 - utils.model_utils - INFO - Model saved to results/models\personalized_fl_ensemble_kd_model.pkl
2025-05-26 21:00:59,984 - __main__ - INFO - Running combined strategy: personalized_fl+progressive_kd
2025-05-26 21:00:59,984 - __main__ - INFO - Running federated learning strategy: personalized_fl
2025-05-26 21:00:59,984 - strategies.federated.personalized_fl - INFO - Starting PersonalizedFL training with 5 clients, 15 rounds, and alpha=0.5
2025-05-26 21:00:59,987 - strategies.federated.personalized_fl - INFO - Round 1/15
2025-05-26 21:01:35,337 - strategies.federated.personalized_fl - INFO - Round 1 completed in 35.35s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:01:35,337 - strategies.federated.personalized_fl - INFO - Round 2/15
2025-05-26 21:02:07,212 - strategies.federated.personalized_fl - INFO - Round 2 completed in 31.88s. Global loss: 0.6688, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 21:02:07,213 - strategies.federated.personalized_fl - INFO - Round 3/15
2025-05-26 21:02:48,137 - strategies.federated.personalized_fl - INFO - Round 3 completed in 40.92s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:02:48,137 - strategies.federated.personalized_fl - INFO - Round 4/15
2025-05-26 21:03:31,470 - strategies.federated.personalized_fl - INFO - Round 4 completed in 43.33s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 21:03:31,470 - strategies.federated.personalized_fl - INFO - Round 5/15
2025-05-26 21:04:00,404 - strategies.federated.personalized_fl - INFO - Round 5 completed in 28.93s. Global loss: 0.6686, Avg client loss: 0.7356, Avg personalized loss: 0.6857
2025-05-26 21:04:00,404 - strategies.federated.personalized_fl - INFO - Round 6/15
2025-05-26 21:04:29,699 - strategies.federated.personalized_fl - INFO - Round 6 completed in 29.29s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 21:04:29,700 - strategies.federated.personalized_fl - INFO - Round 7/15
2025-05-26 21:05:01,540 - strategies.federated.personalized_fl - INFO - Round 7 completed in 31.84s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 21:05:01,541 - strategies.federated.personalized_fl - INFO - Round 8/15
2025-05-26 21:05:33,517 - strategies.federated.personalized_fl - INFO - Round 8 completed in 31.98s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:05:33,517 - strategies.federated.personalized_fl - INFO - Round 9/15
2025-05-26 21:06:05,758 - strategies.federated.personalized_fl - INFO - Round 9 completed in 32.24s. Global loss: 0.6688, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:06:05,759 - strategies.federated.personalized_fl - INFO - Round 10/15
2025-05-26 21:06:37,918 - strategies.federated.personalized_fl - INFO - Round 10 completed in 32.16s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 21:06:37,919 - strategies.federated.personalized_fl - INFO - Round 11/15
2025-05-26 21:07:08,133 - strategies.federated.personalized_fl - INFO - Round 11 completed in 30.21s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:07:08,133 - strategies.federated.personalized_fl - INFO - Round 12/15
2025-05-26 21:07:39,680 - strategies.federated.personalized_fl - INFO - Round 12 completed in 31.55s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:07:39,680 - strategies.federated.personalized_fl - INFO - Round 13/15
2025-05-26 21:08:10,721 - strategies.federated.personalized_fl - INFO - Round 13 completed in 31.04s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:08:10,721 - strategies.federated.personalized_fl - INFO - Round 14/15
2025-05-26 21:08:42,694 - strategies.federated.personalized_fl - INFO - Round 14 completed in 31.97s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:08:42,695 - strategies.federated.personalized_fl - INFO - Round 15/15
2025-05-26 21:09:14,963 - strategies.federated.personalized_fl - INFO - Round 15 completed in 32.27s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:09:14,964 - strategies.federated.personalized_fl - INFO - PersonalizedFL training completed in 494.98s
2025-05-26 21:09:15,739 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 209546822171.4138
2025-05-26 21:09:15,998 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9354, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1344549828975.4270
2025-05-26 21:09:16,281 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6691, test_MSE: 0.9280, test_RMSE: 0.9633, test_R2: 0.7996, test_EVS: 0.7997, test_MAPE: 1.1395
2025-05-26 21:09:16,285 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_20250526_210916.json
2025-05-26 21:09:16,289 - utils.model_utils - WARNING - File results/models\personalized_fl_model.pkl already exists and overwrite=False
2025-05-26 21:09:17,092 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_actual_vs_pred.png
2025-05-26 21:09:17,723 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_residuals.png
2025-05-26 21:09:17,724 - __main__ - INFO - Running knowledge distillation strategy: progressive_kd
2025-05-26 21:09:17,725 - __main__ - ERROR - Error running strategy personalized_fl_progressive_kd: ProgressiveKD.fit() got an unexpected keyword argument 'epochs'
2025-05-26 21:09:17,741 - __main__ - INFO - Running combined strategy: personalized_fl+attention_kd
2025-05-26 21:09:17,742 - __main__ - INFO - Running federated learning strategy: personalized_fl
2025-05-26 21:09:17,742 - strategies.federated.personalized_fl - INFO - Starting PersonalizedFL training with 5 clients, 15 rounds, and alpha=0.5
2025-05-26 21:09:17,745 - strategies.federated.personalized_fl - INFO - Round 1/15
2025-05-26 21:09:48,253 - strategies.federated.personalized_fl - INFO - Round 1 completed in 30.51s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:09:48,253 - strategies.federated.personalized_fl - INFO - Round 2/15
2025-05-26 21:10:19,500 - strategies.federated.personalized_fl - INFO - Round 2 completed in 31.25s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:10:19,500 - strategies.federated.personalized_fl - INFO - Round 3/15
2025-05-26 21:10:50,569 - strategies.federated.personalized_fl - INFO - Round 3 completed in 31.07s. Global loss: 0.6686, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 21:10:50,570 - strategies.federated.personalized_fl - INFO - Round 4/15
2025-05-26 21:11:22,581 - strategies.federated.personalized_fl - INFO - Round 4 completed in 32.01s. Global loss: 0.6686, Avg client loss: 0.7356, Avg personalized loss: 0.6857
2025-05-26 21:11:22,581 - strategies.federated.personalized_fl - INFO - Round 5/15
2025-05-26 21:11:53,089 - strategies.federated.personalized_fl - INFO - Round 5 completed in 30.51s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:11:53,090 - strategies.federated.personalized_fl - INFO - Round 6/15
2025-05-26 21:12:24,480 - strategies.federated.personalized_fl - INFO - Round 6 completed in 31.39s. Global loss: 0.6686, Avg client loss: 0.7356, Avg personalized loss: 0.6857
2025-05-26 21:12:24,480 - strategies.federated.personalized_fl - INFO - Round 7/15
2025-05-26 21:12:55,811 - strategies.federated.personalized_fl - INFO - Round 7 completed in 31.33s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:12:55,811 - strategies.federated.personalized_fl - INFO - Round 8/15
2025-05-26 21:13:26,643 - strategies.federated.personalized_fl - INFO - Round 8 completed in 30.83s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 21:13:26,643 - strategies.federated.personalized_fl - INFO - Round 9/15
2025-05-26 21:13:57,831 - strategies.federated.personalized_fl - INFO - Round 9 completed in 31.19s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:13:57,832 - strategies.federated.personalized_fl - INFO - Round 10/15
2025-05-26 21:14:31,964 - strategies.federated.personalized_fl - INFO - Round 10 completed in 34.13s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:14:31,964 - strategies.federated.personalized_fl - INFO - Round 11/15
2025-05-26 21:16:07,239 - strategies.federated.personalized_fl - INFO - Round 11 completed in 95.27s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 21:16:07,239 - strategies.federated.personalized_fl - INFO - Round 12/15
2025-05-26 21:16:39,999 - strategies.federated.personalized_fl - INFO - Round 12 completed in 32.76s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:16:39,999 - strategies.federated.personalized_fl - INFO - Round 13/15
2025-05-26 21:17:12,892 - strategies.federated.personalized_fl - INFO - Round 13 completed in 32.89s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6857
2025-05-26 21:17:12,892 - strategies.federated.personalized_fl - INFO - Round 14/15
2025-05-26 21:17:43,876 - strategies.federated.personalized_fl - INFO - Round 14 completed in 30.98s. Global loss: 0.6687, Avg client loss: 0.7358, Avg personalized loss: 0.6858
2025-05-26 21:17:43,877 - strategies.federated.personalized_fl - INFO - Round 15/15
2025-05-26 21:18:14,405 - strategies.federated.personalized_fl - INFO - Round 15 completed in 30.53s. Global loss: 0.6687, Avg client loss: 0.7357, Avg personalized loss: 0.6858
2025-05-26 21:18:14,406 - strategies.federated.personalized_fl - INFO - PersonalizedFL training completed in 536.66s
2025-05-26 21:18:15,182 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.5625, train_MSE: 0.6520, train_RMSE: 0.8075, train_R2: 0.8656, train_EVS: 0.8656, train_MAPE: 210914325136.1423
2025-05-26 21:18:15,476 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6687, val_MSE: 0.9352, val_RMSE: 0.9671, val_R2: 0.8109, val_EVS: 0.8109, val_MAPE: 1344549826819.2678
2025-05-26 21:18:15,807 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6692, test_MSE: 0.9282, test_RMSE: 0.9634, test_R2: 0.7996, test_EVS: 0.7996, test_MAPE: 1.1352
2025-05-26 21:18:15,810 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_20250526_211815.json
2025-05-26 21:18:15,811 - utils.model_utils - WARNING - File results/models\personalized_fl_model.pkl already exists and overwrite=False
2025-05-26 21:18:16,635 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_actual_vs_pred.png
2025-05-26 21:18:17,318 - utils.visualization - INFO - Plot saved to results/plots\personalized_fl_residuals.png
2025-05-26 21:18:17,318 - __main__ - INFO - Running knowledge distillation strategy: attention_kd
2025-05-26 21:18:17,319 - strategies.distillation.attention_kd - INFO - Starting Attention KD training with sample_attention=True, feature_attention=True
2025-05-26 21:18:18,080 - strategies.distillation.attention_kd - INFO - Sample weights: min=0.0000, max=1.0870, mean=1.0000
2025-05-26 21:18:19,367 - strategies.distillation.attention_kd - INFO - Epoch 1/1 completed in 1.29s. Train loss: 0.7374, Val loss: 0.7515
2025-05-26 21:18:19,367 - strategies.distillation.attention_kd - INFO - Attention KD training completed in 2.05s
2025-05-26 21:18:19,403 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.7374, train_MSE: 1.0706, train_RMSE: 1.0347, train_R2: 0.7794, train_EVS: 0.7794, train_MAPE: 241221309070.2544
2025-05-26 21:18:19,421 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.7515, val_MSE: 1.1188, val_RMSE: 1.0577, val_R2: 0.7738, val_EVS: 0.7738, val_MAPE: 2765436625739.3164
2025-05-26 21:18:19,441 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.7471, test_MSE: 1.1040, test_RMSE: 1.0507, test_R2: 0.7616, test_EVS: 0.7616, test_MAPE: 1.4282
2025-05-26 21:18:19,444 - utils.evaluation - INFO - Results logged to results/logs\attention_kd_20250526_211819.json
2025-05-26 21:18:19,445 - utils.model_utils - WARNING - File results/models\attention_kd_model.pkl already exists and overwrite=False
2025-05-26 21:18:20,206 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_actual_vs_pred.png
2025-05-26 21:18:20,921 - utils.visualization - INFO - Plot saved to results/plots\attention_kd_residuals.png
2025-05-26 21:18:20,924 - utils.evaluation - INFO - Results logged to results/logs\personalized_fl_attention_kd_20250526_211820.json
2025-05-26 21:18:21,213 - utils.model_utils - INFO - Model saved to results/models\personalized_fl_attention_kd_model.pkl
2025-05-26 21:18:21,218 - __main__ - INFO - Running contrastive learning strategy: simclr
2025-05-26 21:18:21,510 - strategies.contrastive.simclr - INFO - Training SimCLR on cuda
2025-05-26 21:18:23,584 - strategies.contrastive.simclr - INFO - Stage 1: Contrastive pre-training
2025-05-26 21:18:48,312 - strategies.contrastive.simclr - INFO - Contrastive Epoch 0/50, Loss: 1.6257
2025-05-26 21:18:59,996 - strategies.contrastive.simclr - INFO - Contrastive Epoch 10/50, Loss: 0.3514
2025-05-26 21:19:11,408 - strategies.contrastive.simclr - INFO - Contrastive Epoch 20/50, Loss: 0.2800
2025-05-26 21:19:23,379 - strategies.contrastive.simclr - INFO - Contrastive Epoch 30/50, Loss: 0.2574
2025-05-26 21:19:36,446 - strategies.contrastive.simclr - INFO - Contrastive Epoch 40/50, Loss: 0.2346
2025-05-26 21:19:47,483 - strategies.contrastive.simclr - INFO - Stage 2: Supervised fine-tuning
2025-05-26 21:19:48,303 - strategies.contrastive.simclr - INFO - Finetune Epoch 0/50, Loss: 12.2211
2025-05-26 21:19:53,438 - strategies.contrastive.simclr - INFO - Finetune Epoch 10/50, Loss: 3.0096
2025-05-26 21:19:58,539 - strategies.contrastive.simclr - INFO - Finetune Epoch 20/50, Loss: 2.5334
2025-05-26 21:20:03,704 - strategies.contrastive.simclr - INFO - Finetune Epoch 30/50, Loss: 2.4016
2025-05-26 21:20:08,789 - strategies.contrastive.simclr - INFO - Finetune Epoch 40/50, Loss: 2.2983
2025-05-26 21:20:13,486 - strategies.contrastive.simclr - INFO - SimCLR training completed in 111.98s
2025-05-26 21:20:13,631 - utils.evaluation - INFO - Model evaluation - train_MAE: 1.1250, train_MSE: 19.7448, train_RMSE: 4.4435, train_R2: -3.0690, train_EVS: -3.0626, train_MAPE: 504240541755.8214
2025-05-26 21:20:13,650 - utils.evaluation - INFO - Model evaluation - val_MAE: 1.0774, val_MSE: 2.0228, val_RMSE: 1.4222, val_R2: 0.5910, val_EVS: 0.6029, val_MAPE: 2289911477297.0112
2025-05-26 21:20:13,670 - utils.evaluation - INFO - Model evaluation - test_MAE: 1.0486, test_MSE: 1.8967, test_RMSE: 1.3772, test_R2: 0.5905, test_EVS: 0.6031, test_MAPE: 2.8075
2025-05-26 21:20:13,673 - utils.evaluation - INFO - Results logged to results/logs\simclr_20250526_212013.json
2025-05-26 21:20:13,855 - utils.model_utils - INFO - Model saved to results/models\simclr_model.pkl
2025-05-26 21:20:13,860 - __main__ - INFO - Running contrastive learning strategy: simclr
2025-05-26 21:20:13,861 - strategies.contrastive.simclr - INFO - Training SimCLR on cuda
2025-05-26 21:20:13,880 - strategies.contrastive.simclr - INFO - Stage 1: Contrastive pre-training
2025-05-26 21:20:15,105 - strategies.contrastive.simclr - INFO - Contrastive Epoch 0/50, Loss: 1.6254
2025-05-26 21:20:26,404 - strategies.contrastive.simclr - INFO - Contrastive Epoch 10/50, Loss: 0.3512
2025-05-26 21:20:37,654 - strategies.contrastive.simclr - INFO - Contrastive Epoch 20/50, Loss: 0.2724
2025-05-26 21:20:48,935 - strategies.contrastive.simclr - INFO - Contrastive Epoch 30/50, Loss: 0.2466
2025-05-26 21:21:00,056 - strategies.contrastive.simclr - INFO - Contrastive Epoch 40/50, Loss: 0.2333
2025-05-26 21:21:12,034 - strategies.contrastive.simclr - INFO - Stage 2: Supervised fine-tuning
2025-05-26 21:21:12,613 - strategies.contrastive.simclr - INFO - Finetune Epoch 0/50, Loss: 11.7573
2025-05-26 21:21:18,257 - strategies.contrastive.simclr - INFO - Finetune Epoch 10/50, Loss: 2.6661
2025-05-26 21:21:23,885 - strategies.contrastive.simclr - INFO - Finetune Epoch 20/50, Loss: 2.3761
2025-05-26 21:21:29,284 - strategies.contrastive.simclr - INFO - Finetune Epoch 30/50, Loss: 2.2792
2025-05-26 21:21:34,557 - strategies.contrastive.simclr - INFO - Finetune Epoch 40/50, Loss: 2.1331
2025-05-26 21:21:43,676 - strategies.contrastive.simclr - INFO - SimCLR training completed in 89.81s
2025-05-26 21:21:43,726 - utils.evaluation - INFO - Model evaluation - train_MAE: 1.4099, train_MSE: 3.3111, train_RMSE: 1.8197, train_R2: 0.3176, train_EVS: 0.3180, train_MAPE: 993733113100.2799
2025-05-26 21:21:43,755 - utils.evaluation - INFO - Model evaluation - val_MAE: 1.4065, val_MSE: 3.2058, val_RMSE: 1.7905, val_R2: 0.3517, val_EVS: 0.3527, val_MAPE: 4027933452455.8140
2025-05-26 21:21:43,774 - utils.evaluation - INFO - Model evaluation - test_MAE: 1.3639, test_MSE: 2.9994, test_RMSE: 1.7319, test_R2: 0.3524, test_EVS: 0.3528, test_MAPE: 4.1194
2025-05-26 21:21:43,789 - utils.evaluation - INFO - Results logged to results/logs\simclr_20250526_212143.json
2025-05-26 21:21:43,792 - utils.model_utils - WARNING - File results/models\simclr_model.pkl already exists and overwrite=False
2025-05-26 21:21:43,794 - __main__ - INFO - Running contrastive learning strategy: simclr
2025-05-26 21:21:43,795 - strategies.contrastive.simclr - INFO - Training SimCLR on cuda
2025-05-26 21:21:43,829 - strategies.contrastive.simclr - INFO - Stage 1: Contrastive pre-training
2025-05-26 21:21:46,789 - strategies.contrastive.simclr - INFO - Contrastive Epoch 0/50, Loss: 1.5793
2025-05-26 21:22:13,132 - strategies.contrastive.simclr - INFO - Contrastive Epoch 10/50, Loss: 0.3563
2025-05-26 21:22:41,302 - strategies.contrastive.simclr - INFO - Contrastive Epoch 20/50, Loss: 0.2816
2025-05-26 21:23:04,360 - strategies.contrastive.simclr - INFO - Contrastive Epoch 30/50, Loss: 0.2495
2025-05-26 21:23:17,046 - strategies.contrastive.simclr - INFO - Contrastive Epoch 40/50, Loss: 0.2284
2025-05-26 21:23:26,891 - strategies.contrastive.simclr - INFO - Stage 2: Supervised fine-tuning
2025-05-26 21:23:27,384 - strategies.contrastive.simclr - INFO - Finetune Epoch 0/50, Loss: 12.0434
2025-05-26 21:23:32,292 - strategies.contrastive.simclr - INFO - Finetune Epoch 10/50, Loss: 2.9977
2025-05-26 21:23:37,368 - strategies.contrastive.simclr - INFO - Finetune Epoch 20/50, Loss: 2.5776
2025-05-26 21:23:42,348 - strategies.contrastive.simclr - INFO - Finetune Epoch 30/50, Loss: 2.4227
2025-05-26 21:23:47,357 - strategies.contrastive.simclr - INFO - Finetune Epoch 40/50, Loss: 2.2745
2025-05-26 21:23:51,877 - strategies.contrastive.simclr - INFO - SimCLR training completed in 128.08s
2025-05-26 21:23:51,911 - utils.evaluation - INFO - Model evaluation - train_MAE: 1.7059, train_MSE: 4.6933, train_RMSE: 2.1664, train_R2: 0.0328, train_EVS: 0.0372, train_MAPE: 1284143718687.7432
2025-05-26 21:23:51,927 - utils.evaluation - INFO - Model evaluation - val_MAE: 1.7183, val_MSE: 4.7800, val_RMSE: 2.1863, val_R2: 0.0334, val_EVS: 0.0398, val_MAPE: 5221419332546.0264
2025-05-26 21:23:51,940 - utils.evaluation - INFO - Model evaluation - test_MAE: 1.6599, test_MSE: 4.4767, test_RMSE: 2.1158, test_R2: 0.0334, test_EVS: 0.0382, test_MAPE: 4.9732
2025-05-26 21:23:51,942 - utils.evaluation - INFO - Results logged to results/logs\simclr_20250526_212351.json
2025-05-26 21:23:51,945 - utils.model_utils - WARNING - File results/models\simclr_model.pkl already exists and overwrite=False
2025-05-26 21:23:51,946 - __main__ - INFO - Running transfer learning strategy: domain_adaptation
2025-05-26 21:23:51,946 - strategies.transfer.domain_adaptation - INFO - Training Domain Adaptation on cuda
2025-05-26 21:23:53,906 - strategies.transfer.domain_adaptation - INFO - Epoch 0/100, Task Loss: 3.2615, Domain Loss: 0.6894, Alpha: 0.000
2025-05-26 21:24:26,947 - strategies.transfer.domain_adaptation - INFO - Epoch 20/100, Task Loss: 1.0057, Domain Loss: 0.6909, Alpha: 0.762
2025-05-26 21:25:03,401 - strategies.transfer.domain_adaptation - INFO - Epoch 40/100, Task Loss: 0.8339, Domain Loss: 0.6924, Alpha: 0.964
2025-05-26 21:25:41,104 - strategies.transfer.domain_adaptation - INFO - Epoch 60/100, Task Loss: 0.7657, Domain Loss: 0.6927, Alpha: 0.995
2025-05-26 21:26:18,630 - strategies.transfer.domain_adaptation - INFO - Epoch 80/100, Task Loss: 0.7226, Domain Loss: 0.6923, Alpha: 0.999
2025-05-26 21:26:55,933 - strategies.transfer.domain_adaptation - INFO - Domain Adaptation training completed in 183.99s
2025-05-26 21:26:55,966 - utils.evaluation - INFO - Model evaluation - train_MAE: 1.3303, train_MSE: 4.1673, train_RMSE: 2.0414, train_R2: 0.1412, train_EVS: 0.1708, train_MAPE: 758251874317.8352
2025-05-26 21:26:55,979 - utils.evaluation - INFO - Model evaluation - val_MAE: 1.3500, val_MSE: 3.0102, val_RMSE: 1.7350, val_R2: 0.3913, val_EVS: 0.4266, val_MAPE: 3322296536979.2046
2025-05-26 21:26:55,995 - utils.evaluation - INFO - Model evaluation - test_MAE: 1.3057, test_MSE: 2.8126, test_RMSE: 1.6771, test_R2: 0.3927, test_EVS: 0.4266, test_MAPE: 3.6649
2025-05-26 21:26:55,997 - utils.evaluation - INFO - Results logged to results/logs\domain_adaptation_20250526_212655.json
2025-05-26 21:26:56,105 - utils.model_utils - INFO - Model saved to results/models\domain_adaptation_model.pkl
2025-05-26 21:26:56,105 - __main__ - INFO - Running transfer learning strategy: domain_adaptation
2025-05-26 21:26:56,107 - strategies.transfer.domain_adaptation - INFO - Training Domain Adaptation on cuda
2025-05-26 21:26:57,802 - strategies.transfer.domain_adaptation - INFO - Epoch 0/100, Task Loss: 3.3601, Domain Loss: 0.6840, Alpha: 0.000
2025-05-26 21:27:33,106 - strategies.transfer.domain_adaptation - INFO - Epoch 20/100, Task Loss: 0.9724, Domain Loss: 0.6920, Alpha: 0.762
2025-05-26 21:28:24,463 - strategies.transfer.domain_adaptation - INFO - Epoch 40/100, Task Loss: 0.8402, Domain Loss: 0.6915, Alpha: 0.964
2025-05-26 21:29:35,733 - strategies.transfer.domain_adaptation - INFO - Epoch 60/100, Task Loss: 0.7556, Domain Loss: 0.6912, Alpha: 0.995
2025-05-26 21:30:17,734 - strategies.transfer.domain_adaptation - INFO - Epoch 80/100, Task Loss: 0.6654, Domain Loss: 0.6919, Alpha: 0.999
2025-05-26 21:30:55,715 - strategies.transfer.domain_adaptation - INFO - Domain Adaptation training completed in 239.61s
2025-05-26 21:30:55,885 - utils.evaluation - INFO - Model evaluation - train_MAE: 1.6781, train_MSE: 4.6183, train_RMSE: 2.1490, train_R2: 0.0483, train_EVS: 0.0758, train_MAPE: 1141263630255.0217
2025-05-26 21:30:55,900 - utils.evaluation - INFO - Model evaluation - val_MAE: 1.6897, val_MSE: 4.7132, val_RMSE: 2.1710, val_R2: 0.0469, val_EVS: 0.0780, val_MAPE: 4678671580926.6475
2025-05-26 21:30:55,925 - utils.evaluation - INFO - Model evaluation - test_MAE: 1.6377, test_MSE: 4.4154, test_RMSE: 2.1013, test_R2: 0.0467, test_EVS: 0.0760, test_MAPE: 4.4185
2025-05-26 21:30:55,966 - utils.evaluation - INFO - Results logged to results/logs\domain_adaptation_20250526_213055.json
2025-05-26 21:30:55,967 - utils.model_utils - WARNING - File results/models\domain_adaptation_model.pkl already exists and overwrite=False
2025-05-26 21:30:55,970 - __main__ - INFO - Running transfer learning strategy: domain_adaptation
2025-05-26 21:30:55,970 - strategies.transfer.domain_adaptation - INFO - Training Domain Adaptation on cuda
2025-05-26 21:30:57,683 - strategies.transfer.domain_adaptation - INFO - Epoch 0/100, Task Loss: 3.1139, Domain Loss: 0.6889, Alpha: 0.000
2025-05-26 21:31:34,602 - strategies.transfer.domain_adaptation - INFO - Epoch 20/100, Task Loss: 0.9707, Domain Loss: 0.6918, Alpha: 0.762
2025-05-26 21:32:19,101 - strategies.transfer.domain_adaptation - INFO - Epoch 40/100, Task Loss: 0.8218, Domain Loss: 0.6918, Alpha: 0.964
2025-05-26 21:33:00,831 - strategies.transfer.domain_adaptation - INFO - Epoch 60/100, Task Loss: 0.7116, Domain Loss: 0.6922, Alpha: 0.995
2025-05-26 21:33:42,042 - strategies.transfer.domain_adaptation - INFO - Epoch 80/100, Task Loss: 0.6851, Domain Loss: 0.6917, Alpha: 0.999
2025-05-26 21:34:21,322 - strategies.transfer.domain_adaptation - INFO - Domain Adaptation training completed in 205.35s
2025-05-26 21:34:21,357 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.4475, train_MSE: 13.1834, train_RMSE: 3.6309, train_R2: -1.7168, train_EVS: -1.7133, train_MAPE: 118204317212.5650
2025-05-26 21:34:21,371 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.6788, val_MSE: 0.9203, val_RMSE: 0.9593, val_R2: 0.8139, val_EVS: 0.8149, val_MAPE: 245747544175.3516
2025-05-26 21:34:21,384 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.6472, test_MSE: 0.8656, test_RMSE: 0.9304, test_R2: 0.8131, test_EVS: 0.8151, test_MAPE: 1.6038
2025-05-26 21:34:21,386 - utils.evaluation - INFO - Results logged to results/logs\domain_adaptation_20250526_213421.json
2025-05-26 21:34:21,387 - utils.model_utils - WARNING - File results/models\domain_adaptation_model.pkl already exists and overwrite=False
2025-05-26 21:34:21,387 - __main__ - INFO - Running federated learning strategy: fedopt
2025-05-26 21:34:21,387 - strategies.federated.fedopt - INFO - Training FedOpt (adam) with 5 clients
2025-05-26 21:34:21,818 - utils.data_utils - INFO - Client 1: 467 samples, mean(y)=-2.9696, std(y)=2.2321
2025-05-26 21:34:21,819 - utils.data_utils - INFO - Client 2: 467 samples, mean(y)=-2.8337, std(y)=2.1996
2025-05-26 21:34:21,820 - utils.data_utils - INFO - Client 3: 467 samples, mean(y)=-2.8870, std(y)=2.1844
2025-05-26 21:34:21,821 - utils.data_utils - INFO - Client 4: 467 samples, mean(y)=-2.8729, std(y)=2.2240
2025-05-26 21:34:21,821 - utils.data_utils - INFO - Client 5: 467 samples, mean(y)=-2.9155, std(y)=2.2870
2025-05-26 21:34:22,556 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:23,510 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 91.228317
2025-05-26 21:34:23,604 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 133.733673
2025-05-26 21:34:23,682 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 56.370209
2025-05-26 21:34:23,749 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 83.452606
2025-05-26 21:34:23,810 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 97.144379
2025-05-26 21:34:23,888 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 59.834156
2025-05-26 21:34:23,951 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 46.493404
2025-05-26 21:34:24,011 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 49.070965
2025-05-26 21:34:24,074 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 62.463287
2025-05-26 21:34:24,138 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 41.072010
2025-05-26 21:34:24,138 - utils.neural_models - INFO - Neural network training completed in 1.60s
2025-05-26 21:34:24,139 - strategies.federated.fedopt - INFO - Round 1/15
2025-05-26 21:34:24,153 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:24,668 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.132121
2025-05-26 21:34:25,140 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.842381
2025-05-26 21:34:25,598 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 17.359419
2025-05-26 21:34:26,066 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.090695
2025-05-26 21:34:26,528 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.089157
2025-05-26 21:34:27,020 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.275685
2025-05-26 21:34:27,590 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.905394
2025-05-26 21:34:28,083 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.955356
2025-05-26 21:34:28,602 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.521398
2025-05-26 21:34:29,103 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.238944
2025-05-26 21:34:29,103 - utils.neural_models - INFO - Neural network training completed in 4.95s
2025-05-26 21:34:29,111 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:29,692 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 38.068025
2025-05-26 21:34:30,196 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.994022
2025-05-26 21:34:30,697 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.567640
2025-05-26 21:34:31,213 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 9.984222
2025-05-26 21:34:31,761 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.127793
2025-05-26 21:34:32,357 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 7.741315
2025-05-26 21:34:32,941 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.862890
2025-05-26 21:34:33,482 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.901246
2025-05-26 21:34:34,073 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 6.858377
2025-05-26 21:34:34,623 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.438857
2025-05-26 21:34:34,623 - utils.neural_models - INFO - Neural network training completed in 5.51s
2025-05-26 21:34:34,628 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:35,228 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.980655
2025-05-26 21:34:35,778 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.860067
2025-05-26 21:34:36,397 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 18.175442
2025-05-26 21:34:36,958 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.573105
2025-05-26 21:34:37,497 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.420364
2025-05-26 21:34:38,092 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.895581
2025-05-26 21:34:38,638 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 10.185266
2025-05-26 21:34:39,180 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 8.608988
2025-05-26 21:34:39,720 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.112794
2025-05-26 21:34:40,291 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.925248
2025-05-26 21:34:40,291 - utils.neural_models - INFO - Neural network training completed in 5.66s
2025-05-26 21:34:40,296 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:40,885 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 34.451930
2025-05-26 21:34:41,425 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.438795
2025-05-26 21:34:42,009 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.030344
2025-05-26 21:34:42,612 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 12.161887
2025-05-26 21:34:43,190 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.437900
2025-05-26 21:34:43,782 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 9.957514
2025-05-26 21:34:44,397 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.000638
2025-05-26 21:34:44,957 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 9.068989
2025-05-26 21:34:45,513 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.386724
2025-05-26 21:34:46,097 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.450850
2025-05-26 21:34:46,098 - utils.neural_models - INFO - Neural network training completed in 5.80s
2025-05-26 21:34:46,103 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:46,686 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 36.887998
2025-05-26 21:34:47,271 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 20.268556
2025-05-26 21:34:47,837 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.571893
2025-05-26 21:34:48,399 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.981150
2025-05-26 21:34:48,953 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.365538
2025-05-26 21:34:49,529 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 10.387160
2025-05-26 21:34:50,100 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.626738
2025-05-26 21:34:50,638 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.345769
2025-05-26 21:34:51,202 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.976543
2025-05-26 21:34:51,747 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 8.073163
2025-05-26 21:34:51,747 - utils.neural_models - INFO - Neural network training completed in 5.64s
2025-05-26 21:34:51,886 - __main__ - ERROR - Error running strategy fedopt_adam: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'
2025-05-26 21:34:51,887 - __main__ - INFO - Running federated learning strategy: fedopt
2025-05-26 21:34:51,888 - strategies.federated.fedopt - INFO - Training FedOpt (yogi) with 5 clients
2025-05-26 21:34:51,898 - utils.data_utils - INFO - Client 1: 467 samples, mean(y)=-2.9696, std(y)=2.2321
2025-05-26 21:34:51,900 - utils.data_utils - INFO - Client 2: 467 samples, mean(y)=-2.8337, std(y)=2.1996
2025-05-26 21:34:51,900 - utils.data_utils - INFO - Client 3: 467 samples, mean(y)=-2.8870, std(y)=2.1844
2025-05-26 21:34:51,901 - utils.data_utils - INFO - Client 4: 467 samples, mean(y)=-2.8729, std(y)=2.2240
2025-05-26 21:34:51,902 - utils.data_utils - INFO - Client 5: 467 samples, mean(y)=-2.9155, std(y)=2.2870
2025-05-26 21:34:51,904 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:52,025 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 91.228317
2025-05-26 21:34:52,144 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 133.733673
2025-05-26 21:34:52,242 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 56.370209
2025-05-26 21:34:52,330 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 83.452606
2025-05-26 21:34:52,398 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 97.144379
2025-05-26 21:34:52,463 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 59.834156
2025-05-26 21:34:52,527 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 46.493404
2025-05-26 21:34:52,583 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 49.070965
2025-05-26 21:34:52,638 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 62.463287
2025-05-26 21:34:52,692 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 41.072010
2025-05-26 21:34:52,692 - utils.neural_models - INFO - Neural network training completed in 0.79s
2025-05-26 21:34:52,694 - strategies.federated.fedopt - INFO - Round 1/15
2025-05-26 21:34:52,698 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:53,196 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.132121
2025-05-26 21:34:53,644 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.842381
2025-05-26 21:34:54,114 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 17.359419
2025-05-26 21:34:54,576 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.090695
2025-05-26 21:34:55,015 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.089157
2025-05-26 21:34:55,454 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.275685
2025-05-26 21:34:55,895 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.905394
2025-05-26 21:34:56,342 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.955356
2025-05-26 21:34:56,780 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.521398
2025-05-26 21:34:57,268 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.238944
2025-05-26 21:34:57,269 - utils.neural_models - INFO - Neural network training completed in 4.57s
2025-05-26 21:34:57,273 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:34:57,753 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 38.068025
2025-05-26 21:34:58,230 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.994022
2025-05-26 21:34:58,703 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.567640
2025-05-26 21:34:59,194 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 9.984222
2025-05-26 21:34:59,708 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.127793
2025-05-26 21:35:00,190 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 7.741315
2025-05-26 21:35:00,631 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.862890
2025-05-26 21:35:01,136 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.901246
2025-05-26 21:35:01,600 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 6.858377
2025-05-26 21:35:02,063 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.438857
2025-05-26 21:35:02,064 - utils.neural_models - INFO - Neural network training completed in 4.79s
2025-05-26 21:35:02,067 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:02,556 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.980655
2025-05-26 21:35:03,039 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.860067
2025-05-26 21:35:03,536 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 18.175442
2025-05-26 21:35:03,984 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.573105
2025-05-26 21:35:04,442 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.420364
2025-05-26 21:35:04,864 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.895581
2025-05-26 21:35:05,320 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 10.185266
2025-05-26 21:35:05,773 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 8.608988
2025-05-26 21:35:06,233 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.112794
2025-05-26 21:35:06,696 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.925248
2025-05-26 21:35:06,696 - utils.neural_models - INFO - Neural network training completed in 4.63s
2025-05-26 21:35:06,699 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:07,207 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 34.451930
2025-05-26 21:35:07,699 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.438795
2025-05-26 21:35:08,199 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.030344
2025-05-26 21:35:08,685 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 12.161887
2025-05-26 21:35:09,162 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.437900
2025-05-26 21:35:09,629 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 9.957514
2025-05-26 21:35:10,104 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.000638
2025-05-26 21:35:10,580 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 9.068989
2025-05-26 21:35:11,038 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.386724
2025-05-26 21:35:11,472 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.450850
2025-05-26 21:35:11,472 - utils.neural_models - INFO - Neural network training completed in 4.77s
2025-05-26 21:35:11,477 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:11,956 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 36.887998
2025-05-26 21:35:12,445 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 20.268556
2025-05-26 21:35:12,957 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.571893
2025-05-26 21:35:13,433 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.981150
2025-05-26 21:35:13,925 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.365538
2025-05-26 21:35:14,375 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 10.387160
2025-05-26 21:35:14,831 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.626738
2025-05-26 21:35:15,322 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.345769
2025-05-26 21:35:15,778 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.976543
2025-05-26 21:35:16,261 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 8.073163
2025-05-26 21:35:16,262 - utils.neural_models - INFO - Neural network training completed in 4.79s
2025-05-26 21:35:16,266 - __main__ - ERROR - Error running strategy fedopt_yogi: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'
2025-05-26 21:35:16,267 - __main__ - INFO - Running federated learning strategy: fedopt
2025-05-26 21:35:16,267 - strategies.federated.fedopt - INFO - Training FedOpt (adagrad) with 5 clients
2025-05-26 21:35:16,284 - utils.data_utils - INFO - Client 1: 467 samples, mean(y)=-2.9696, std(y)=2.2321
2025-05-26 21:35:16,285 - utils.data_utils - INFO - Client 2: 467 samples, mean(y)=-2.8337, std(y)=2.1996
2025-05-26 21:35:16,285 - utils.data_utils - INFO - Client 3: 467 samples, mean(y)=-2.8870, std(y)=2.1844
2025-05-26 21:35:16,285 - utils.data_utils - INFO - Client 4: 467 samples, mean(y)=-2.8729, std(y)=2.2240
2025-05-26 21:35:16,286 - utils.data_utils - INFO - Client 5: 467 samples, mean(y)=-2.9155, std(y)=2.2870
2025-05-26 21:35:16,288 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:16,362 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 91.228317
2025-05-26 21:35:16,419 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 133.733673
2025-05-26 21:35:16,479 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 56.370209
2025-05-26 21:35:16,542 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 83.452606
2025-05-26 21:35:16,614 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 97.144379
2025-05-26 21:35:16,682 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 59.834156
2025-05-26 21:35:16,746 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 46.493404
2025-05-26 21:35:16,807 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 49.070965
2025-05-26 21:35:16,881 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 62.463287
2025-05-26 21:35:16,943 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 41.072010
2025-05-26 21:35:16,943 - utils.neural_models - INFO - Neural network training completed in 0.66s
2025-05-26 21:35:16,945 - strategies.federated.fedopt - INFO - Round 1/15
2025-05-26 21:35:16,947 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:17,448 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.132121
2025-05-26 21:35:17,983 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.842381
2025-05-26 21:35:18,507 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 17.359419
2025-05-26 21:35:19,001 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.090695
2025-05-26 21:35:19,576 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.089157
2025-05-26 21:35:20,202 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.275685
2025-05-26 21:35:20,746 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.905394
2025-05-26 21:35:21,320 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.955356
2025-05-26 21:35:21,879 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.521398
2025-05-26 21:35:22,459 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.238944
2025-05-26 21:35:22,459 - utils.neural_models - INFO - Neural network training completed in 5.51s
2025-05-26 21:35:22,466 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:23,062 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 38.068025
2025-05-26 21:35:23,671 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.994022
2025-05-26 21:35:24,264 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.567640
2025-05-26 21:35:24,842 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 9.984222
2025-05-26 21:35:25,406 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.127793
2025-05-26 21:35:25,983 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 7.741315
2025-05-26 21:35:26,597 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.862890
2025-05-26 21:35:27,192 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.901246
2025-05-26 21:35:27,739 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 6.858377
2025-05-26 21:35:28,323 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.438857
2025-05-26 21:35:28,324 - utils.neural_models - INFO - Neural network training completed in 5.86s
2025-05-26 21:35:28,328 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:28,953 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.980655
2025-05-26 21:35:29,530 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.860067
2025-05-26 21:35:30,159 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 18.175442
2025-05-26 21:35:30,757 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.573105
2025-05-26 21:35:31,337 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.420364
2025-05-26 21:35:31,901 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.895581
2025-05-26 21:35:32,487 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 10.185266
2025-05-26 21:35:33,109 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 8.608988
2025-05-26 21:35:33,739 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.112794
2025-05-26 21:35:34,218 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.925248
2025-05-26 21:35:34,218 - utils.neural_models - INFO - Neural network training completed in 5.89s
2025-05-26 21:35:34,222 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:34,713 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 34.451930
2025-05-26 21:35:35,209 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.438795
2025-05-26 21:35:35,652 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.030344
2025-05-26 21:35:36,129 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 12.161887
2025-05-26 21:35:36,601 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.437900
2025-05-26 21:35:37,050 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 9.957514
2025-05-26 21:35:37,503 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.000638
2025-05-26 21:35:37,952 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 9.068989
2025-05-26 21:35:38,412 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.386724
2025-05-26 21:35:38,891 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.450850
2025-05-26 21:35:38,891 - utils.neural_models - INFO - Neural network training completed in 4.67s
2025-05-26 21:35:38,895 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:39,406 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 36.887998
2025-05-26 21:35:39,897 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 20.268556
2025-05-26 21:35:40,414 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.571893
2025-05-26 21:35:40,868 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.981150
2025-05-26 21:35:41,306 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.365538
2025-05-26 21:35:41,758 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 10.387160
2025-05-26 21:35:42,290 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.626738
2025-05-26 21:35:42,806 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.345769
2025-05-26 21:35:43,353 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.976543
2025-05-26 21:35:43,819 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 8.073163
2025-05-26 21:35:43,820 - utils.neural_models - INFO - Neural network training completed in 4.92s
2025-05-26 21:35:43,824 - __main__ - ERROR - Error running strategy fedopt_adagrad: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'
2025-05-26 21:35:43,825 - __main__ - INFO - Running combined strategy: fedopt+vanilla_kd
2025-05-26 21:35:43,826 - __main__ - INFO - Running federated learning strategy: fedopt
2025-05-26 21:35:43,826 - strategies.federated.fedopt - INFO - Training FedOpt (adam) with 5 clients
2025-05-26 21:35:43,839 - utils.data_utils - INFO - Client 1: 467 samples, mean(y)=-2.9696, std(y)=2.2321
2025-05-26 21:35:43,841 - utils.data_utils - INFO - Client 2: 467 samples, mean(y)=-2.8337, std(y)=2.1996
2025-05-26 21:35:43,842 - utils.data_utils - INFO - Client 3: 467 samples, mean(y)=-2.8870, std(y)=2.1844
2025-05-26 21:35:43,842 - utils.data_utils - INFO - Client 4: 467 samples, mean(y)=-2.8729, std(y)=2.2240
2025-05-26 21:35:43,843 - utils.data_utils - INFO - Client 5: 467 samples, mean(y)=-2.9155, std(y)=2.2870
2025-05-26 21:35:43,844 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:43,922 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 91.228317
2025-05-26 21:35:43,978 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 133.733673
2025-05-26 21:35:44,039 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 56.370209
2025-05-26 21:35:44,098 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 83.452606
2025-05-26 21:35:44,155 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 97.144379
2025-05-26 21:35:44,216 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 59.834156
2025-05-26 21:35:44,277 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 46.493404
2025-05-26 21:35:44,340 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 49.070965
2025-05-26 21:35:44,397 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 62.463287
2025-05-26 21:35:44,466 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 41.072010
2025-05-26 21:35:44,467 - utils.neural_models - INFO - Neural network training completed in 0.62s
2025-05-26 21:35:44,470 - strategies.federated.fedopt - INFO - Round 1/15
2025-05-26 21:35:44,472 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:44,959 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.132121
2025-05-26 21:35:45,412 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.842381
2025-05-26 21:35:45,902 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 17.359419
2025-05-26 21:35:46,371 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.090695
2025-05-26 21:35:46,852 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.089157
2025-05-26 21:35:47,333 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.275685
2025-05-26 21:35:47,807 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.905394
2025-05-26 21:35:48,312 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.955356
2025-05-26 21:35:48,845 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.521398
2025-05-26 21:35:49,352 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.238944
2025-05-26 21:35:49,352 - utils.neural_models - INFO - Neural network training completed in 4.88s
2025-05-26 21:35:49,356 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:49,861 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 38.068025
2025-05-26 21:35:50,374 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.994022
2025-05-26 21:35:50,826 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.567640
2025-05-26 21:35:51,318 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 9.984222
2025-05-26 21:35:51,805 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.127793
2025-05-26 21:35:52,312 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 7.741315
2025-05-26 21:35:52,795 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 7.862890
2025-05-26 21:35:53,284 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.901246
2025-05-26 21:35:53,766 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 6.858377
2025-05-26 21:35:54,246 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.438857
2025-05-26 21:35:54,247 - utils.neural_models - INFO - Neural network training completed in 4.89s
2025-05-26 21:35:54,254 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:54,701 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 40.980655
2025-05-26 21:35:55,152 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 19.860067
2025-05-26 21:35:55,608 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 18.175442
2025-05-26 21:35:56,138 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.573105
2025-05-26 21:35:56,612 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.420364
2025-05-26 21:35:57,077 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 8.895581
2025-05-26 21:35:57,555 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 10.185266
2025-05-26 21:35:58,008 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 8.608988
2025-05-26 21:35:58,458 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 7.112794
2025-05-26 21:35:58,917 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 6.925248
2025-05-26 21:35:58,917 - utils.neural_models - INFO - Neural network training completed in 4.66s
2025-05-26 21:35:58,922 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:35:59,430 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 34.451930
2025-05-26 21:35:59,911 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 18.438795
2025-05-26 21:36:00,397 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.030344
2025-05-26 21:36:00,869 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 12.161887
2025-05-26 21:36:01,321 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.437900
2025-05-26 21:36:01,772 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 9.957514
2025-05-26 21:36:02,236 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.000638
2025-05-26 21:36:02,679 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 9.068989
2025-05-26 21:36:03,218 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.386724
2025-05-26 21:36:03,809 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 7.450850
2025-05-26 21:36:03,809 - utils.neural_models - INFO - Neural network training completed in 4.89s
2025-05-26 21:36:03,815 - utils.neural_models - INFO - Training neural network on cuda
2025-05-26 21:36:04,340 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 36.887998
2025-05-26 21:36:04,784 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 20.268556
2025-05-26 21:36:05,270 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 14.571893
2025-05-26 21:36:05,739 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 11.981150
2025-05-26 21:36:06,189 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 10.365538
2025-05-26 21:36:06,647 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 10.387160
2025-05-26 21:36:07,135 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 8.626738
2025-05-26 21:36:07,570 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 7.345769
2025-05-26 21:36:08,001 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 8.976543
2025-05-26 21:36:08,463 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 8.073163
2025-05-26 21:36:08,463 - utils.neural_models - INFO - Neural network training completed in 4.65s
2025-05-26 21:36:08,469 - __main__ - ERROR - Error running strategy fedopt_adam_vanilla_kd: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'
2025-05-26 21:36:10,475 - utils.visualization - INFO - Plot saved to results/plots\strategy_comparison_mae.png
2025-05-26 21:36:11,469 - utils.visualization - INFO - Plot saved to results/plots\strategy_comparison_mse.png
2025-05-26 21:36:12,504 - utils.visualization - INFO - Plot saved to results/plots\strategy_comparison_rmse.png
2025-05-26 21:36:13,572 - utils.visualization - INFO - Plot saved to results/plots\strategy_comparison_r2.png
2025-05-26 21:36:14,640 - utils.visualization - INFO - Plot saved to results/plots\strategy_comparison_evs.png
2025-05-26 21:36:15,656 - utils.visualization - INFO - Plot saved to results/plots\strategy_comparison_mape.png
2025-05-26 21:36:15,656 - __main__ - INFO - All strategies completed. Results saved to results directory.
