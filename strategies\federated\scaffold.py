"""
Implementation of SCAFFOLD algorithm.

Reference:
<PERSON><PERSON><PERSON><PERSON>, <PERSON>. P<PERSON>, <PERSON>, S., <PERSON><PERSON>, M., <PERSON>, S., Stich, S., & Suresh, A. T. (2020).
SCAFFOLD: Stochastic controlled averaging for federated learning.
In International Conference on Machine Learning (pp. 5132-5143).
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time
from strategies.federated.fedavg import EnsembleModel

logger = logging.getLogger(__name__)

class SCAFFOLD:
    """
    SCAFFOLD algorithm for tree-based models.
    
    This implementation adapts SCAFFOLD for tree-based models by using
    control variates to correct for client drift.
    """
    
    def __init__(self, base_model=None, n_clients=3, client_data=None, 
                 n_rounds=10, local_epochs=1, client_sample_ratio=1.0,
                 learning_rate=0.1, random_state=42):
        """
        Initialize SCAFFOLD.
        
        Args:
            base_model: Base model to use for clients (default: LGBMRegressor)
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of communication rounds
            local_epochs: Number of local epochs per round
            client_sample_ratio: Ratio of clients to sample in each round
            learning_rate: Learning rate for control variate updates
            random_state: Random seed
        """
        self.base_model = base_model if base_model is not None else LGBMRegressor(random_state=random_state)
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.client_sample_ratio = client_sample_ratio
        self.learning_rate = learning_rate
        self.random_state = random_state
        
        self.client_models = None
        self.global_model = None
        self.control_variates = None
        self.global_control_variate = None
        self.history = {
            'client_losses': [],
            'global_loss': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_val=None, y_val=None):
        """
        Train the federated model.
        
        Args:
            X_val: Validation features (for tracking progress)
            y_val: Validation targets (for tracking progress)
            
        Returns:
            self: Trained model
        """
        if self.client_data is None or len(self.client_data) == 0:
            raise ValueError("No client data provided")
        
        start_time = time.time()
        logger.info(f"Starting SCAFFOLD training with {self.n_clients} clients and {self.n_rounds} rounds")
        
        # Initialize client models
        self.client_models = [clone(self.base_model) for _ in range(self.n_clients)]
        
        # Initialize control variates (for tree-based models, we use prediction differences)
        self.control_variates = [np.zeros(len(y)) for X, y in self.client_data]
        self.global_control_variate = np.zeros(len(self.client_data[0][1]))
        
        # Training loop
        for round_idx in range(self.n_rounds):
            round_start = time.time()
            logger.info(f"Round {round_idx+1}/{self.n_rounds}")
            
            # Sample clients
            n_sampled = max(1, int(self.client_sample_ratio * self.n_clients))
            sampled_clients = np.random.choice(self.n_clients, n_sampled, replace=False)
            
            # Train client models
            client_losses = []
            delta_controls = []
            
            for client_idx in sampled_clients:
                X_client, y_client = self.client_data[client_idx]
                
                # Get initial predictions
                if round_idx > 0:
                    initial_preds = self.client_models[client_idx].predict(X_client)
                
                # Train with control variate correction
                for _ in range(self.local_epochs):
                    # For tree-based models, we use sample weights to approximate control variate correction
                    if round_idx > 0:
                        # Adjust sample weights based on control variates
                        control_diff = self.control_variates[client_idx] - self.global_control_variate[:len(y_client)]
                        # Higher weight where control variate indicates underprediction
                        sample_weight = 1.0 + self.learning_rate * control_diff
                        # Ensure positive weights
                        sample_weight = np.maximum(0.1, sample_weight)
                        
                        self.client_models[client_idx].fit(
                            X_client, y_client, 
                            sample_weight=sample_weight
                        )
                    else:
                        self.client_models[client_idx].fit(X_client, y_client)
                
                # Update control variates
                if round_idx > 0:
                    final_preds = self.client_models[client_idx].predict(X_client)
                    pred_diff = final_preds - initial_preds
                    
                    # Update client control variate
                    new_control = self.control_variates[client_idx] + pred_diff
                    delta_control = new_control - self.control_variates[client_idx]
                    self.control_variates[client_idx] = new_control
                    delta_controls.append((client_idx, delta_control))
                
                # Evaluate client model
                if X_val is not None and y_val is not None:
                    y_pred = self.client_models[client_idx].predict(X_val)
                    loss = np.mean(np.abs(y_val - y_pred))
                    client_losses.append(loss)
                    logger.debug(f"Client {client_idx} loss: {loss:.4f}")
            
            # Update global model
            self.global_model = self._create_ensemble_model()
            
            # Update global control variate
            if round_idx > 0:
                for client_idx, delta_control in delta_controls:
                    # Scale by number of clients
                    self.global_control_variate[:len(delta_control)] += delta_control / self.n_clients
            
            # Evaluate global model
            if X_val is not None and y_val is not None:
                y_pred = self.global_model.predict(X_val)
                global_loss = np.mean(np.abs(y_val - y_pred))
                self.history['client_losses'].append(client_losses)
                self.history['global_loss'].append(global_loss)
                
                round_time = time.time() - round_start
                logger.info(f"Round {round_idx+1} completed in {round_time:.2f}s. "
                           f"Global loss: {global_loss:.4f}, "
                           f"Avg client loss: {np.mean(client_losses):.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"SCAFFOLD training completed in {total_time:.2f}s")
        
        return self
    
    def _create_ensemble_model(self):
        """
        Create an ensemble model from client models.
        
        Returns:
            EnsembleModel: Ensemble of client models
        """
        return EnsembleModel(self.client_models)
    
    def predict(self, X):
        """
        Make predictions using the global model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        if self.global_model is None:
            raise ValueError("Model has not been trained yet")
        
        return self.global_model.predict(X)
