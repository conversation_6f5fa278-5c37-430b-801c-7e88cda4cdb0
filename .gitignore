__pycache__
__pycache__/

# Ignore Visual Studio IDE files
.vs/
.vscode/
.suo
.vcxproj
.vcproj
.pdb
.user
.userprefs
.dll
.exe

# Ignore JetBrains IntelliJ IDEA files
.idea/
.iml

# Ignore Eclipse IDE files
.classpath
.project
.settings/

# Ignore NetBeans IDE files
nbproject/

# Ignore Sublime Text editor files
*.sublime-project
*.sublime-workspace

# Ignore C/C++ compiled files and build directories
*.o
*.obj
*.out
*.dll
*.so
*.dylib
build/
Debug/
Release/

# Ignore C# compiled files
bin/
obj/

# Ignore Java compiled files
*.class
target/

# Ignore Python bytecode files
*.pyc
__pycache__/

# Ignore Lua compiled files
*.luac

# Ignore Node.js dependencies
node_modules/

# Ignore npm debug log
npm-debug.log*

# Ignore yarn lockfile
yarn.lock

# Ignore Editor-specific files
.DS_Store
Thumbs.db

# Unity generated files
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
#[Ll]ogs/
[Uu]ser[Ss]ettings/
[Mm]emoryCaptures/
[Cc]ache/
/*.sln
/*.csproj
/*.unityproj
/*.sln.DotSettings.user
.AppleDouble
.LSOverride
._*
*.apk
*.unitypackage
/*.apk
/*.aab
/*.app
/*.ipa
.gradle/
/gradlew
/gradle-wrapper.jar
.vscode/

# Ignore Intermediate, Saved, and Build directories generated by UE4
Intermediate/
Saved/
DerivedDataCache/
Build/

# Ignore compiled binaries and executables
Binaries/
#*.log
*.ini
*.uproject.user
*.xcodeproj
*.xcworkspace
*.xcuserstate
DerivedDataCache/
