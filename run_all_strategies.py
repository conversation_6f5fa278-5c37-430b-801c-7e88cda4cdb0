#!/usr/bin/env python3
"""
Complete FLKDDrug Platform Runner
=================================

This script runs ALL strategies from training to evaluation and comparison.
It provides a one-command solution to:
1. Train the baseline original model
2. Train all federated learning strategies
3. Train all knowledge distillation strategies
4. Train combined FL+KD strategies
5. Evaluate and compare all results
6. Generate comprehensive reports and visualizations

Usage:
    python run_all_strategies.py                    # Run all strategies with default settings
    python run_all_strategies.py --gpu 0            # Use GPU acceleration
    python run_all_strategies.py --quick            # Quick test with minimal parameters
    python run_all_strategies.py --comprehensive    # Full comprehensive evaluation
"""

import os
import sys
import argparse
import subprocess
import time
import json
import pandas as pd
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'run_all_strategies_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FLKDDrugRunner:
    """Complete FLKDDrug platform runner."""
    
    def __init__(self, args):
        self.args = args
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f"results/complete_run_{self.timestamp}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Strategy configurations
        self.strategies = {
            'baseline': {
                'original': {'cmd': ['--original'], 'description': 'LightGBM with Bayesian optimization'}
            },
            'federated_learning': {
                'fedavg': {'cmd': ['--fl_strategy', 'fedavg'], 'description': 'Federated Averaging'},
                'fedprox': {'cmd': ['--fl_strategy', 'fedprox'], 'description': 'Federated Proximal'},
                'scaffold': {'cmd': ['--fl_strategy', 'scaffold'], 'description': 'SCAFFOLD with control variates'},
                'personalized_fl': {'cmd': ['--fl_strategy', 'personalized_fl'], 'description': 'Personalized Federated Learning'}
            },
            'knowledge_distillation': {
                'vanilla_kd': {'cmd': ['--kd_strategy', 'vanilla_kd'], 'description': 'Vanilla Knowledge Distillation'},
                'ensemble_kd': {'cmd': ['--kd_strategy', 'ensemble_kd'], 'description': 'Ensemble Knowledge Distillation'},
                'progressive_kd': {'cmd': ['--kd_strategy', 'progressive_kd'], 'description': 'Progressive Knowledge Distillation'},
                'attention_kd': {'cmd': ['--kd_strategy', 'attention_kd'], 'description': 'Attention-based Knowledge Distillation'}
            },
            'combined_strategies': {
                'fedavg_vanilla_kd': {
                    'cmd': ['--fl_strategy', 'fedavg', '--kd_strategy', 'vanilla_kd'],
                    'description': 'FedAvg + Vanilla KD'
                },
                'fedprox_ensemble_kd': {
                    'cmd': ['--fl_strategy', 'fedprox', '--kd_strategy', 'ensemble_kd'],
                    'description': 'FedProx + Ensemble KD'
                },
                'scaffold_attention_kd': {
                    'cmd': ['--fl_strategy', 'scaffold', '--kd_strategy', 'attention_kd'],
                    'description': 'SCAFFOLD + Attention KD'
                }
            }
        }
        
        self.results = {}
        self.start_time = time.time()
    
    def get_base_command(self):
        """Get base command with common parameters."""
        cmd = [sys.executable, 'run_strategy.py']
        
        # Add GPU parameter
        if self.args.gpu >= 0:
            cmd.extend(['--gpu', str(self.args.gpu)])
        
        # Add federated learning parameters
        cmd.extend(['--n_clients', str(self.args.n_clients)])
        cmd.extend(['--n_rounds', str(self.args.n_rounds)])
        cmd.extend(['--local_epochs', str(self.args.local_epochs)])
        
        # Add knowledge distillation parameters
        cmd.extend(['--alpha', str(self.args.alpha)])
        cmd.extend(['--temperature', str(self.args.temperature)])
        
        # Add training parameters
        if hasattr(self.args, 'epochs'):
            cmd.extend(['--epochs', str(self.args.epochs)])
        
        cmd.extend(['--seed', str(self.args.seed)])
        
        return cmd
    
    def run_strategy(self, category, strategy_name, strategy_config):
        """Run a single strategy."""
        logger.info(f"Running {category}/{strategy_name}: {strategy_config['description']}")
        
        # Build command
        cmd = self.get_base_command() + strategy_config['cmd']
        
        start_time = time.time()
        try:
            # Run the strategy
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.args.timeout,
                cwd=os.getcwd()
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                logger.info(f"✓ {strategy_name} completed successfully in {execution_time:.1f}s")
                
                # Try to extract metrics from output
                metrics = self._extract_metrics(result.stdout)
                
                return {
                    'status': 'success',
                    'execution_time': execution_time,
                    'metrics': metrics,
                    'description': strategy_config['description'],
                    'stdout': result.stdout[-1000:],  # Last 1000 chars
                    'stderr': result.stderr
                }
            else:
                logger.error(f"✗ {strategy_name} failed with return code {result.returncode}")
                return {
                    'status': 'failed',
                    'execution_time': execution_time,
                    'description': strategy_config['description'],
                    'return_code': result.returncode,
                    'stdout': result.stdout[-1000:],
                    'stderr': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error(f"✗ {strategy_name} timed out after {self.args.timeout}s")
            return {
                'status': 'timeout',
                'execution_time': self.args.timeout,
                'description': strategy_config['description'],
                'error': f'Timeout after {self.args.timeout}s'
            }
        except Exception as e:
            logger.error(f"✗ {strategy_name} failed with exception: {e}")
            return {
                'status': 'error',
                'execution_time': time.time() - start_time,
                'description': strategy_config['description'],
                'error': str(e)
            }
    
    def _extract_metrics(self, output):
        """Extract performance metrics from output."""
        metrics = {}
        
        # Look for common metric patterns
        lines = output.split('\n')
        for line in lines:
            line = line.strip().lower()
            
            # Extract test metrics
            if 'test_mae:' in line:
                try:
                    metrics['test_mae'] = float(line.split(':')[-1].strip())
                except:
                    pass
            elif 'test_mse:' in line:
                try:
                    metrics['test_mse'] = float(line.split(':')[-1].strip())
                except:
                    pass
            elif 'test_r2:' in line:
                try:
                    metrics['test_r2'] = float(line.split(':')[-1].strip())
                except:
                    pass
        
        return metrics
    
    def run_all_strategies(self):
        """Run all strategies."""
        logger.info("="*80)
        logger.info("STARTING COMPLETE FLKDDRUG PLATFORM EVALUATION")
        logger.info("="*80)
        logger.info(f"Results will be saved to: {self.results_dir}")
        logger.info(f"Configuration: GPU={self.args.gpu}, Clients={self.args.n_clients}, Rounds={self.args.n_rounds}")
        
        total_strategies = sum(len(strategies) for strategies in self.strategies.values())
        current_strategy = 0
        
        for category, strategies in self.strategies.items():
            logger.info(f"\n{'='*60}")
            logger.info(f"CATEGORY: {category.upper().replace('_', ' ')}")
            logger.info(f"{'='*60}")
            
            category_results = {}
            
            for strategy_name, strategy_config in strategies.items():
                current_strategy += 1
                logger.info(f"\n[{current_strategy}/{total_strategies}] {strategy_name}")
                
                result = self.run_strategy(category, strategy_name, strategy_config)
                category_results[strategy_name] = result
                
                # Brief pause between strategies
                time.sleep(2)
            
            self.results[category] = category_results
        
        # Generate comprehensive report
        self._generate_comprehensive_report()
        
        return self.results
    
    def _generate_comprehensive_report(self):
        """Generate comprehensive evaluation report."""
        logger.info("\n" + "="*80)
        logger.info("GENERATING COMPREHENSIVE EVALUATION REPORT")
        logger.info("="*80)
        
        # Calculate statistics
        total_strategies = 0
        successful_strategies = 0
        failed_strategies = 0
        timeout_strategies = 0
        
        summary_data = []
        performance_data = []
        
        for category, category_results in self.results.items():
            for strategy_name, result in category_results.items():
                total_strategies += 1
                status = result['status']
                exec_time = result.get('execution_time', 0)
                
                if status == 'success':
                    successful_strategies += 1
                    status_symbol = "✓"
                elif status == 'timeout':
                    timeout_strategies += 1
                    status_symbol = "⏱"
                else:
                    failed_strategies += 1
                    status_symbol = "✗"
                
                logger.info(f"  {status_symbol} {strategy_name:<25} - {status:<8} ({exec_time:.1f}s)")
                
                # Collect summary data
                summary_data.append({
                    'category': category,
                    'strategy': strategy_name,
                    'description': result.get('description', ''),
                    'status': status,
                    'execution_time': exec_time,
                    'test_mae': result.get('metrics', {}).get('test_mae', ''),
                    'test_mse': result.get('metrics', {}).get('test_mse', ''),
                    'test_r2': result.get('metrics', {}).get('test_r2', ''),
                })
                
                # Collect performance data for successful strategies
                if status == 'success' and result.get('metrics'):
                    performance_data.append({
                        'strategy': strategy_name,
                        'category': category,
                        **result['metrics']
                    })
        
        # Overall statistics
        total_time = time.time() - self.start_time
        logger.info(f"\nOVERALL STATISTICS:")
        logger.info(f"Total Strategies: {total_strategies}")
        logger.info(f"Successful: {successful_strategies}")
        logger.info(f"Failed: {failed_strategies}")
        logger.info(f"Timeout: {timeout_strategies}")
        logger.info(f"Success Rate: {successful_strategies/total_strategies*100:.1f}%")
        logger.info(f"Total Time: {total_time/60:.1f} minutes")
        
        # Save detailed results
        results_file = f"{self.results_dir}/complete_evaluation_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        logger.info(f"Detailed results saved to {results_file}")
        
        # Save summary CSV
        summary_file = f"{self.results_dir}/strategy_comparison_summary.csv"
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_csv(summary_file, index=False)
        logger.info(f"Summary report saved to {summary_file}")
        
        # Save performance comparison
        if performance_data:
            performance_file = f"{self.results_dir}/performance_comparison.csv"
            df_performance = pd.DataFrame(performance_data)
            df_performance.to_csv(performance_file, index=False)
            logger.info(f"Performance comparison saved to {performance_file}")
            
            # Show top performers
            if 'test_mae' in df_performance.columns:
                logger.info(f"\nTOP PERFORMERS (by Test MAE):")
                top_performers = df_performance.nsmallest(5, 'test_mae')
                for _, row in top_performers.iterrows():
                    logger.info(f"  {row['strategy']:<25} - MAE: {row['test_mae']:.4f}")
        
        # Final verdict
        if successful_strategies == total_strategies:
            logger.info("\n🎉 ALL STRATEGIES COMPLETED SUCCESSFULLY!")
            logger.info("The FLKDDrug platform is fully functional and ready for research.")
        elif successful_strategies > total_strategies * 0.8:
            logger.info("\n✅ MOST STRATEGIES COMPLETED SUCCESSFULLY!")
            logger.info("The platform is largely functional with minor issues.")
        else:
            logger.warning("\n⚠️ MANY STRATEGIES FAILED!")
            logger.warning("Please check individual logs for details.")
        
        logger.info(f"\nAll results saved to: {self.results_dir}")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Run complete FLKDDrug platform evaluation',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_all_strategies.py                    # Run all strategies with default settings
  python run_all_strategies.py --gpu 0            # Use GPU acceleration
  python run_all_strategies.py --quick            # Quick test with minimal parameters
  python run_all_strategies.py --comprehensive    # Full comprehensive evaluation
        """
    )
    
    # Mode selection
    parser.add_argument('--quick', action='store_true',
                       help='Quick test mode (minimal parameters)')
    parser.add_argument('--comprehensive', action='store_true',
                       help='Comprehensive evaluation mode (full parameters)')
    
    # Hardware settings
    parser.add_argument('--gpu', type=int, default=-1,
                       help='GPU ID to use (-1 for CPU)')
    
    # Federated learning parameters
    parser.add_argument('--n_clients', type=int, default=3,
                       help='Number of federated learning clients')
    parser.add_argument('--n_rounds', type=int, default=5,
                       help='Number of federated learning rounds')
    parser.add_argument('--local_epochs', type=int, default=1,
                       help='Number of local epochs per round')
    
    # Knowledge distillation parameters
    parser.add_argument('--alpha', type=float, default=0.5,
                       help='Knowledge distillation alpha parameter')
    parser.add_argument('--temperature', type=float, default=3.0,
                       help='Knowledge distillation temperature parameter')
    
    # Training parameters
    parser.add_argument('--epochs', type=int, default=10,
                       help='Number of training epochs')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')
    
    # Execution parameters
    parser.add_argument('--timeout', type=int, default=1800,
                       help='Timeout per strategy in seconds (default: 30 minutes)')
    
    args = parser.parse_args()
    
    # Adjust parameters based on mode
    if args.quick:
        args.n_rounds = 2
        args.n_clients = 2
        args.epochs = 3
        args.timeout = 300  # 5 minutes
        logger.info("Quick mode: Using minimal parameters for fast testing")
    elif args.comprehensive:
        args.n_rounds = 10
        args.n_clients = 5
        args.epochs = 20
        args.timeout = 3600  # 1 hour
        logger.info("Comprehensive mode: Using full parameters for complete evaluation")
    
    return args

def main():
    """Main function."""
    args = parse_args()
    
    logger.info("FLKDDrug Complete Platform Runner")
    logger.info("=" * 50)
    logger.info(f"Mode: {'Quick' if args.quick else 'Comprehensive' if args.comprehensive else 'Standard'}")
    logger.info(f"GPU: {args.gpu if args.gpu >= 0 else 'CPU'}")
    logger.info(f"Clients: {args.n_clients}, Rounds: {args.n_rounds}, Epochs: {args.epochs}")
    
    # Create runner and execute
    runner = FLKDDrugRunner(args)
    results = runner.run_all_strategies()
    
    logger.info("\n" + "="*80)
    logger.info("COMPLETE EVALUATION FINISHED")
    logger.info("="*80)
    logger.info(f"Results directory: {runner.results_dir}")
    logger.info("Check the generated CSV and JSON files for detailed analysis.")

if __name__ == "__main__":
    main()
