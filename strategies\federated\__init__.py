"""
Federated Learning Strategies for Drug Solubility Prediction.

This module implements various federated learning algorithms including:
- FedAvg: Federated Averaging
- FedProx: Federated Proximal
- SCAFFOLD: Stochastic Controlled Averaging for Federated Learning
- PersonalizedFL: Personalized Federated Learning
- FedOpt: Federated Learning with Adaptive Server Optimization
"""

from .fedavg import FedAvg
from .fedprox import FedProx
from .scaffold import SCAFFOLD
from .personalized_fl import PersonalizedFL
from .fedopt import FedOpt

__all__ = ['FedAvg', 'FedProx', 'SCAFFOLD', 'PersonalizedFL', 'FedOpt']
