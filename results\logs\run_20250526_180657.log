2025-05-26 18:07:08,900 - utils.gpu_utils - INFO - Using GPU: NVIDIA GeForce GTX 1050
2025-05-26 18:07:08,901 - utils.gpu_utils - INFO - GPU Memory: 4.3 GB
2025-05-26 18:07:08,901 - utils.gpu_utils - INFO - Applying Windows-specific GPU optimizations
2025-05-26 18:07:08,901 - utils.gpu_utils - INFO - Enabled Flash SDP for Windows
2025-05-26 18:07:08,902 - utils.gpu_utils - INFO - Mixed precision training enabled
2025-05-26 18:07:08,902 - utils.gpu_utils - INFO - CUDNN benchmark mode enabled
2025-05-26 18:07:08,902 - utils.gpu_utils - INFO - TF32 enabled for Ampere GPUs
2025-05-26 18:07:08,903 - __main__ - INFO - Using GPU 0: cuda:0
2025-05-26 18:07:08,905 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.00GB, Free: 4.29GB, Utilization: 0.0%
2025-05-26 18:07:08,907 - __main__ - INFO - Loading data...
2025-05-26 18:07:09,915 - utils.data_utils - INFO - Loaded primary data from logS_des.csv: 14594 samples, 199 features
2025-05-26 18:07:09,963 - __main__ - INFO - Splitting data...
2025-05-26 18:07:10,050 - utils.data_utils - INFO - Data split: train=9340, val=2335, test=2919
2025-05-26 18:07:10,052 - __main__ - INFO - Preprocessing data...
2025-05-26 18:07:10,123 - utils.data_utils - INFO - Found 62 NaN values in training data
2025-05-26 18:07:10,311 - utils.data_utils - INFO - NaN values filled with median values
2025-05-26 18:07:10,468 - utils.data_utils - INFO - Data scaled using RobustScaler
2025-05-26 18:07:10,552 - __main__ - INFO - Creating client data for 5 clients...
2025-05-26 18:07:10,601 - utils.data_utils - INFO - Client 1: 1868 samples, mean(y)=-2.7965, std(y)=2.1408
2025-05-26 18:07:10,602 - utils.data_utils - INFO - Client 2: 1868 samples, mean(y)=-2.8348, std(y)=2.1821
2025-05-26 18:07:10,602 - utils.data_utils - INFO - Client 3: 1868 samples, mean(y)=-2.8803, std(y)=2.2216
2025-05-26 18:07:10,602 - utils.data_utils - INFO - Client 4: 1868 samples, mean(y)=-2.8153, std(y)=2.2115
2025-05-26 18:07:10,603 - utils.data_utils - INFO - Client 5: 1868 samples, mean(y)=-3.0071, std(y)=2.2530
2025-05-26 18:07:13,773 - utils.visualization - INFO - Plot saved to results/plots\client_data_distribution.png
2025-05-26 18:07:13,774 - __main__ - INFO - Running original LightGBM model with Bayesian optimization (replicating original-model_save.py)...
2025-05-26 18:11:28,169 - __main__ - INFO - Best score: -0.617
2025-05-26 18:11:28,169 - __main__ - INFO - Best parameters: {'colsample_bynode': 0.8784256512134201, 'colsample_bytree': 0.4838386136220194, 'learning_rate': 0.09280451878488163, 'n_estimators': 863.3898489174285, 'num_leaves': 24.52758909380178, 'reg_alpha': 0.7830388126033466, 'reg_lambda': 3.9009134636461953, 'subsample': 0.9985281233805139, 'subsample_freq': 2.534749002674246}
2025-05-26 18:11:46,993 - __main__ - INFO - Original LightGBM model training completed successfully
2025-05-26 18:11:47,151 - utils.evaluation - INFO - Model evaluation - train_MAE: 0.2478, train_MSE: 0.1184, train_RMSE: 0.3441, train_R2: 0.9756, train_EVS: 0.9756, train_MAPE: 103922099299.0739
2025-05-26 18:11:47,206 - utils.evaluation - INFO - Model evaluation - val_MAE: 0.2473, val_MSE: 0.1256, val_RMSE: 0.3544, val_R2: 0.9746, val_EVS: 0.9746, val_MAPE: 276454279988.0191
2025-05-26 18:11:47,264 - utils.evaluation - INFO - Model evaluation - test_MAE: 0.5907, test_MSE: 0.7856, test_RMSE: 0.8863, test_R2: 0.8304, test_EVS: 0.8304, test_MAPE: 0.9618
2025-05-26 18:11:47,266 - utils.evaluation - INFO - Results logged to results/logs\original_20250526_181147.json
2025-05-26 18:11:47,361 - utils.model_utils - INFO - Model saved to results/models\original_model.pkl
2025-05-26 18:11:48,221 - utils.visualization - INFO - Plot saved to results/plots\original_actual_vs_pred.png
2025-05-26 18:11:48,888 - utils.visualization - INFO - Plot saved to results/plots\original_residuals.png
2025-05-26 18:11:49,576 - utils.visualization - INFO - Plot saved to results/plots\original_feature_importance.png
2025-05-26 18:11:49,579 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 18:11:49,579 - strategies.federated.fedavg - INFO - Starting FedAvg training with 5 clients and 15 rounds
2025-05-26 18:11:49,582 - strategies.federated.fedavg - INFO - Round 1/15
2025-05-26 18:12:19,930 - strategies.federated.fedavg - INFO - Round 1 completed in 30.35s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:12:19,930 - strategies.federated.fedavg - INFO - Round 2/15
2025-05-26 18:12:49,454 - strategies.federated.fedavg - INFO - Round 2 completed in 29.52s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 18:12:49,454 - strategies.federated.fedavg - INFO - Round 3/15
2025-05-26 18:13:18,714 - strategies.federated.fedavg - INFO - Round 3 completed in 29.26s. Global loss: 0.6686, Avg client loss: 0.7357
2025-05-26 18:13:18,714 - strategies.federated.fedavg - INFO - Round 4/15
2025-05-26 18:13:48,129 - strategies.federated.fedavg - INFO - Round 4 completed in 29.41s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:13:48,129 - strategies.federated.fedavg - INFO - Round 5/15
2025-05-26 18:14:17,799 - strategies.federated.fedavg - INFO - Round 5 completed in 29.67s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:14:17,799 - strategies.federated.fedavg - INFO - Round 6/15
2025-05-26 18:14:48,639 - strategies.federated.fedavg - INFO - Round 6 completed in 30.84s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:14:48,639 - strategies.federated.fedavg - INFO - Round 7/15
2025-05-26 18:15:19,125 - strategies.federated.fedavg - INFO - Round 7 completed in 30.49s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 18:15:19,126 - strategies.federated.fedavg - INFO - Round 8/15
2025-05-26 18:15:52,915 - strategies.federated.fedavg - INFO - Round 8 completed in 33.79s. Global loss: 0.6687, Avg client loss: 0.7357
2025-05-26 18:15:52,915 - strategies.federated.fedavg - INFO - Round 9/15
2025-05-26 18:16:24,342 - strategies.federated.fedavg - INFO - Round 9 completed in 31.43s. Global loss: 0.6687, Avg client loss: 0.7358
2025-05-26 18:16:24,343 - strategies.federated.fedavg - INFO - Round 10/15
2025-05-26 18:16:54,988 - strategies.federated.fedavg - INFO - Round 10 completed in 30.64s. Global loss: 0.6688, Avg client loss: 0.7358
2025-05-26 18:16:54,989 - strategies.federated.fedavg - INFO - Round 11/15
