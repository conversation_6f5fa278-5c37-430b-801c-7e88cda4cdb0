"""
TRPO (Trust Region Policy Optimization) for federated learning optimization.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.model_selection import cross_val_score
import time

logger = logging.getLogger(__name__)


class TRPOPolicyNetwork(nn.Module):
    """Policy network for TRPO agent."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 128]):
        super(TRPOPolicyNetwork, self).__init__()
        
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*layers)
        
        # Policy head (mean and log_std for continuous actions)
        self.policy_mean = nn.Linear(prev_dim, action_dim)
        self.policy_log_std = nn.Parameter(torch.zeros(action_dim))
        
        # Value head
        self.value_head = nn.Linear(prev_dim, 1)
        
    def forward(self, state):
        """Forward pass through the network."""
        shared = self.shared_layers(state)
        
        # Policy
        mean = torch.tanh(self.policy_mean(shared))  # Normalize to [-1, 1]
        std = torch.exp(self.policy_log_std.clamp(-20, 2))
        
        # Value
        value = self.value_head(shared)
        
        return mean, std, value
    
    def get_action_and_value(self, state):
        """Sample action and get value."""
        mean, std, value = self.forward(state)
        dist = Normal(mean, std)
        action = dist.sample()
        log_prob = dist.log_prob(action).sum(dim=-1)
        
        return action, log_prob, value, dist
    
    def evaluate_action(self, state, action):
        """Evaluate action under current policy."""
        mean, std, value = self.forward(state)
        dist = Normal(mean, std)
        log_prob = dist.log_prob(action).sum(dim=-1)
        entropy = dist.entropy().sum(dim=-1)
        
        return log_prob, entropy, value, dist


class TRPOOptimizer:
    """TRPO-based optimizer for federated learning."""
    
    def __init__(self, state_dim: int, action_dim: int, 
                 lr: float = 3e-4, gamma: float = 0.99, 
                 max_kl: float = 0.01, damping: float = 0.1,
                 device: torch.device = None):
        """
        Initialize TRPO optimizer.
        
        Args:
            state_dim: Dimension of state space
            action_dim: Dimension of action space
            lr: Learning rate for value function
            gamma: Discount factor
            max_kl: Maximum KL divergence for trust region
            damping: Damping factor for conjugate gradient
            device: Device to use
        """
        self.device = device if device is not None else torch.device('cpu')
        self.gamma = gamma
        self.max_kl = max_kl
        self.damping = damping
        
        # Networks
        self.policy = TRPOPolicyNetwork(state_dim, action_dim).to(self.device)
        self.value_optimizer = optim.Adam([p for p in self.policy.parameters() 
                                         if p in self.policy.value_head.parameters()], lr=lr)
        
        # Storage
        self.states = []
        self.actions = []
        self.rewards = []
        self.log_probs = []
        self.values = []
        self.dones = []
        
    def store_transition(self, state, action, reward, log_prob, value, done):
        """Store transition in memory."""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.dones.append(done)
    
    def compute_returns_and_advantages(self):
        """Compute returns and advantages using GAE."""
        returns = []
        advantages = []
        gae = 0
        
        values = torch.stack(self.values).squeeze()
        rewards = torch.tensor(self.rewards, dtype=torch.float32, device=self.device)
        dones = torch.tensor(self.dones, dtype=torch.bool, device=self.device)
        
        # Add final value for bootstrap
        next_value = 0
        
        for step in reversed(range(len(rewards))):
            if step == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[step]
                next_value = next_value
            else:
                next_non_terminal = 1.0 - dones[step]
                next_value = values[step + 1]
            
            delta = rewards[step] + self.gamma * next_value * next_non_terminal - values[step]
            gae = delta + self.gamma * 0.95 * next_non_terminal * gae  # GAE-lambda with lambda=0.95
            
            advantages.insert(0, gae)
            returns.insert(0, gae + values[step])
        
        advantages = torch.tensor(advantages, dtype=torch.float32, device=self.device)
        returns = torch.tensor(returns, dtype=torch.float32, device=self.device)
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return returns, advantages
    
    def conjugate_gradient(self, Avp_func, b, nsteps=10):
        """Conjugate gradient algorithm for solving Ax = b."""
        x = torch.zeros_like(b)
        r = b.clone()
        p = b.clone()
        rdotr = torch.dot(r, r)
        
        for _ in range(nsteps):
            Avp = Avp_func(p)
            alpha = rdotr / torch.dot(p, Avp)
            x += alpha * p
            r -= alpha * Avp
            new_rdotr = torch.dot(r, r)
            beta = new_rdotr / rdotr
            p = r + beta * p
            rdotr = new_rdotr
            
        return x
    
    def compute_kl_divergence(self, old_dist, new_dist):
        """Compute KL divergence between two distributions."""
        return torch.distributions.kl_divergence(old_dist, new_dist).mean()
    
    def update(self):
        """Update policy using TRPO."""
        if len(self.states) == 0:
            return
        
        # Convert to tensors
        states = torch.stack(self.states).to(self.device)
        actions = torch.stack(self.actions).to(self.device)
        old_log_probs = torch.stack(self.log_probs).to(self.device)
        
        # Compute returns and advantages
        returns, advantages = self.compute_returns_and_advantages()
        
        # Get old policy distribution
        with torch.no_grad():
            _, _, _, old_dist = self.policy.get_action_and_value(states)
        
        # Update value function
        for _ in range(5):  # Multiple value function updates
            _, _, values, _ = self.policy.get_action_and_value(states)
            values = values.squeeze()
            value_loss = F.mse_loss(values, returns)
            
            self.value_optimizer.zero_grad()
            value_loss.backward()
            self.value_optimizer.step()
        
        # Compute policy gradient
        log_probs, _, _, new_dist = self.policy.evaluate_action(states, actions)
        ratio = torch.exp(log_probs - old_log_probs)
        policy_loss = -(ratio * advantages).mean()
        
        # Compute gradient
        policy_params = [p for p in self.policy.parameters() 
                        if p not in self.policy.value_head.parameters()]
        grads = torch.autograd.grad(policy_loss, policy_params, create_graph=True)
        flat_grad = torch.cat([grad.view(-1) for grad in grads])
        
        # Define Fisher-vector product function
        def Fvp(v):
            kl = self.compute_kl_divergence(old_dist, new_dist)
            grads = torch.autograd.grad(kl, policy_params, create_graph=True)
            flat_grad_kl = torch.cat([grad.view(-1) for grad in grads])
            
            kl_v = (flat_grad_kl * v).sum()
            grads = torch.autograd.grad(kl_v, policy_params)
            flat_grad_grad_kl = torch.cat([grad.contiguous().view(-1) for grad in grads])
            
            return flat_grad_grad_kl + self.damping * v
        
        # Solve for search direction using conjugate gradient
        search_dir = self.conjugate_gradient(Fvp, -flat_grad)
        
        # Compute step size
        shs = 0.5 * torch.dot(search_dir, Fvp(search_dir))
        lm = torch.sqrt(shs / self.max_kl)
        fullstep = search_dir / lm
        
        # Line search
        def apply_update(step_size):
            n = 0
            for p in policy_params:
                numel = p.numel()
                p.data += step_size * fullstep[n:n+numel].view(p.shape)
                n += numel
        
        # Backtracking line search
        for step_frac in [1.0, 0.5, 0.25, 0.125]:
            old_params = [p.clone() for p in policy_params]
            apply_update(step_frac)
            
            # Check KL constraint
            with torch.no_grad():
                _, _, _, new_dist_check = self.policy.get_action_and_value(states)
                kl = self.compute_kl_divergence(old_dist, new_dist_check)
                
                if kl <= self.max_kl:
                    break
                else:
                    # Restore old parameters
                    for p, old_p in zip(policy_params, old_params):
                        p.data.copy_(old_p.data)
        
        # Clear memory
        self.clear_memory()
    
    def clear_memory(self):
        """Clear stored transitions."""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.log_probs.clear()
        self.values.clear()
        self.dones.clear()
    
    def get_action(self, state):
        """Get action from policy."""
        state = torch.tensor(state, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            action, log_prob, value, _ = self.policy.get_action_and_value(state)
        
        return action.squeeze().cpu().numpy(), log_prob.cpu().numpy(), value.cpu().numpy()


class TRPOFederatedOptimizer(BaseEstimator, RegressorMixin):
    """TRPO-based federated learning optimizer."""
    
    def __init__(self, base_model_class, n_clients: int = 5, 
                 n_episodes: int = 50, max_steps: int = 20,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize TRPO federated optimizer.
        
        Args:
            base_model_class: Base model class to optimize
            n_clients: Number of federated clients
            n_episodes: Number of optimization episodes
            max_steps: Maximum steps per episode
            device: Device to use
            random_state: Random seed
        """
        self.base_model_class = base_model_class
        self.n_clients = n_clients
        self.n_episodes = n_episodes
        self.max_steps = max_steps
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        # TRPO parameters
        self.state_dim = 10  # Problem features
        self.action_dim = n_clients  # Client selection actions
        
        # Initialize TRPO
        self.trpo = TRPOOptimizer(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            device=self.device
        )
        
        self.best_client_weights = None
        self.best_score = -np.inf
        self.history = []
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
    
    def _get_state(self, client_performances: List[float]):
        """Extract state features from client performances."""
        if len(client_performances) < self.n_clients:
            client_performances.extend([0.0] * (self.n_clients - len(client_performances)))
        
        state = np.array([
            np.mean(client_performances),
            np.std(client_performances),
            np.max(client_performances),
            np.min(client_performances),
            np.median(client_performances),
            len([p for p in client_performances if p > 0]) / self.n_clients,
            np.percentile(client_performances, 25),
            np.percentile(client_performances, 75),
            np.sum(client_performances),
            np.var(client_performances)
        ])
        
        # Normalize state
        state = (state - np.mean(state)) / (np.std(state) + 1e-8)
        return state
    
    def optimize_client_selection(self, client_data: List, X_val, y_val):
        """Optimize client selection using TRPO."""
        logger.info(f"Starting TRPO client selection optimization for {self.n_episodes} episodes")
        
        for episode in range(self.n_episodes):
            episode_reward = 0
            client_performances = [0.0] * self.n_clients
            
            for step in range(self.max_steps):
                # Get current state
                state = self._get_state(client_performances)
                
                # Get action (client weights) from policy
                action, log_prob, value = self.trpo.get_action(state)
                
                # Convert action to client weights (softmax normalization)
                client_weights = torch.softmax(torch.tensor(action), dim=0).numpy()
                
                # Simulate federated learning round with weighted clients
                try:
                    # Create weighted ensemble of client models
                    ensemble_pred = np.zeros(len(y_val))
                    total_weight = 0
                    
                    for i, (X_client, y_client) in enumerate(client_data[:self.n_clients]):
                        if len(X_client) > 0:
                            # Train client model
                            model = self.base_model_class(device=self.device, epochs=10)
                            model.fit(X_client, y_client)
                            
                            # Get predictions
                            pred = model.predict(X_val)
                            ensemble_pred += client_weights[i] * pred
                            total_weight += client_weights[i]
                            
                            # Update client performance
                            client_performances[i] = -np.mean(np.abs(y_val.values - pred))
                    
                    if total_weight > 0:
                        ensemble_pred /= total_weight
                        reward = -np.mean(np.abs(y_val.values - ensemble_pred))
                    else:
                        reward = -np.inf
                        
                except Exception as e:
                    logger.warning(f"Error in client selection: {e}")
                    reward = -np.inf
                
                # Update best weights
                if reward > self.best_score:
                    self.best_score = reward
                    self.best_client_weights = client_weights.copy()
                
                # Store transition
                done = (step == self.max_steps - 1)
                self.trpo.store_transition(
                    torch.tensor(state, dtype=torch.float32),
                    torch.tensor(action, dtype=torch.float32),
                    reward,
                    torch.tensor(log_prob, dtype=torch.float32),
                    torch.tensor(value, dtype=torch.float32),
                    done
                )
                
                episode_reward += reward
            
            # Update policy
            self.trpo.update()
            
            # Log progress
            self.history.append(episode_reward)
            if (episode + 1) % 10 == 0:
                logger.info(f"Episode {episode + 1}/{self.n_episodes}, "
                           f"Episode Reward: {episode_reward:.4f}, "
                           f"Best Score: {self.best_score:.4f}")
        
        logger.info(f"TRPO optimization completed. Best score: {self.best_score:.4f}")
        return self.best_client_weights, self.best_score
    
    def fit(self, X, y, client_data=None):
        """Fit the TRPO-optimized federated model."""
        if client_data is None:
            # Create dummy client data for testing
            n_samples = len(X) // self.n_clients
            client_data = []
            for i in range(self.n_clients):
                start_idx = i * n_samples
                end_idx = (i + 1) * n_samples if i < self.n_clients - 1 else len(X)
                client_data.append((X.iloc[start_idx:end_idx], y.iloc[start_idx:end_idx]))
        
        # Split data for validation
        split_idx = int(0.8 * len(X))
        X_val = X.iloc[split_idx:]
        y_val = y.iloc[split_idx:]
        
        # Optimize client selection
        self.best_client_weights, _ = self.optimize_client_selection(client_data, X_val, y_val)
        
        return self
    
    def predict(self, X):
        """Make predictions using optimized client weights."""
        # This is a simplified prediction - in practice, you'd use the actual federated model
        return np.zeros(len(X))  # Placeholder
    
    @property
    def feature_importances_(self):
        """Get feature importances."""
        return np.ones(10) / 10  # Placeholder
