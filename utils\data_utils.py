"""
Data loading and preprocessing utilities for the FLKDDrug project with GPU acceleration.
"""

import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import logging
import torch
from typing import Optional, Tuple, Union

logger = logging.getLogger(__name__)

def load_data(primary_file="logS_des.csv", secondary_file=None, combine=True, target_col='value'):
    """
    Load data from CSV files.

    Args:
        primary_file (str): Path to the primary data file
        secondary_file (str): Path to the secondary data file (optional)
        combine (bool): Whether to combine the datasets if secondary_file is provided
        target_col (str): Name of the target column (default: 'value')

    Returns:
        tuple: (X, y) or (X_primary, y_primary, X_secondary, y_secondary)
    """
    try:
        # Try to load primary data
        try:
            data_primary = pd.read_csv(primary_file)
        except Exception as e:
            logger.error(f"Error reading {primary_file}: {e}")
            # Try to find the file in the current directory or data directory
            for path in ['.', 'data']:
                try:
                    file_path = os.path.join(path, os.path.basename(primary_file))
                    if os.path.exists(file_path):
                        logger.info(f"Found {primary_file} at {file_path}")
                        data_primary = pd.read_csv(file_path)
                        break
                except:
                    continue
            else:
                raise FileNotFoundError(f"Could not find {primary_file}")

        # Check for target column
        if target_col not in data_primary.columns:
            # Try to find alternative target column
            possible_targets = ['value', 'logS', 'target', 'y']
            for col in possible_targets:
                if col in data_primary.columns:
                    logger.warning(f"Target column '{target_col}' not found, using '{col}' instead")
                    target_col = col
                    break
            else:
                raise ValueError(f"Target column '{target_col}' not found in {primary_file}")

        # Clean data - replace any string 'inf' or '-inf' with np.inf or -np.inf
        for col in data_primary.columns:
            if data_primary[col].dtype == 'object':
                data_primary[col] = data_primary[col].replace(['inf', '-inf', 'Inf', '-Inf'],
                                                             [np.inf, -np.inf, np.inf, -np.inf])

        # Extract features and target
        X_primary = data_primary.drop(columns=[target_col])
        y_primary = data_primary[target_col]

        # Convert to numeric if possible
        for col in X_primary.columns:
            try:
                X_primary[col] = pd.to_numeric(X_primary[col])
            except:
                pass

        logger.info(f"Loaded primary data from {primary_file}: {X_primary.shape[0]} samples, {X_primary.shape[1]} features")

        if secondary_file:
            try:
                # Try to load secondary data
                try:
                    data_secondary = pd.read_csv(secondary_file)
                except Exception as e:
                    logger.error(f"Error reading {secondary_file}: {e}")
                    # Try to find the file in the current directory or data directory
                    for path in ['.', 'data']:
                        try:
                            file_path = os.path.join(path, os.path.basename(secondary_file))
                            if os.path.exists(file_path):
                                logger.info(f"Found {secondary_file} at {file_path}")
                                data_secondary = pd.read_csv(file_path)
                                break
                        except:
                            continue
                    else:
                        raise FileNotFoundError(f"Could not find {secondary_file}")

                if target_col in data_secondary.columns:
                    # Clean data
                    for col in data_secondary.columns:
                        if data_secondary[col].dtype == 'object':
                            data_secondary[col] = data_secondary[col].replace(['inf', '-inf', 'Inf', '-Inf'],
                                                                           [np.inf, -np.inf, np.inf, -np.inf])

                    X_secondary = data_secondary.drop(columns=[target_col])
                    y_secondary = data_secondary[target_col]

                    # Convert to numeric if possible
                    for col in X_secondary.columns:
                        try:
                            X_secondary[col] = pd.to_numeric(X_secondary[col])
                        except:
                            pass

                    logger.info(f"Loaded secondary data from {secondary_file}: {X_secondary.shape[0]} samples")

                    if combine:
                        # Ensure secondary data has the same columns as primary
                        common_cols = set(X_primary.columns).intersection(set(X_secondary.columns))
                        if len(common_cols) < len(X_primary.columns):
                            logger.warning(f"Secondary data has fewer features ({len(common_cols)}) than primary data ({len(X_primary.columns)})")
                            X_primary = X_primary[list(common_cols)]
                            X_secondary = X_secondary[list(common_cols)]

                        X = pd.concat([X_primary, X_secondary], axis=0, ignore_index=True)
                        y = pd.concat([y_primary, y_secondary], axis=0, ignore_index=True)
                        logger.info(f"Combined data: {X.shape[0]} samples, {X.shape[1]} features")
                        return X, y
                    else:
                        return X_primary, y_primary, X_secondary, y_secondary
                else:
                    logger.warning(f"Column '{target_col}' not found in {secondary_file}, using only primary data")
                    return X_primary, y_primary
            except FileNotFoundError:
                logger.warning(f"Secondary file {secondary_file} not found, using only primary data")
                return X_primary, y_primary
            except Exception as e:
                logger.error(f"Error loading secondary data: {e}")
                return X_primary, y_primary
        else:
            return X_primary, y_primary

    except FileNotFoundError:
        logger.error(f"Primary file {primary_file} not found")
        raise
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        raise

def split_data(X, y, test_size=0.2, val_size=0.2, random_state=42):
    """
    Split data into train, validation, and test sets.

    Args:
        X (DataFrame): Features
        y (Series): Target
        test_size (float): Proportion of data to use for testing
        val_size (float): Proportion of training data to use for validation
        random_state (int): Random seed

    Returns:
        tuple: (X_train, X_val, X_test, y_train, y_val, y_test)
    """
    # First split into train+val and test
    X_remain, X_test, y_remain, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state
    )

    # Then split train+val into train and val
    X_train, X_val, y_train, y_val = train_test_split(
        X_remain, y_remain, test_size=val_size, random_state=random_state
    )

    logger.info(f"Data split: train={X_train.shape[0]}, val={X_val.shape[0]}, test={X_test.shape[0]}")

    return X_train, X_val, X_test, y_train, y_val, y_test

def create_client_data(X, y, n_clients=3, method='split', alpha=0.5, random_state=42):
    """
    Create data for federated learning clients.

    Args:
        X (DataFrame): Features
        y (Series): Target
        n_clients (int): Number of clients
        method (str): Method to split data ('split', 'dirichlet', 'shard')
        alpha (float): Concentration parameter for Dirichlet distribution
        random_state (int): Random seed

    Returns:
        list: List of (X_client, y_client) tuples
    """
    np.random.seed(random_state)
    client_data = []

    if method == 'split':
        # Simple split into equal parts
        combined = pd.concat([X, y], axis=1)
        splits = np.array_split(combined, n_clients)

        for split in splits:
            # Get the target column name (last column)
            target_col = split.columns[-1]
            X_client = split.drop(columns=[target_col])
            y_client = split[target_col]
            client_data.append((X_client, y_client))

    elif method == 'dirichlet':
        # Non-IID split using Dirichlet distribution
        n_samples = X.shape[0]
        labels = pd.Categorical(pd.qcut(y, 5)).codes  # Convert continuous to categorical

        # Generate Dirichlet distribution
        proportions = np.random.dirichlet(np.repeat(alpha, n_clients), size=5)

        # Initialize client data
        client_idxs = [[] for _ in range(n_clients)]

        # For each class, distribute samples according to proportions
        for c in range(5):
            idx_c = np.where(labels == c)[0]
            np.random.shuffle(idx_c)

            # Calculate number of samples per client for this class
            proportions_c = proportions[c]
            proportions_c = proportions_c / proportions_c.sum()  # Normalize
            samples_per_client = (np.cumsum(proportions_c) * len(idx_c)).astype(int)
            samples_per_client = np.append(0, samples_per_client)

            # Assign samples to clients
            for i in range(n_clients):
                client_idxs[i].extend(idx_c[samples_per_client[i]:samples_per_client[i+1]])

        # Create client datasets
        for idxs in client_idxs:
            X_client = X.iloc[idxs].reset_index(drop=True)
            y_client = y.iloc[idxs].reset_index(drop=True)
            client_data.append((X_client, y_client))

    elif method == 'shard':
        # Shard-based non-IID split
        n_samples = X.shape[0]
        n_shards = n_clients * 2  # Each client gets 2 shards

        # Sort data by target value
        sorted_idx = np.argsort(y)
        shard_size = n_samples // n_shards

        # Create shards
        shards = [sorted_idx[i*shard_size:(i+1)*shard_size] for i in range(n_shards)]
        np.random.shuffle(shards)

        # Assign shards to clients
        client_shards = [[] for _ in range(n_clients)]
        for i, shard in enumerate(shards):
            client_shards[i % n_clients].extend(shard)

        # Create client datasets
        for idxs in client_shards:
            X_client = X.iloc[idxs].reset_index(drop=True)
            y_client = y.iloc[idxs].reset_index(drop=True)
            client_data.append((X_client, y_client))

    # Log client data distribution
    for i, (X_client, y_client) in enumerate(client_data):
        logger.info(f"Client {i+1}: {X_client.shape[0]} samples, mean(y)={y_client.mean():.4f}, std(y)={y_client.std():.4f}")

    return client_data

def preprocess_data(X_train, X_val, X_test, scale=True, handle_outliers=True):
    """
    Preprocess data by handling outliers and scaling features.

    Args:
        X_train (DataFrame): Training features
        X_val (DataFrame): Validation features
        X_test (DataFrame): Test features
        scale (bool): Whether to scale the features
        handle_outliers (bool): Whether to handle outliers and infinity values

    Returns:
        tuple: (X_train_processed, X_val_processed, X_test_processed, scaler)
    """
    # Make copies to avoid modifying the original data
    X_train_processed = X_train.copy()
    X_val_processed = X_val.copy()
    X_test_processed = X_test.copy()

    if handle_outliers:
        # Replace infinity values with NaN
        X_train_processed = X_train_processed.replace([np.inf, -np.inf], np.nan)
        X_val_processed = X_val_processed.replace([np.inf, -np.inf], np.nan)
        X_test_processed = X_test_processed.replace([np.inf, -np.inf], np.nan)

        # Count NaN values
        nan_count = X_train_processed.isna().sum().sum()
        if nan_count > 0:
            logger.info(f"Found {nan_count} NaN values in training data")

            # Fill NaN values with median (more robust than mean)
            medians = X_train_processed.median()
            X_train_processed = X_train_processed.fillna(medians)
            X_val_processed = X_val_processed.fillna(medians)
            X_test_processed = X_test_processed.fillna(medians)
            logger.info("NaN values filled with median values")

        # Check for remaining problematic values
        if (X_train_processed.abs() > 1e10).any().any():
            logger.warning("Found very large values (>1e10) in the data")
            # Cap extreme values
            for col in X_train_processed.columns:
                q1 = X_train_processed[col].quantile(0.01)
                q3 = X_train_processed[col].quantile(0.99)
                cap_min = q1 - 3 * (q3 - q1)
                cap_max = q3 + 3 * (q3 - q1)

                # Apply capping
                X_train_processed[col] = X_train_processed[col].clip(cap_min, cap_max)
                X_val_processed[col] = X_val_processed[col].clip(cap_min, cap_max)
                X_test_processed[col] = X_test_processed[col].clip(cap_min, cap_max)

            logger.info("Extreme values capped using IQR method")

    if scale:
        try:
            # Use robust scaler if there are outliers
            from sklearn.preprocessing import RobustScaler
            scaler = RobustScaler()

            X_train_processed = pd.DataFrame(
                scaler.fit_transform(X_train_processed),
                columns=X_train_processed.columns,
                index=X_train_processed.index
            )
            X_val_processed = pd.DataFrame(
                scaler.transform(X_val_processed),
                columns=X_val_processed.columns,
                index=X_val_processed.index
            )
            X_test_processed = pd.DataFrame(
                scaler.transform(X_test_processed),
                columns=X_test_processed.columns,
                index=X_test_processed.index
            )
            logger.info("Data scaled using RobustScaler")
        except Exception as e:
            logger.warning(f"Error using RobustScaler: {e}. Falling back to StandardScaler.")

            # Fall back to standard scaler
            scaler = StandardScaler()
            X_train_processed = pd.DataFrame(
                scaler.fit_transform(X_train_processed),
                columns=X_train_processed.columns,
                index=X_train_processed.index
            )
            X_val_processed = pd.DataFrame(
                scaler.transform(X_val_processed),
                columns=X_val_processed.columns,
                index=X_val_processed.index
            )
            X_test_processed = pd.DataFrame(
                scaler.transform(X_test_processed),
                columns=X_test_processed.columns,
                index=X_test_processed.index
            )
            logger.info("Data scaled using StandardScaler")

        return X_train_processed, X_val_processed, X_test_processed, scaler
    else:
        logger.info("Data not scaled")
        return X_train_processed, X_val_processed, X_test_processed, None


def load_data_gpu(primary_file="logS_des.csv", secondary_file=None, combine=True,
                  target_col='value', device=None, pin_memory=True) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Load data directly to GPU for faster processing.

    Args:
        primary_file (str): Path to the primary data file
        secondary_file (str): Path to the secondary data file (optional)
        combine (bool): Whether to combine the datasets if secondary_file is provided
        target_col (str): Name of the target column
        device (torch.device): Device to load data to
        pin_memory (bool): Whether to pin memory for faster transfer

    Returns:
        Tuple[torch.Tensor, torch.Tensor]: (X, y) tensors on GPU
    """
    # Load data using existing function
    X, y = load_data(primary_file, secondary_file, combine, target_col)

    # Convert to tensors
    X_tensor = torch.from_numpy(X.values).float()
    y_tensor = torch.from_numpy(y.values).float()

    # Pin memory if requested and device is CUDA
    if pin_memory and device is not None and device.type == 'cuda':
        X_tensor = X_tensor.pin_memory()
        y_tensor = y_tensor.pin_memory()

    # Move to device
    if device is not None:
        X_tensor = X_tensor.to(device, non_blocking=True)
        y_tensor = y_tensor.to(device, non_blocking=True)
        logger.info(f"Data loaded to {device}")

    return X_tensor, y_tensor


def preprocess_data_gpu(X_train: Union[pd.DataFrame, torch.Tensor],
                       X_val: Union[pd.DataFrame, torch.Tensor],
                       X_test: Union[pd.DataFrame, torch.Tensor],
                       y_train: Union[pd.Series, torch.Tensor] = None,
                       y_val: Union[pd.Series, torch.Tensor] = None,
                       y_test: Union[pd.Series, torch.Tensor] = None,
                       device: Optional[torch.device] = None,
                       scale: bool = True) -> Tuple[torch.Tensor, ...]:
    """
    Preprocess data on GPU for faster processing.

    Args:
        X_train, X_val, X_test: Feature datasets
        y_train, y_val, y_test: Target datasets (optional)
        device: Device to use for processing
        scale: Whether to scale the data

    Returns:
        Tuple of GPU tensors
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    logger.info(f"Preprocessing data on {device}")

    # Convert to tensors and move to device
    def to_tensor_gpu(data):
        if isinstance(data, torch.Tensor):
            return data.to(device)
        elif isinstance(data, (pd.DataFrame, pd.Series)):
            return torch.from_numpy(data.values).float().to(device)
        else:
            return torch.from_numpy(data).float().to(device)

    X_train_gpu = to_tensor_gpu(X_train)
    X_val_gpu = to_tensor_gpu(X_val)
    X_test_gpu = to_tensor_gpu(X_test)

    results = [X_train_gpu, X_val_gpu, X_test_gpu]

    if y_train is not None:
        y_train_gpu = to_tensor_gpu(y_train)
        results.append(y_train_gpu)

    if y_val is not None:
        y_val_gpu = to_tensor_gpu(y_val)
        results.append(y_val_gpu)

    if y_test is not None:
        y_test_gpu = to_tensor_gpu(y_test)
        results.append(y_test_gpu)

    if scale:
        # Compute mean and std on training data
        mean = X_train_gpu.mean(dim=0, keepdim=True)
        std = X_train_gpu.std(dim=0, keepdim=True)

        # Avoid division by zero
        std = torch.where(std == 0, torch.ones_like(std), std)

        # Apply standardization
        results[0] = (X_train_gpu - mean) / std  # X_train
        results[1] = (X_val_gpu - mean) / std    # X_val
        results[2] = (X_test_gpu - mean) / std   # X_test

        logger.info("Data standardized on GPU")

    return tuple(results)
