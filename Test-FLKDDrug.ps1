# ============================================================================
# FLKDDrug Platform - Comprehensive PowerShell Testing Script
# ============================================================================
# This script provides a complete testing suite for the FLKDDrug platform
# Compatible with Windows 10/11 PowerShell 5.1+ and PowerShell Core 7+
# ============================================================================

param(
    [switch]$Quick,
    [switch]$Comprehensive,
    [switch]$SkipGPU,
    [string]$LogDir = "test_logs"
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Colors for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Test-Prerequisites {
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " PREREQUISITES CHECK" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    # Check Python
    try {
        $pythonVersion = python --version 2>&1
        Write-ColorOutput "[SUCCESS] Python detected: $pythonVersion" "Success"
    }
    catch {
        Write-ColorOutput "[ERROR] Python is not installed or not in PATH" "Error"
        Write-ColorOutput "Please install Python 3.8+ and add it to your PATH" "Error"
        exit 1
    }
    
    # Check if we're in the correct directory
    if (-not (Test-Path "run_strategy.py")) {
        Write-ColorOutput "[ERROR] Please run this script from the FLKDDrug root directory" "Error"
        Write-ColorOutput "Current directory should contain run_strategy.py" "Error"
        exit 1
    }
    
    Write-ColorOutput "[SUCCESS] Directory check passed" "Success"
    
    # Create logs directory
    if (-not (Test-Path $LogDir)) {
        New-Item -ItemType Directory -Path $LogDir | Out-Null
        Write-ColorOutput "[INFO] Created log directory: $LogDir" "Info"
    }
    
    return $true
}

function Test-SystemValidation {
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " SYSTEM VALIDATION" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    
    # Test system dependencies
    Write-ColorOutput "[1/4] Testing system dependencies..." "Info"
    try {
        $result = python test_system.py 2>&1
        $result | Out-File "$LogDir\system_test_$timestamp.log"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] System validation passed" "Success"
        } else {
            Write-ColorOutput "[ERROR] System test failed. Check $LogDir\system_test_$timestamp.log" "Error"
            Write-ColorOutput "Common issues:" "Warning"
            Write-ColorOutput "- Missing dependencies: pip install -r requirements.txt" "Warning"
            Write-ColorOutput "- CUDA not available: Install CUDA toolkit and PyTorch with CUDA" "Warning"
            Write-ColorOutput "- GPU drivers: Update NVIDIA drivers" "Warning"
            
            $continue = Read-Host "Continue anyway? (y/N)"
            if ($continue -ne "y") { exit 1 }
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Failed to run system test: $_" "Error"
        exit 1
    }
    
    # Test data loading
    Write-ColorOutput "`n[2/4] Testing data loading..." "Info"
    try {
        $result = python -c "from utils.data_utils import load_data; X, y = load_data(); print(f'Data loaded: X={X.shape}, y={y.shape}')" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] Data loading test passed" "Success"
            Write-ColorOutput "Result: $result" "Info"
        } else {
            Write-ColorOutput "[ERROR] Data loading failed: $result" "Error"
            exit 1
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Data loading test failed: $_" "Error"
        exit 1
    }
    
    # Test GPU functionality
    if (-not $SkipGPU) {
        Write-ColorOutput "`n[3/4] Testing GPU functionality..." "Info"
        try {
            $result = python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count() if torch.cuda.is_available() else 0}')" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "[SUCCESS] GPU functionality test passed" "Success"
                Write-ColorOutput "Result: $result" "Info"
            } else {
                Write-ColorOutput "[WARNING] GPU test failed - will use CPU mode" "Warning"
                Write-ColorOutput "Result: $result" "Warning"
            }
        }
        catch {
            Write-ColorOutput "[WARNING] GPU test failed: $_" "Warning"
        }
    } else {
        Write-ColorOutput "`n[3/4] Skipping GPU test (--SkipGPU specified)" "Warning"
    }
    
    # Test strategy imports
    Write-ColorOutput "`n[4/4] Testing strategy imports..." "Info"
    try {
        $result = python -c "from strategies.federated.fedavg import FedAvg; from strategies.distillation.vanilla_kd import VanillaKD; print('Strategy imports successful')" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] Strategy imports test passed" "Success"
        } else {
            Write-ColorOutput "[ERROR] Strategy import failed: $result" "Error"
            exit 1
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Strategy import test failed: $_" "Error"
        exit 1
    }
}

function Test-QuickFunctionality {
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " QUICK FUNCTIONALITY TESTS" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $gpuArg = if ($SkipGPU) { "" } else { "--gpu 0" }
    
    # Test original model
    Write-ColorOutput "[1/3] Testing original baseline model (2 epochs)..." "Info"
    try {
        $result = python run_strategy.py --original --epochs 2 $gpuArg 2>&1
        $result | Out-File "$LogDir\original_test_$timestamp.log"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] Original model test passed" "Success"
        } else {
            Write-ColorOutput "[ERROR] Original model test failed. Check $LogDir\original_test_$timestamp.log" "Error"
            $continue = Read-Host "Continue with other tests? (y/N)"
            if ($continue -ne "y") { exit 1 }
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Original model test failed: $_" "Error"
        exit 1
    }
    
    # Test federated learning
    Write-ColorOutput "`n[2/3] Testing federated learning (2 rounds, 2 clients)..." "Info"
    try {
        $result = python run_strategy.py --fl_strategy fedavg --n_rounds 2 --n_clients 2 $gpuArg 2>&1
        $result | Out-File "$LogDir\fedavg_test_$timestamp.log"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] Federated learning test passed" "Success"
        } else {
            Write-ColorOutput "[ERROR] Federated learning test failed. Check $LogDir\fedavg_test_$timestamp.log" "Error"
            $continue = Read-Host "Continue with other tests? (y/N)"
            if ($continue -ne "y") { exit 1 }
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Federated learning test failed: $_" "Error"
        exit 1
    }
    
    # Test knowledge distillation
    Write-ColorOutput "`n[3/3] Testing knowledge distillation (2 epochs)..." "Info"
    try {
        $result = python run_strategy.py --kd_strategy vanilla_kd --epochs 2 $gpuArg 2>&1
        $result | Out-File "$LogDir\vanilla_kd_test_$timestamp.log"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] Knowledge distillation test passed" "Success"
        } else {
            Write-ColorOutput "[ERROR] Knowledge distillation test failed. Check $LogDir\vanilla_kd_test_$timestamp.log" "Error"
            $continue = Read-Host "Continue with comprehensive tests? (y/N)"
            if ($continue -ne "y") { exit 1 }
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Knowledge distillation test failed: $_" "Error"
        exit 1
    }
}

function Test-ComprehensiveStrategies {
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " COMPREHENSIVE STRATEGY TESTING" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    if (-not $Comprehensive) {
        $runComprehensive = Read-Host "Run comprehensive strategy testing? This may take 30-60 minutes. (y/N)"
        if ($runComprehensive -ne "y") { return }
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    
    Write-ColorOutput "[INFO] Starting comprehensive strategy testing..." "Info"
    Write-ColorOutput "[INFO] This will test all available strategies with minimal parameters" "Info"
    Write-ColorOutput "[INFO] Progress will be logged to $LogDir\comprehensive_test_$timestamp.log" "Info"
    
    try {
        $result = python test_strategies.py --timeout 600 2>&1
        $result | Out-File "$LogDir\comprehensive_test_$timestamp.log"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "[SUCCESS] All strategies passed comprehensive testing" "Success"
        } else {
            Write-ColorOutput "[WARNING] Some strategies failed. Check $LogDir\comprehensive_test_$timestamp.log for details" "Warning"
        }
    }
    catch {
        Write-ColorOutput "[ERROR] Comprehensive testing failed: $_" "Error"
    }
}

function Test-PerformanceMonitoring {
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " PERFORMANCE MONITORING TEST" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    $runMonitoring = Read-Host "Test performance monitoring? (y/N)"
    if ($runMonitoring -ne "y") { return }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $gpuArg = if ($SkipGPU) { "" } else { "--gpu 0" }
    
    Write-ColorOutput "[INFO] Starting performance monitoring test (60 seconds)..." "Info"
    Write-ColorOutput "[INFO] This will monitor system resources during a short training session" "Info"
    
    try {
        # Start performance monitor in background
        $monitorJob = Start-Job -ScriptBlock {
            python monitor_performance.py --duration 60 --interval 5 --save-report
        }
        
        # Run a quick training session
        $result = python run_strategy.py --original --epochs 1 $gpuArg 2>&1
        $result | Out-File "$LogDir\monitoring_test_$timestamp.log"
        
        # Wait for monitor to complete
        Wait-Job $monitorJob | Out-Null
        Remove-Job $monitorJob
        
        Write-ColorOutput "[SUCCESS] Performance monitoring test completed" "Success"
        Write-ColorOutput "[INFO] Check for performance_report_*.json files for detailed metrics" "Info"
    }
    catch {
        Write-ColorOutput "[ERROR] Performance monitoring test failed: $_" "Error"
    }
}

function Show-FinalReport {
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " FINAL REPORT" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    Write-ColorOutput "[INFO] Test Summary:" "Info"
    Write-ColorOutput "- System validation: Completed" "Info"
    Write-ColorOutput "- Quick functionality tests: Completed" "Info"
    Write-ColorOutput "- Comprehensive testing: $(if ($Comprehensive) { 'Completed' } else { 'Optional' })" "Info"
    Write-ColorOutput "- Performance monitoring: Optional" "Info"
    
    Write-ColorOutput "`n[INFO] Log files created in $LogDir directory:" "Info"
    Get-ChildItem $LogDir -Filter "*$(Get-Date -Format 'yyyyMMdd')*" | ForEach-Object {
        Write-ColorOutput "  - $($_.Name)" "Info"
    }
    
    Write-ColorOutput "`n[INFO] Next steps:" "Info"
    Write-ColorOutput "1. Review log files for any errors or warnings" "Info"
    Write-ColorOutput "2. Run 'python run_all.py --help' to see all available options" "Info"
    Write-ColorOutput "3. Start with: python run_all.py --gpu 0 --n_clients 3 --n_rounds 5" "Info"
    Write-ColorOutput "4. Monitor performance: python monitor_performance.py --save-report --plot" "Info"
    
    Write-ColorOutput "`n============================================================================" "Header"
    Write-ColorOutput " TESTING COMPLETE" "Header"
    Write-ColorOutput "============================================================================`n" "Header"
    
    Write-ColorOutput "[SUCCESS] FLKDDrug platform testing completed successfully!" "Success"
    Write-ColorOutput "[INFO] The platform is ready for use." "Info"
    
    Write-ColorOutput "`nUseful commands:" "Info"
    Write-ColorOutput "- Quick test: python run_strategy.py --original --epochs 5 --gpu 0" "Info"
    Write-ColorOutput "- Full FL test: python run_strategy.py --fl_strategy fedavg --n_rounds 10 --gpu 0" "Info"
    Write-ColorOutput "- Combined strategy: python run_all.py --strategies fedavg_vanilla_kd --gpu 0" "Info"
    Write-ColorOutput "- Monitor performance: python monitor_performance.py --save-report --plot" "Info"
}

# Main execution
try {
    Write-ColorOutput "============================================================================" "Header"
    Write-ColorOutput " FLKDDrug Platform - Comprehensive Testing Suite" "Header"
    Write-ColorOutput "============================================================================" "Header"
    
    Test-Prerequisites
    Test-SystemValidation
    
    if (-not $Quick) {
        Test-QuickFunctionality
        Test-ComprehensiveStrategies
        Test-PerformanceMonitoring
    } else {
        Write-ColorOutput "`n[INFO] Quick mode enabled - skipping detailed tests" "Info"
    }
    
    Show-FinalReport
}
catch {
    Write-ColorOutput "`n[ERROR] Testing failed with exception: $_" "Error"
    Write-ColorOutput "Stack trace: $($_.ScriptStackTrace)" "Error"
    exit 1
}
