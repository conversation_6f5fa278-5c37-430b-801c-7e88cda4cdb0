"""
Model utilities for the FLKDDrug project with GPU acceleration.
"""

import numpy as np
from lightgbm import LGBMRegressor
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
import pickle
import os
import logging
import torch

# Import neural models
from .neural_models import NeuralNetRegressor
from .advanced_models import AdvancedNeuralRegressor

logger = logging.getLogger(__name__)

def create_model(model_type='neural_net', params=None, random_state=42, device=None, gpu_manager=None):
    """
    Create a model of the specified type with given parameters.

    Args:
        model_type (str): Type of model to create
        params (dict): Model parameters
        random_state (int): Random seed
        device (torch.device): Device for neural networks
        gpu_manager (GPUManager): GPU manager for device handling

    Returns:
        model: Created model
    """
    if params is None:
        params = {}

    # Set random state
    params['random_state'] = random_state

    if model_type.lower() == 'neural_net':
        # Use GPU device if available
        if device is None and gpu_manager is not None:
            device = gpu_manager.device
        elif device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        params['device'] = device
        return NeuralNetRegressor(**params)
    elif model_type.lower() in ['transformer', 'resnet', 'ensemble', 'advanced_neural_net']:
        # Use advanced neural network models
        if device is None and gpu_manager is not None:
            device = gpu_manager.device
        elif device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        params['device'] = device

        # Set model type if not specified
        if 'model_type' not in params:
            if model_type.lower() == 'advanced_neural_net':
                params['model_type'] = 'transformer'  # Default to transformer
            else:
                params['model_type'] = model_type.lower()

        return AdvancedNeuralRegressor(**params)
    elif model_type.lower() == 'lgbm':
        # Enable GPU for LightGBM if available and not explicitly disabled
        if device is not None and device.type == 'cuda' and 'device' not in params:
            try:
                params['device'] = 'gpu'
                params['gpu_platform_id'] = 0
                params['gpu_device_id'] = device.index if device.index is not None else 0
                logger.info("Enabled GPU acceleration for LightGBM")
            except Exception as e:
                logger.warning(f"Could not enable GPU for LightGBM: {e}")
                params.pop('device', None)
                params.pop('gpu_platform_id', None)
                params.pop('gpu_device_id', None)

        return LGBMRegressor(**params)
    elif model_type.lower() == 'rf':
        return RandomForestRegressor(**params)
    elif model_type.lower() == 'gbm':
        return GradientBoostingRegressor(**params)
    elif model_type.lower() == 'ridge':
        return Ridge(**params)
    elif model_type.lower() == 'lasso':
        return Lasso(**params)
    elif model_type.lower() == 'elasticnet':
        return ElasticNet(**params)
    elif model_type.lower() == 'svr':
        return SVR(**params)
    else:
        raise ValueError(f"Unknown model type: {model_type}")



def save_model(model, filepath, overwrite=False):
    """
    Save a model to disk.

    Args:
        model: Model to save
        filepath: Path to save the model
        overwrite: Whether to overwrite existing file

    Returns:
        bool: True if successful, False otherwise
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    # Check if file exists and overwrite is False
    if os.path.exists(filepath) and not overwrite:
        logger.warning(f"File {filepath} already exists and overwrite=False")
        return False

    try:
        with open(filepath, 'wb') as f:
            pickle.dump(model, f)
        logger.info(f"Model saved to {filepath}")
        return True
    except Exception as e:
        logger.error(f"Error saving model: {e}")
        return False

def load_model(filepath):
    """
    Load a model from disk.

    Args:
        filepath: Path to the model file

    Returns:
        model: Loaded model
    """
    try:
        with open(filepath, 'rb') as f:
            model = pickle.load(f)
        logger.info(f"Model loaded from {filepath}")
        return model
    except FileNotFoundError:
        logger.error(f"Model file {filepath} not found")
        return None
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return None

def ensemble_predict(models, X):
    """
    Make predictions using an ensemble of models.

    Args:
        models: List of models
        X: Features

    Returns:
        array: Ensemble predictions
    """
    predictions = np.array([model.predict(X) for model in models])
    return np.mean(predictions, axis=0)

def weighted_ensemble_predict(models, X, weights=None):
    """
    Make predictions using a weighted ensemble of models.

    Args:
        models: List of models
        X: Features
        weights: List of weights for each model (default: equal weights)

    Returns:
        array: Weighted ensemble predictions
    """
    if weights is None:
        weights = np.ones(len(models)) / len(models)
    else:
        # Normalize weights
        weights = np.array(weights) / np.sum(weights)

    predictions = np.array([model.predict(X) for model in models])
    return np.sum(predictions * weights[:, np.newaxis], axis=0)
