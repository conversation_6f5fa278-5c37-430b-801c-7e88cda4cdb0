"""
Model utilities for the FLKDDrug project with GPU acceleration.
"""

import numpy as np
import pandas as pd
from lightgbm import LGBMRegressor
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from bayes_opt import BayesianOptimization
import pickle
import os
import logging
import time
import torch

# Import GPU utilities and neural models
from .gpu_utils import GPUManager
from .neural_models import NeuralNetRegressor
from .advanced_models import AdvancedNeuralRegressor

logger = logging.getLogger(__name__)

def create_model(model_type='neural_net', params=None, random_state=42, device=None, gpu_manager=None):
    """
    Create a model of the specified type with given parameters.

    Args:
        model_type (str): Type of model to create
        params (dict): Model parameters
        random_state (int): Random seed
        device (torch.device): Device for neural networks
        gpu_manager (GPUManager): GPU manager for device handling

    Returns:
        model: Created model
    """
    if params is None:
        params = {}

    # Set random state
    params['random_state'] = random_state

    if model_type.lower() == 'neural_net':
        # Use GPU device if available
        if device is None and gpu_manager is not None:
            device = gpu_manager.device
        elif device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        params['device'] = device
        return NeuralNetRegressor(**params)
    elif model_type.lower() in ['transformer', 'resnet', 'ensemble', 'advanced_neural_net']:
        # Use advanced neural network models
        if device is None and gpu_manager is not None:
            device = gpu_manager.device
        elif device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        params['device'] = device

        # Set model type if not specified
        if 'model_type' not in params:
            if model_type.lower() == 'advanced_neural_net':
                params['model_type'] = 'transformer'  # Default to transformer
            else:
                params['model_type'] = model_type.lower()

        return AdvancedNeuralRegressor(**params)
    elif model_type.lower() == 'lgbm':
        # Enable GPU for LightGBM if available and not explicitly disabled
        if device is not None and device.type == 'cuda' and 'device' not in params:
            try:
                params['device'] = 'gpu'
                params['gpu_platform_id'] = 0
                params['gpu_device_id'] = device.index if device.index is not None else 0
                logger.info("Enabled GPU acceleration for LightGBM")
            except Exception as e:
                logger.warning(f"Could not enable GPU for LightGBM: {e}")
                params.pop('device', None)
                params.pop('gpu_platform_id', None)
                params.pop('gpu_device_id', None)

        return LGBMRegressor(**params)
    elif model_type.lower() == 'rf':
        return RandomForestRegressor(**params)
    elif model_type.lower() == 'gbm':
        return GradientBoostingRegressor(**params)
    elif model_type.lower() == 'ridge':
        return Ridge(**params)
    elif model_type.lower() == 'lasso':
        return Lasso(**params)
    elif model_type.lower() == 'elasticnet':
        return ElasticNet(**params)
    elif model_type.lower() == 'svr':
        return SVR(**params)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def optimize_hyperparameters(X_train, y_train, X_val, y_val, model_type='lgbm',
                            init_points=10, n_iter=50, random_state=42):
    """
    Optimize hyperparameters using Bayesian optimization.

    Args:
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        y_val: Validation targets
        model_type: Type of model to optimize
        init_points: Number of initial points for Bayesian optimization
        n_iter: Number of iterations for Bayesian optimization
        random_state: Random seed

    Returns:
        dict: Optimized parameters
    """
    start_time = time.time()
    logger.info(f"Starting hyperparameter optimization for {model_type} model")

    if model_type.lower() == 'lgbm':
        def objective(n_estimators, learning_rate, num_leaves, subsample,
                     colsample_bytree, reg_alpha, reg_lambda):
            model = LGBMRegressor(
                n_estimators=int(n_estimators),
                learning_rate=learning_rate,
                num_leaves=int(num_leaves),
                subsample=subsample,
                colsample_bytree=colsample_bytree,
                reg_alpha=reg_alpha,
                reg_lambda=reg_lambda,
                random_state=random_state
            )
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            # Negative MAE as we want to maximize
            return -np.mean(np.abs(y_val - y_pred))

        # Define parameter bounds
        pbounds = {
            'n_estimators': (50, 1000),
            'learning_rate': (0.001, 0.1),
            'num_leaves': (2, 100),
            'subsample': (0.5, 1.0),
            'colsample_bytree': (0.1, 1.0),
            'reg_alpha': (0.0, 10.0),
            'reg_lambda': (0.0, 10.0)
        }

    elif model_type.lower() == 'rf':
        def objective(n_estimators, max_depth, min_samples_split, min_samples_leaf):
            model = RandomForestRegressor(
                n_estimators=int(n_estimators),
                max_depth=int(max_depth),
                min_samples_split=int(min_samples_split),
                min_samples_leaf=int(min_samples_leaf),
                random_state=random_state
            )
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            return -np.mean(np.abs(y_val - y_pred))

        pbounds = {
            'n_estimators': (50, 500),
            'max_depth': (3, 20),
            'min_samples_split': (2, 20),
            'min_samples_leaf': (1, 10)
        }

    elif model_type.lower() == 'gbm':
        def objective(n_estimators, learning_rate, max_depth, subsample):
            model = GradientBoostingRegressor(
                n_estimators=int(n_estimators),
                learning_rate=learning_rate,
                max_depth=int(max_depth),
                subsample=subsample,
                random_state=random_state
            )
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            return -np.mean(np.abs(y_val - y_pred))

        pbounds = {
            'n_estimators': (50, 500),
            'learning_rate': (0.001, 0.1),
            'max_depth': (3, 10),
            'subsample': (0.5, 1.0)
        }

    elif model_type.lower() in ['ridge', 'lasso', 'elasticnet']:
        def objective(alpha, l1_ratio=0.5):
            if model_type.lower() == 'ridge':
                model = Ridge(alpha=alpha, random_state=random_state)
            elif model_type.lower() == 'lasso':
                model = Lasso(alpha=alpha, random_state=random_state)
            else:  # elasticnet
                model = ElasticNet(alpha=alpha, l1_ratio=l1_ratio, random_state=random_state)

            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            return -np.mean(np.abs(y_val - y_pred))

        if model_type.lower() == 'elasticnet':
            pbounds = {
                'alpha': (0.0001, 10.0),
                'l1_ratio': (0.0, 1.0)
            }
        else:
            pbounds = {
                'alpha': (0.0001, 10.0)
            }

    elif model_type.lower() == 'svr':
        def objective(C, epsilon, gamma):
            model = SVR(
                C=C,
                epsilon=epsilon,
                gamma=gamma
            )
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            return -np.mean(np.abs(y_val - y_pred))

        pbounds = {
            'C': (0.1, 100.0),
            'epsilon': (0.01, 1.0),
            'gamma': (0.001, 1.0)
        }

    else:
        raise ValueError(f"Unsupported model type for optimization: {model_type}")

    # Run Bayesian optimization
    optimizer = BayesianOptimization(
        f=objective,
        pbounds=pbounds,
        random_state=random_state
    )

    optimizer.maximize(init_points=init_points, n_iter=n_iter)

    # Get best parameters
    best_params = optimizer.max['params']

    # Convert integer parameters
    if model_type.lower() in ['lgbm', 'rf', 'gbm']:
        if 'n_estimators' in best_params:
            best_params['n_estimators'] = int(best_params['n_estimators'])
        if 'num_leaves' in best_params:
            best_params['num_leaves'] = int(best_params['num_leaves'])
        if 'max_depth' in best_params:
            best_params['max_depth'] = int(best_params['max_depth'])
        if 'min_samples_split' in best_params:
            best_params['min_samples_split'] = int(best_params['min_samples_split'])
        if 'min_samples_leaf' in best_params:
            best_params['min_samples_leaf'] = int(best_params['min_samples_leaf'])

    elapsed_time = time.time() - start_time
    logger.info(f"Hyperparameter optimization completed in {elapsed_time:.2f} seconds")
    logger.info(f"Best parameters: {best_params}")
    logger.info(f"Best score: {-optimizer.max['target']:.4f} MAE")

    return best_params

def save_model(model, filepath, overwrite=False):
    """
    Save a model to disk.

    Args:
        model: Model to save
        filepath: Path to save the model
        overwrite: Whether to overwrite existing file

    Returns:
        bool: True if successful, False otherwise
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    # Check if file exists and overwrite is False
    if os.path.exists(filepath) and not overwrite:
        logger.warning(f"File {filepath} already exists and overwrite=False")
        return False

    try:
        with open(filepath, 'wb') as f:
            pickle.dump(model, f)
        logger.info(f"Model saved to {filepath}")
        return True
    except Exception as e:
        logger.error(f"Error saving model: {e}")
        return False

def load_model(filepath):
    """
    Load a model from disk.

    Args:
        filepath: Path to the model file

    Returns:
        model: Loaded model
    """
    try:
        with open(filepath, 'rb') as f:
            model = pickle.load(f)
        logger.info(f"Model loaded from {filepath}")
        return model
    except FileNotFoundError:
        logger.error(f"Model file {filepath} not found")
        return None
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return None

def ensemble_predict(models, X):
    """
    Make predictions using an ensemble of models.

    Args:
        models: List of models
        X: Features

    Returns:
        array: Ensemble predictions
    """
    predictions = np.array([model.predict(X) for model in models])
    return np.mean(predictions, axis=0)

def weighted_ensemble_predict(models, X, weights=None):
    """
    Make predictions using a weighted ensemble of models.

    Args:
        models: List of models
        X: Features
        weights: List of weights for each model (default: equal weights)

    Returns:
        array: Weighted ensemble predictions
    """
    if weights is None:
        weights = np.ones(len(models)) / len(models)
    else:
        # Normalize weights
        weights = np.array(weights) / np.sum(weights)

    predictions = np.array([model.predict(X) for model in models])
    return np.sum(predictions * weights[:, np.newaxis], axis=0)
