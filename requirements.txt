numpy>=1.19.0
pandas>=1.1.0
scikit-learn>=0.24.0
lightgbm>=3.2.0
matplotlib>=3.3.0
seaborn>=0.11.0
bayesian-optimization>=1.2.0
torch>=1.9.0
torchvision>=0.10.0
tqdm>=4.50.0
# GPU acceleration dependencies
cupy-cuda11x>=10.0.0; sys_platform != "darwin"  # CUDA support for GPU arrays
cudf>=21.0.0; sys_platform != "darwin"  # GPU DataFrames (optional)
# Memory management
psutil>=5.8.0  # System and process utilities
# Additional ML libraries with GPU support
xgboost>=1.5.0  # XGBoost with GPU support
# Reinforcement Learning dependencies
gym>=0.21.0  # OpenAI Gym for RL environments
stable-baselines3>=1.6.0  # Stable Baselines3 for RL algorithms
tensorboard>=2.8.0  # TensorBoard for logging and visualization
