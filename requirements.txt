numpy>=1.19.0
pandas>=1.1.0
scikit-learn>=0.24.0
lightgbm>=3.2.0
matplotlib>=3.3.0
seaborn>=0.11.0
bayesian-optimization>=1.2.0
torch>=1.9.0
torchvision>=0.10.0
tqdm>=4.50.0
# GPU acceleration dependencies (Windows compatible)
cupy-cuda11x>=10.0.0; sys_platform == "win32"  # CUDA support for GPU arrays on Windows
cupy-cuda11x>=10.0.0; sys_platform == "linux"  # CUDA support for GPU arrays on Linux
cupy-cuda11x>=10.0.0; sys_platform == "linux2"  # CUDA support for GPU arrays on Linux
# Note: cudf is not available on Windows, using pandas with GPU acceleration instead
# Memory management
psutil>=5.8.0  # System and process utilities
# Additional ML libraries with GPU support
xgboost>=1.5.0  # XGBoost with GPU support
# Reinforcement Learning dependencies
gym>=0.21.0  # OpenAI Gym for RL environments
gymnasium>=0.26.0  # New Gym API (Gymnasium)
stable-baselines3>=1.6.0  # Stable Baselines3 for RL algorithms
tensorboard>=2.8.0  # TensorBoard for logging and visualization
# Advanced RL algorithms
sb3-contrib>=1.6.0  # Additional RL algorithms (TRPO, etc.)
# Multi-agent RL
pettingzoo>=1.18.0  # Multi-agent RL environments
# Hierarchical RL and advanced algorithms
tianshou>=0.4.0  # Advanced RL library with SAC, etc.
# Optional: Ray for distributed RL (Windows compatible)
ray[rllib]>=2.0.0; python_version >= "3.7"  # Distributed RL framework
