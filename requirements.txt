numpy>=1.19.0
pandas>=1.1.0
scikit-learn>=0.24.0
lightgbm>=3.2.0
matplotlib>=3.3.0
seaborn>=0.11.0
bayesian-optimization>=1.2.0
torch>=1.9.0
torchvision>=0.10.0
tqdm>=4.50.0
# GPU acceleration dependencies (Windows compatible)
cupy-cuda11x>=10.0.0; sys_platform == "win32"  # CUDA support for GPU arrays on Windows
cupy-cuda11x>=10.0.0; sys_platform == "linux"  # CUDA support for GPU arrays on Linux
cupy-cuda11x>=10.0.0; sys_platform == "linux2"  # CUDA support for GPU arrays on Linux
# Note: cudf is not available on Windows, using pandas with GPU acceleration instead
# Memory management
psutil>=5.8.0  # System and process utilities
# Additional ML libraries with GPU support
xgboost>=1.5.0  # XGBoost with GPU support
# Note: RL dependencies removed for simplicity and Windows compatibility
# The current framework focuses on proven FL and KD strategies
