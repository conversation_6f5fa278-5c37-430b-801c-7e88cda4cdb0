"""
PPO (Proximal Policy Optimization) for hyperparameter optimization and model selection.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.model_selection import cross_val_score
import time

logger = logging.getLogger(__name__)


class PolicyNetwork(nn.Module):
    """Policy network for PPO agent."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 128]):
        super(PolicyNetwork, self).__init__()
        
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*layers)
        
        # Policy head (mean and log_std for continuous actions)
        self.policy_mean = nn.Linear(prev_dim, action_dim)
        self.policy_log_std = nn.Parameter(torch.zeros(action_dim))
        
        # Value head
        self.value_head = nn.Linear(prev_dim, 1)
        
    def forward(self, state):
        """Forward pass through the network."""
        shared = self.shared_layers(state)
        
        # Policy
        mean = torch.tanh(self.policy_mean(shared))  # Normalize to [-1, 1]
        std = torch.exp(self.policy_log_std.clamp(-20, 2))
        
        # Value
        value = self.value_head(shared)
        
        return mean, std, value
    
    def get_action(self, state):
        """Sample action from policy."""
        mean, std, value = self.forward(state)
        dist = Normal(mean, std)
        action = dist.sample()
        log_prob = dist.log_prob(action).sum(dim=-1)
        
        return action, log_prob, value
    
    def evaluate_action(self, state, action):
        """Evaluate action under current policy."""
        mean, std, value = self.forward(state)
        dist = Normal(mean, std)
        log_prob = dist.log_prob(action).sum(dim=-1)
        entropy = dist.entropy().sum(dim=-1)
        
        return log_prob, entropy, value


class PPOOptimizer:
    """PPO-based hyperparameter optimizer."""
    
    def __init__(self, state_dim: int, action_dim: int, 
                 lr: float = 3e-4, gamma: float = 0.99, 
                 eps_clip: float = 0.2, k_epochs: int = 4,
                 device: torch.device = None):
        """
        Initialize PPO optimizer.
        
        Args:
            state_dim: Dimension of state space (problem features)
            action_dim: Dimension of action space (hyperparameters)
            lr: Learning rate
            gamma: Discount factor
            eps_clip: PPO clipping parameter
            k_epochs: Number of PPO update epochs
            device: Device to use
        """
        self.device = device if device is not None else torch.device('cpu')
        self.gamma = gamma
        self.eps_clip = eps_clip
        self.k_epochs = k_epochs
        
        # Networks
        self.policy = PolicyNetwork(state_dim, action_dim).to(self.device)
        self.optimizer = optim.Adam(self.policy.parameters(), lr=lr)
        
        # Storage
        self.states = []
        self.actions = []
        self.rewards = []
        self.log_probs = []
        self.values = []
        self.dones = []
        
    def store_transition(self, state, action, reward, log_prob, value, done):
        """Store transition in memory."""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.dones.append(done)
    
    def compute_returns(self):
        """Compute discounted returns."""
        returns = []
        discounted_sum = 0
        
        for reward, done in zip(reversed(self.rewards), reversed(self.dones)):
            if done:
                discounted_sum = 0
            discounted_sum = reward + self.gamma * discounted_sum
            returns.insert(0, discounted_sum)
        
        return torch.tensor(returns, dtype=torch.float32, device=self.device)
    
    def update(self):
        """Update policy using PPO."""
        if len(self.states) == 0:
            return
        
        # Convert to tensors
        states = torch.stack(self.states).to(self.device)
        actions = torch.stack(self.actions).to(self.device)
        old_log_probs = torch.stack(self.log_probs).to(self.device)
        old_values = torch.stack(self.values).squeeze().to(self.device)
        
        # Compute returns and advantages
        returns = self.compute_returns()
        advantages = returns - old_values
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # PPO update
        for _ in range(self.k_epochs):
            # Evaluate current policy
            log_probs, entropy, values = self.policy.evaluate_action(states, actions)
            values = values.squeeze()
            
            # Compute ratios
            ratios = torch.exp(log_probs - old_log_probs)
            
            # Compute surrogate losses
            surr1 = ratios * advantages
            surr2 = torch.clamp(ratios, 1 - self.eps_clip, 1 + self.eps_clip) * advantages
            
            # Policy loss
            policy_loss = -torch.min(surr1, surr2).mean()
            
            # Value loss
            value_loss = F.mse_loss(values, returns)
            
            # Entropy bonus
            entropy_loss = -entropy.mean()
            
            # Total loss
            total_loss = policy_loss + 0.5 * value_loss + 0.01 * entropy_loss
            
            # Update
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.policy.parameters(), 0.5)
            self.optimizer.step()
        
        # Clear memory
        self.clear_memory()
    
    def clear_memory(self):
        """Clear stored transitions."""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.log_probs.clear()
        self.values.clear()
        self.dones.clear()
    
    def get_action(self, state):
        """Get action from policy."""
        state = torch.tensor(state, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            action, log_prob, value = self.policy.get_action(state)
        
        return action.squeeze().cpu().numpy(), log_prob.cpu().numpy(), value.cpu().numpy()


class PPOHyperparameterOptimizer(BaseEstimator):
    """PPO-based hyperparameter optimizer for neural networks."""
    
    def __init__(self, base_model_class, param_bounds: Dict[str, Tuple[float, float]],
                 n_episodes: int = 50, max_steps: int = 20,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize PPO hyperparameter optimizer.
        
        Args:
            base_model_class: Base model class to optimize
            param_bounds: Dictionary of parameter bounds
            n_episodes: Number of optimization episodes
            max_steps: Maximum steps per episode
            device: Device to use
            random_state: Random seed
        """
        self.base_model_class = base_model_class
        self.param_bounds = param_bounds
        self.n_episodes = n_episodes
        self.max_steps = max_steps
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        # PPO parameters
        self.param_names = list(param_bounds.keys())
        self.state_dim = 10  # Problem features (data statistics)
        self.action_dim = len(self.param_names)
        
        # Initialize PPO
        self.ppo = PPOOptimizer(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            device=self.device
        )
        
        self.best_params = None
        self.best_score = -np.inf
        self.history = []
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
    
    def _get_state(self, X, y):
        """Extract state features from data."""
        # Compute data statistics as state
        state = np.array([
            X.shape[0],  # Number of samples
            X.shape[1],  # Number of features
            np.mean(X.values if hasattr(X, 'values') else X),  # Mean of features
            np.std(X.values if hasattr(X, 'values') else X),   # Std of features
            np.mean(y.values if hasattr(y, 'values') else y),  # Mean of target
            np.std(y.values if hasattr(y, 'values') else y),   # Std of target
            np.corrcoef(X.values.T if hasattr(X, 'values') else X.T).mean(),  # Mean correlation
            np.min(X.values if hasattr(X, 'values') else X),   # Min feature value
            np.max(X.values if hasattr(X, 'values') else X),   # Max feature value
            len(np.unique(y.values if hasattr(y, 'values') else y)) / len(y)  # Target diversity
        ])
        
        # Normalize state
        state = (state - np.mean(state)) / (np.std(state) + 1e-8)
        return state
    
    def _action_to_params(self, action):
        """Convert action to hyperparameters."""
        params = {}
        for i, param_name in enumerate(self.param_names):
            min_val, max_val = self.param_bounds[param_name]
            # Convert from [-1, 1] to [min_val, max_val]
            normalized_action = (action[i] + 1) / 2  # Convert to [0, 1]
            param_value = min_val + normalized_action * (max_val - min_val)
            
            # Handle integer parameters
            if param_name in ['hidden_dims', 'epochs', 'batch_size']:
                param_value = int(param_value)
            
            params[param_name] = param_value
        
        return params
    
    def _evaluate_params(self, params, X, y):
        """Evaluate hyperparameters using cross-validation."""
        try:
            # Create model with parameters
            model = self.base_model_class(**params, device=self.device)
            
            # Use cross-validation for evaluation
            scores = cross_val_score(model, X, y, cv=3, scoring='neg_mean_absolute_error')
            score = np.mean(scores)
            
            return score
        except Exception as e:
            logger.warning(f"Error evaluating parameters {params}: {e}")
            return -np.inf
    
    def optimize(self, X, y):
        """Optimize hyperparameters using PPO."""
        logger.info(f"Starting PPO hyperparameter optimization for {self.n_episodes} episodes")
        
        state = self._get_state(X, y)
        
        for episode in range(self.n_episodes):
            episode_reward = 0
            current_state = state.copy()
            
            for step in range(self.max_steps):
                # Get action from policy
                action, log_prob, value = self.ppo.get_action(current_state)
                
                # Convert action to parameters
                params = self._action_to_params(action)
                
                # Evaluate parameters
                score = self._evaluate_params(params, X, y)
                reward = score  # Use negative MAE as reward
                
                # Update best parameters
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params.copy()
                
                # Store transition
                done = (step == self.max_steps - 1)
                self.ppo.store_transition(
                    torch.tensor(current_state, dtype=torch.float32),
                    torch.tensor(action, dtype=torch.float32),
                    reward,
                    torch.tensor(log_prob, dtype=torch.float32),
                    torch.tensor(value, dtype=torch.float32),
                    done
                )
                
                episode_reward += reward
                
                # Add some noise to state for exploration
                current_state = state + np.random.normal(0, 0.1, size=state.shape)
            
            # Update policy
            self.ppo.update()
            
            # Log progress
            self.history.append(episode_reward)
            if (episode + 1) % 10 == 0:
                logger.info(f"Episode {episode + 1}/{self.n_episodes}, "
                           f"Episode Reward: {episode_reward:.4f}, "
                           f"Best Score: {self.best_score:.4f}")
        
        logger.info(f"PPO optimization completed. Best score: {self.best_score:.4f}")
        logger.info(f"Best parameters: {self.best_params}")
        
        return self.best_params, self.best_score


class RLEnhancedRegressor(BaseEstimator, RegressorMixin):
    """Regressor enhanced with reinforcement learning optimization."""
    
    def __init__(self, base_model_class=None, param_bounds: Dict[str, Tuple[float, float]] = None,
                 use_rl_optimization: bool = True, n_episodes: int = 30,
                 device: torch.device = None, random_state: int = 42):
        """
        Initialize RL-enhanced regressor.
        
        Args:
            base_model_class: Base model class to use
            param_bounds: Parameter bounds for optimization
            use_rl_optimization: Whether to use RL for hyperparameter optimization
            n_episodes: Number of RL episodes
            device: Device to use
            random_state: Random seed
        """
        self.base_model_class = base_model_class
        self.param_bounds = param_bounds
        self.use_rl_optimization = use_rl_optimization
        self.n_episodes = n_episodes
        self.device = device if device is not None else torch.device('cpu')
        self.random_state = random_state
        
        self.model = None
        self.optimizer = None
        self.best_params = None
        
        # Default parameter bounds if not provided
        if self.param_bounds is None:
            self.param_bounds = {
                'learning_rate': (1e-5, 1e-2),
                'weight_decay': (1e-6, 1e-3),
                'dropout_rate': (0.0, 0.5),
                'batch_size': (16, 128),
                'epochs': (50, 200)
            }
    
    def fit(self, X, y, X_val=None, y_val=None):
        """Fit the RL-enhanced regressor."""
        if self.use_rl_optimization and self.base_model_class is not None:
            # Use PPO for hyperparameter optimization
            self.optimizer = PPOHyperparameterOptimizer(
                base_model_class=self.base_model_class,
                param_bounds=self.param_bounds,
                n_episodes=self.n_episodes,
                device=self.device,
                random_state=self.random_state
            )
            
            # Optimize hyperparameters
            self.best_params, _ = self.optimizer.optimize(X, y)
            
            # Train final model with best parameters
            self.model = self.base_model_class(**self.best_params, device=self.device)
        else:
            # Use default parameters
            from ..advanced_models import AdvancedNeuralRegressor
            self.model = AdvancedNeuralRegressor(device=self.device, random_state=self.random_state)
        
        # Train the model
        if hasattr(self.model, 'fit') and 'X_val' in self.model.fit.__code__.co_varnames:
            self.model.fit(X, y, X_val=X_val, y_val=y_val)
        else:
            self.model.fit(X, y)
        
        return self
    
    def predict(self, X):
        """Make predictions."""
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        return self.model.predict(X)
    
    @property
    def feature_importances_(self):
        """Get feature importances."""
        if hasattr(self.model, 'feature_importances_'):
            return self.model.feature_importances_
        else:
            return np.ones(X.shape[1]) / X.shape[1] if hasattr(self, 'X') else None
