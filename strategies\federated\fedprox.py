"""
Implementation of FedProx algorithm.

Reference:
<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, M<PERSON>, <PERSON>, <PERSON><PERSON>, & <PERSON>, V. (2020).
Federated optimization in heterogeneous networks.
Proceedings of Machine Learning and Systems, 2, 429-450.
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time
from strategies.federated.fedavg import EnsembleModel

logger = logging.getLogger(__name__)

class FedProx:
    """
    FedProx algorithm for tree-based models.
    
    This implementation adapts FedProx for tree-based models by using
    a proximal term in the objective function to regularize client models
    towards the global model.
    """
    
    def __init__(self, base_model=None, n_clients=3, client_data=None, 
                 n_rounds=10, local_epochs=1, client_sample_ratio=1.0,
                 mu=0.01, random_state=42):
        """
        Initialize FedProx.
        
        Args:
            base_model: Base model to use for clients (default: LGBMRegressor)
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of communication rounds
            local_epochs: Number of local epochs per round
            client_sample_ratio: Ratio of clients to sample in each round
            mu: Proximal term coefficient
            random_state: Random seed
        """
        self.base_model = base_model if base_model is not None else LGBMRegressor(random_state=random_state)
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.client_sample_ratio = client_sample_ratio
        self.mu = mu
        self.random_state = random_state
        
        self.client_models = None
        self.global_model = None
        self.history = {
            'client_losses': [],
            'global_loss': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_val=None, y_val=None):
        """
        Train the federated model.
        
        Args:
            X_val: Validation features (for tracking progress)
            y_val: Validation targets (for tracking progress)
            
        Returns:
            self: Trained model
        """
        if self.client_data is None or len(self.client_data) == 0:
            raise ValueError("No client data provided")
        
        start_time = time.time()
        logger.info(f"Starting FedProx training with {self.n_clients} clients, "
                   f"{self.n_rounds} rounds, and mu={self.mu}")
        
        # Initialize client models
        self.client_models = [clone(self.base_model) for _ in range(self.n_clients)]
        
        # Initialize global model
        global_preds = None
        
        # Training loop
        for round_idx in range(self.n_rounds):
            round_start = time.time()
            logger.info(f"Round {round_idx+1}/{self.n_rounds}")
            
            # Sample clients
            n_sampled = max(1, int(self.client_sample_ratio * self.n_clients))
            sampled_clients = np.random.choice(self.n_clients, n_sampled, replace=False)
            
            # Train client models
            client_losses = []
            for client_idx in sampled_clients:
                X_client, y_client = self.client_data[client_idx]
                
                # Get global model predictions for proximal term
                if global_preds is not None:
                    # For tree-based models, we use predictions as proximal targets
                    global_client_preds = global_preds[client_idx]
                    
                    # Create combined target with proximal regularization
                    # y_combined = (1-mu)*y_client + mu*global_client_preds
                    # But we use sample weights instead for LightGBM
                    
                    # Train with proximal regularization
                    for _ in range(self.local_epochs):
                        # Create sample weights that emphasize examples where client and global disagree
                        if round_idx > 0:  # Skip proximal term in first round
                            client_preds = self.client_models[client_idx].predict(X_client)
                            prox_diff = np.abs(client_preds - global_client_preds)
                            # Higher weight for examples with larger difference
                            sample_weight = 1.0 + self.mu * prox_diff
                            
                            self.client_models[client_idx].fit(
                                X_client, y_client, 
                                sample_weight=sample_weight
                            )
                        else:
                            self.client_models[client_idx].fit(X_client, y_client)
                else:
                    # First round, no proximal term
                    for _ in range(self.local_epochs):
                        self.client_models[client_idx].fit(X_client, y_client)
                
                # Evaluate client model
                if X_val is not None and y_val is not None:
                    y_pred = self.client_models[client_idx].predict(X_val)
                    loss = np.mean(np.abs(y_val - y_pred))
                    client_losses.append(loss)
                    logger.debug(f"Client {client_idx} loss: {loss:.4f}")
            
            # Update global model
            self.global_model = self._create_ensemble_model()
            
            # Update global predictions for next round's proximal term
            global_preds = []
            for client_idx in range(self.n_clients):
                X_client, _ = self.client_data[client_idx]
                global_preds.append(self.global_model.predict(X_client))
            
            # Evaluate global model
            if X_val is not None and y_val is not None:
                y_pred = self.global_model.predict(X_val)
                global_loss = np.mean(np.abs(y_val - y_pred))
                self.history['client_losses'].append(client_losses)
                self.history['global_loss'].append(global_loss)
                
                round_time = time.time() - round_start
                logger.info(f"Round {round_idx+1} completed in {round_time:.2f}s. "
                           f"Global loss: {global_loss:.4f}, "
                           f"Avg client loss: {np.mean(client_losses):.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"FedProx training completed in {total_time:.2f}s")
        
        return self
    
    def _create_ensemble_model(self):
        """
        Create an ensemble model from client models.
        
        Returns:
            EnsembleModel: Ensemble of client models
        """
        return EnsembleModel(self.client_models)
    
    def predict(self, X):
        """
        Make predictions using the global model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        if self.global_model is None:
            raise ValueError("Model has not been trained yet")
        
        return self.global_model.predict(X)
