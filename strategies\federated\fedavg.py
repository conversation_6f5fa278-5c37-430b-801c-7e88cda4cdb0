"""
Implementation of Federated Averaging (FedAvg) algorithm.

Reference:
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, B. A. (2017).
Communication-efficient learning of deep networks from decentralized data.
In Artificial intelligence and statistics (pp. 1273-1282).
"""

import numpy as np
import pandas as pd
import logging
from lightgbm import LGBMRegressor
from sklearn.base import clone
import time

logger = logging.getLogger(__name__)

class FedAvg:
    """
    Federated Averaging (FedAvg) algorithm for tree-based models.
    
    This implementation adapts FedAvg for tree-based models by averaging
    the predictions of client models rather than model parameters.
    """
    
    def __init__(self, base_model=None, n_clients=3, client_data=None, 
                 n_rounds=10, local_epochs=1, client_sample_ratio=1.0,
                 random_state=42):
        """
        Initialize FedAvg.
        
        Args:
            base_model: Base model to use for clients (default: LGBMRegressor)
            n_clients: Number of clients
            client_data: List of (X, y) tuples for each client
            n_rounds: Number of communication rounds
            local_epochs: Number of local epochs per round
            client_sample_ratio: Ratio of clients to sample in each round
            random_state: Random seed
        """
        self.base_model = base_model if base_model is not None else LGBMRegressor(random_state=random_state)
        self.n_clients = n_clients
        self.client_data = client_data
        self.n_rounds = n_rounds
        self.local_epochs = local_epochs
        self.client_sample_ratio = client_sample_ratio
        self.random_state = random_state
        
        self.client_models = None
        self.global_model = None
        self.history = {
            'client_losses': [],
            'global_loss': []
        }
        
        np.random.seed(random_state)
    
    def fit(self, X_val=None, y_val=None):
        """
        Train the federated model.
        
        Args:
            X_val: Validation features (for tracking progress)
            y_val: Validation targets (for tracking progress)
            
        Returns:
            self: Trained model
        """
        if self.client_data is None or len(self.client_data) == 0:
            raise ValueError("No client data provided")
        
        start_time = time.time()
        logger.info(f"Starting FedAvg training with {self.n_clients} clients and {self.n_rounds} rounds")
        
        # Initialize client models
        self.client_models = [clone(self.base_model) for _ in range(self.n_clients)]
        
        # Training loop
        for round_idx in range(self.n_rounds):
            round_start = time.time()
            logger.info(f"Round {round_idx+1}/{self.n_rounds}")
            
            # Sample clients
            n_sampled = max(1, int(self.client_sample_ratio * self.n_clients))
            sampled_clients = np.random.choice(self.n_clients, n_sampled, replace=False)
            
            # Train client models
            client_losses = []
            for client_idx in sampled_clients:
                X_client, y_client = self.client_data[client_idx]
                
                # Train for multiple local epochs
                for _ in range(self.local_epochs):
                    self.client_models[client_idx].fit(X_client, y_client)
                
                # Evaluate client model
                if X_val is not None and y_val is not None:
                    y_pred = self.client_models[client_idx].predict(X_val)
                    loss = np.mean(np.abs(y_val - y_pred))
                    client_losses.append(loss)
                    logger.debug(f"Client {client_idx} loss: {loss:.4f}")
            
            # Update global model (for tree-based models, we use ensemble prediction)
            self.global_model = self._create_ensemble_model()
            
            # Evaluate global model
            if X_val is not None and y_val is not None:
                y_pred = self.global_model.predict(X_val)
                global_loss = np.mean(np.abs(y_val - y_pred))
                self.history['client_losses'].append(client_losses)
                self.history['global_loss'].append(global_loss)
                
                round_time = time.time() - round_start
                logger.info(f"Round {round_idx+1} completed in {round_time:.2f}s. "
                           f"Global loss: {global_loss:.4f}, "
                           f"Avg client loss: {np.mean(client_losses):.4f}")
        
        total_time = time.time() - start_time
        logger.info(f"FedAvg training completed in {total_time:.2f}s")
        
        return self
    
    def _create_ensemble_model(self):
        """
        Create an ensemble model from client models.
        
        Returns:
            EnsembleModel: Ensemble of client models
        """
        return EnsembleModel(self.client_models)
    
    def predict(self, X):
        """
        Make predictions using the global model.
        
        Args:
            X: Features
            
        Returns:
            array: Predictions
        """
        if self.global_model is None:
            raise ValueError("Model has not been trained yet")
        
        return self.global_model.predict(X)

class EnsembleModel:
    """
    Ensemble model that averages predictions from multiple models.
    """
    
    def __init__(self, models):
        """
        Initialize ensemble model.
        
        Args:
            models: List of models
        """
        self.models = models
    
    def predict(self, X):
        """
        Make predictions by averaging predictions from all models.
        
        Args:
            X: Features
            
        Returns:
            array: Averaged predictions
        """
        predictions = np.array([model.predict(X) for model in self.models])
        return np.mean(predictions, axis=0)
    
    def __getattr__(self, name):
        """
        Forward attribute access to the first model.
        
        This allows accessing attributes like feature_importances_.
        
        Args:
            name: Attribute name
            
        Returns:
            Attribute value
        """
        if name == 'feature_importances_' and hasattr(self.models[0], 'feature_importances_'):
            # Average feature importances across models
            importances = np.array([model.feature_importances_ for model in self.models 
                                   if hasattr(model, 'feature_importances_')])
            return np.mean(importances, axis=0)
        
        # Forward to first model
        return getattr(self.models[0], name)
