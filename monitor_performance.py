#!/usr/bin/env python3
"""
Performance Monitoring Script for FLKDDrug Platform
Real-time monitoring of GPU, CPU, memory, and training metrics.
"""

import os
import sys
import time
import threading
import logging
import json
from datetime import datetime
import psutil
import matplotlib.pyplot as plt
import numpy as np
from collections import deque

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Real-time performance monitoring class."""
    
    def __init__(self, log_interval=5, max_history=100):
        self.log_interval = log_interval
        self.max_history = max_history
        self.monitoring = False
        
        # Data storage
        self.timestamps = deque(maxlen=max_history)
        self.cpu_usage = deque(maxlen=max_history)
        self.memory_usage = deque(maxlen=max_history)
        self.gpu_usage = deque(maxlen=max_history)
        self.gpu_memory = deque(maxlen=max_history)
        
        # GPU monitoring setup
        self.gpu_available = self._check_gpu_availability()
        
    def _check_gpu_availability(self):
        """Check if GPU monitoring is available."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            try:
                import pynvml
                pynvml.nvmlInit()
                return True
            except ImportError:
                logger.warning("No GPU monitoring available (install torch or nvidia-ml-py)")
                return False
    
    def _get_gpu_stats(self):
        """Get GPU statistics."""
        if not self.gpu_available:
            return 0, 0
        
        try:
            import torch
            if torch.cuda.is_available():
                # Get GPU utilization (approximation)
                gpu_memory_used = torch.cuda.memory_allocated(0)
                gpu_memory_total = torch.cuda.max_memory_allocated(0)
                gpu_memory_percent = (gpu_memory_used / gpu_memory_total * 100) if gpu_memory_total > 0 else 0
                
                # For GPU utilization, we'll use a simple heuristic
                gpu_utilization = min(100, gpu_memory_percent * 1.2)
                
                return gpu_utilization, gpu_memory_percent
        except Exception as e:
            logger.debug(f"GPU stats error: {e}")
        
        try:
            import pynvml
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            
            # Get GPU utilization
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_util = util.gpu
            
            # Get memory info
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            gpu_mem_percent = (mem_info.used / mem_info.total) * 100
            
            return gpu_util, gpu_mem_percent
        except Exception as e:
            logger.debug(f"NVML stats error: {e}")
            return 0, 0
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.monitoring:
            timestamp = datetime.now()
            
            # Get system stats
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            gpu_util, gpu_mem = self._get_gpu_stats()
            
            # Store data
            self.timestamps.append(timestamp)
            self.cpu_usage.append(cpu_percent)
            self.memory_usage.append(memory_percent)
            self.gpu_usage.append(gpu_util)
            self.gpu_memory.append(gpu_mem)
            
            # Log current stats
            logger.info(f"CPU: {cpu_percent:5.1f}% | "
                       f"RAM: {memory_percent:5.1f}% | "
                       f"GPU: {gpu_util:5.1f}% | "
                       f"VRAM: {gpu_mem:5.1f}%")
            
            time.sleep(self.log_interval)
    
    def start_monitoring(self):
        """Start performance monitoring."""
        if self.monitoring:
            logger.warning("Monitoring already started")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=10)
        logger.info("Performance monitoring stopped")
    
    def save_report(self, filename=None):
        """Save performance report."""
        if filename is None:
            filename = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'timestamps': [t.isoformat() for t in self.timestamps],
            'cpu_usage': list(self.cpu_usage),
            'memory_usage': list(self.memory_usage),
            'gpu_usage': list(self.gpu_usage),
            'gpu_memory': list(self.gpu_memory),
            'summary': {
                'avg_cpu': np.mean(self.cpu_usage) if self.cpu_usage else 0,
                'max_cpu': np.max(self.cpu_usage) if self.cpu_usage else 0,
                'avg_memory': np.mean(self.memory_usage) if self.memory_usage else 0,
                'max_memory': np.max(self.memory_usage) if self.memory_usage else 0,
                'avg_gpu': np.mean(self.gpu_usage) if self.gpu_usage else 0,
                'max_gpu': np.max(self.gpu_usage) if self.gpu_usage else 0,
                'avg_gpu_memory': np.mean(self.gpu_memory) if self.gpu_memory else 0,
                'max_gpu_memory': np.max(self.gpu_memory) if self.gpu_memory else 0,
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Performance report saved to {filename}")
        return filename
    
    def plot_performance(self, save_plot=True):
        """Plot performance metrics."""
        if not self.timestamps:
            logger.warning("No data to plot")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('System Performance Monitoring', fontsize=16)
        
        # Convert timestamps to relative seconds
        start_time = self.timestamps[0]
        time_seconds = [(t - start_time).total_seconds() for t in self.timestamps]
        
        # CPU Usage
        axes[0, 0].plot(time_seconds, self.cpu_usage, 'b-', linewidth=2)
        axes[0, 0].set_title('CPU Usage (%)')
        axes[0, 0].set_ylabel('Usage (%)')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].set_ylim(0, 100)
        
        # Memory Usage
        axes[0, 1].plot(time_seconds, self.memory_usage, 'g-', linewidth=2)
        axes[0, 1].set_title('Memory Usage (%)')
        axes[0, 1].set_ylabel('Usage (%)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].set_ylim(0, 100)
        
        # GPU Usage
        axes[1, 0].plot(time_seconds, self.gpu_usage, 'r-', linewidth=2)
        axes[1, 0].set_title('GPU Usage (%)')
        axes[1, 0].set_xlabel('Time (seconds)')
        axes[1, 0].set_ylabel('Usage (%)')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_ylim(0, 100)
        
        # GPU Memory
        axes[1, 1].plot(time_seconds, self.gpu_memory, 'm-', linewidth=2)
        axes[1, 1].set_title('GPU Memory (%)')
        axes[1, 1].set_xlabel('Time (seconds)')
        axes[1, 1].set_ylabel('Usage (%)')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].set_ylim(0, 100)
        
        plt.tight_layout()
        
        if save_plot:
            plot_filename = f"performance_plot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            logger.info(f"Performance plot saved to {plot_filename}")
        
        plt.show()
        return fig

def main():
    """Main function for standalone monitoring."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Performance Monitor for FLKDDrug')
    parser.add_argument('--interval', type=int, default=5, help='Monitoring interval in seconds')
    parser.add_argument('--duration', type=int, default=300, help='Monitoring duration in seconds')
    parser.add_argument('--save-report', action='store_true', help='Save performance report')
    parser.add_argument('--plot', action='store_true', help='Show performance plot')
    
    args = parser.parse_args()
    
    monitor = PerformanceMonitor(log_interval=args.interval)
    
    try:
        monitor.start_monitoring()
        logger.info(f"Monitoring for {args.duration} seconds...")
        time.sleep(args.duration)
        
    except KeyboardInterrupt:
        logger.info("Monitoring interrupted by user")
    
    finally:
        monitor.stop_monitoring()
        
        if args.save_report:
            monitor.save_report()
        
        if args.plot:
            monitor.plot_performance()

if __name__ == "__main__":
    main()
