2025-05-26 23:13:43,422 - utils.gpu_utils - INFO - Using GPU: NVIDIA GeForce GTX 1050
2025-05-26 23:13:43,423 - utils.gpu_utils - INFO - GPU Memory: 4.3 GB
2025-05-26 23:13:43,423 - utils.gpu_utils - INFO - Applying Windows-specific GPU optimizations
2025-05-26 23:13:43,423 - utils.gpu_utils - INFO - Enabled Flash SDP for Windows
2025-05-26 23:13:43,423 - utils.gpu_utils - INFO - Mixed precision training enabled
2025-05-26 23:13:43,423 - utils.gpu_utils - INFO - CUDNN benchmark mode enabled
2025-05-26 23:13:43,423 - utils.gpu_utils - INFO - TF32 enabled for Ampere GPUs
2025-05-26 23:13:43,423 - __main__ - INFO - Using GPU 0: cuda:0
2025-05-26 23:13:43,424 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.00GB, Free: 4.29GB, Utilization: 0.0%
2025-05-26 23:13:43,426 - __main__ - INFO - Loading data...
2025-05-26 23:13:43,762 - utils.data_utils - INFO - Loaded primary data from logS_des.csv: 14594 samples, 199 features
2025-05-26 23:13:43,763 - __main__ - INFO - Splitting data...
2025-05-26 23:13:43,885 - utils.data_utils - INFO - Data split: train=9340, val=2335, test=2919
2025-05-26 23:13:43,888 - __main__ - INFO - Preprocessing data on GPU...
2025-05-26 23:13:43,888 - utils.data_utils - INFO - Preprocessing data on cuda:0
2025-05-26 23:13:44,271 - utils.data_utils - INFO - Data standardized on GPU
2025-05-26 23:13:44,285 - utils.gpu_utils - INFO - GPU Memory - Allocated: 0.00GB, Cached: 0.04GB, Free: 4.25GB, Utilization: 1.0%
2025-05-26 23:13:44,286 - __main__ - INFO - Creating client data for 2 clients...
2025-05-26 23:13:44,311 - utils.data_utils - INFO - Client 1: 4670 samples, mean(y)=-2.8065, std(y)=2.1641
2025-05-26 23:13:44,312 - utils.data_utils - INFO - Client 2: 4670 samples, mean(y)=-2.9271, std(y)=2.2397
2025-05-26 23:13:50,944 - utils.visualization - INFO - Plot saved to results/plots\client_data_distribution.png
2025-05-26 23:13:50,945 - __main__ - INFO - Running federated learning strategy: fedavg
2025-05-26 23:13:50,952 - strategies.federated.fedavg - INFO - Starting FedAvg training with 2 clients and 3 rounds
2025-05-26 23:13:50,957 - strategies.federated.fedavg - INFO - Round 1/3
2025-05-26 23:13:50,957 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 23:13:50,984 - utils.neural_models - WARNING - NaN/inf values detected in input features, replacing with zeros
2025-05-26 23:14:03,230 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 4.580220
2025-05-26 23:14:07,297 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 2.204381
2025-05-26 23:14:11,439 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 1.671270
2025-05-26 23:14:15,672 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 1.317348
2025-05-26 23:14:19,468 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 1.138128
2025-05-26 23:14:23,221 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 1.085281
2025-05-26 23:14:26,886 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 1.091769
2025-05-26 23:14:30,720 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 0.994272
2025-05-26 23:14:34,413 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 0.989790
2025-05-26 23:14:38,187 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 0.998408
2025-05-26 23:14:38,187 - utils.neural_models - INFO - Neural network training completed in 47.23s
2025-05-26 23:14:38,214 - utils.neural_models - WARNING - NaN values detected in predictions, replacing with zeros
2025-05-26 23:14:38,225 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 23:14:38,227 - utils.neural_models - WARNING - NaN/inf values detected in input features, replacing with zeros
2025-05-26 23:14:42,373 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 5.784400
2025-05-26 23:14:46,979 - utils.neural_models - INFO - Epoch 20/100, Train Loss: 2.330123
2025-05-26 23:14:51,503 - utils.neural_models - INFO - Epoch 30/100, Train Loss: 1.618045
2025-05-26 23:14:55,824 - utils.neural_models - INFO - Epoch 40/100, Train Loss: 1.344078
2025-05-26 23:15:00,220 - utils.neural_models - INFO - Epoch 50/100, Train Loss: 1.204037
2025-05-26 23:15:05,073 - utils.neural_models - INFO - Epoch 60/100, Train Loss: 1.133593
2025-05-26 23:15:10,139 - utils.neural_models - INFO - Epoch 70/100, Train Loss: 1.080317
2025-05-26 23:15:15,459 - utils.neural_models - INFO - Epoch 80/100, Train Loss: 1.025893
2025-05-26 23:15:20,622 - utils.neural_models - INFO - Epoch 90/100, Train Loss: 1.034995
2025-05-26 23:15:25,370 - utils.neural_models - INFO - Epoch 100/100, Train Loss: 1.024177
2025-05-26 23:15:25,370 - utils.neural_models - INFO - Neural network training completed in 47.14s
2025-05-26 23:15:25,379 - utils.neural_models - WARNING - NaN values detected in predictions, replacing with zeros
2025-05-26 23:15:25,387 - utils.neural_models - WARNING - NaN values detected in predictions, replacing with zeros
2025-05-26 23:15:25,393 - utils.neural_models - WARNING - NaN values detected in predictions, replacing with zeros
2025-05-26 23:15:25,401 - strategies.federated.fedavg - INFO - Round 1 completed in 94.44s. Global loss: 3.0064, Avg client loss: 3.0064
2025-05-26 23:15:25,401 - strategies.federated.fedavg - INFO - Round 2/3
2025-05-26 23:15:25,402 - utils.neural_models - INFO - Training neural network on cuda:0
2025-05-26 23:15:25,404 - utils.neural_models - WARNING - NaN/inf values detected in input features, replacing with zeros
2025-05-26 23:15:30,565 - utils.neural_models - INFO - Epoch 10/100, Train Loss: 4.129000
