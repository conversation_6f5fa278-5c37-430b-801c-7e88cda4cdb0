"""
Evaluation utilities for the FLKDDrug project.
"""

import numpy as np
import pandas as pd
from sklearn.metrics import (
    mean_absolute_error,
    mean_squared_error,
    r2_score,
    explained_variance_score,
    mean_absolute_percentage_error
)
import logging
import json
import os
from datetime import datetime

logger = logging.getLogger(__name__)

def evaluate_model(model, X, y_true, prefix=""):
    """
    Evaluate a model on given data.

    Args:
        model: Trained model with predict method
        X: Features
        y_true: True target values
        prefix: Prefix for metric names (e.g., 'train', 'val', 'test')

    Returns:
        dict: Dictionary of performance metrics
    """
    y_pred = model.predict(X)

    # Calculate metrics
    mae = mean_absolute_error(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)
    evs = explained_variance_score(y_true, y_pred)

    # Try to calculate MAPE, but handle zero values
    try:
        mape = mean_absolute_percentage_error(y_true, y_pred)
    except:
        # Handle case where y_true contains zeros
        mape = np.mean(np.abs((y_true - y_pred) / np.maximum(np.abs(y_true), 1e-10))) * 100

    # Create metrics dictionary
    metrics = {
        f"{prefix}MAE": mae,
        f"{prefix}MSE": mse,
        f"{prefix}RMSE": rmse,
        f"{prefix}R2": r2,
        f"{prefix}EVS": evs,
        f"{prefix}MAPE": mape
    }

    # Log metrics
    metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
    logger.info(f"Model evaluation - {metrics_str}")

    return metrics, y_pred

def compare_models(models_dict, X, y_true, prefix=""):
    """
    Compare multiple models on the same dataset.

    Args:
        models_dict: Dictionary of {model_name: model}
        X: Features
        y_true: True target values
        prefix: Prefix for metric names

    Returns:
        DataFrame: Comparison of model performances
    """
    results = {}

    for model_name, model in models_dict.items():
        metrics, _ = evaluate_model(model, X, y_true, prefix="")
        results[model_name] = metrics

    # Convert to DataFrame for easy comparison
    results_df = pd.DataFrame(results).T

    # Log comparison
    logger.info(f"\n{prefix} Model Comparison:\n{results_df}")

    return results_df

def log_results(metrics, strategy_name, output_dir="results/logs"):
    """
    Log evaluation results to a file.

    Args:
        metrics: Dictionary of performance metrics
        strategy_name: Name of the strategy
        output_dir: Directory to save logs

    Returns:
        str: Path to the log file
    """
    os.makedirs(output_dir, exist_ok=True)

    # Create timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create log file name
    log_file = os.path.join(output_dir, f"{strategy_name}_{timestamp}.json")

    # Add timestamp to metrics
    metrics["timestamp"] = timestamp
    metrics["strategy"] = strategy_name

    # Convert numpy types to Python types for JSON serialization
    def convert_numpy_types(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        else:
            return obj

    metrics = convert_numpy_types(metrics)

    # Write to file
    with open(log_file, 'w') as f:
        json.dump(metrics, f, indent=4)

    logger.info(f"Results logged to {log_file}")

    return log_file

def aggregate_results(log_dir="results/logs", output_file="results/aggregate_results.csv"):
    """
    Aggregate results from multiple log files.

    Args:
        log_dir: Directory containing log files
        output_file: Path to save aggregated results

    Returns:
        DataFrame: Aggregated results
    """
    results = []

    # Get all JSON files in the log directory
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.json')]

    for log_file in log_files:
        with open(os.path.join(log_dir, log_file), 'r') as f:
            data = json.load(f)
            results.append(data)

    # Convert to DataFrame
    results_df = pd.DataFrame(results)

    # Save to CSV
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    results_df.to_csv(output_file, index=False)

    logger.info(f"Aggregated results saved to {output_file}")

    return results_df

def calculate_improvement(baseline_metrics, new_metrics):
    """
    Calculate improvement over baseline.

    Args:
        baseline_metrics: Dictionary of baseline performance metrics
        new_metrics: Dictionary of new performance metrics

    Returns:
        dict: Dictionary of improvement percentages
    """
    improvement = {}

    for metric in baseline_metrics:
        if metric in new_metrics:
            # For R2 and EVS, higher is better
            if metric.endswith('R2') or metric.endswith('EVS'):
                if baseline_metrics[metric] > 0:  # Avoid division by zero or negative values
                    improvement[metric] = (new_metrics[metric] - baseline_metrics[metric]) / abs(baseline_metrics[metric]) * 100
                else:
                    improvement[metric] = np.inf if new_metrics[metric] > baseline_metrics[metric] else -np.inf
            # For other metrics (MAE, MSE, RMSE, MAPE), lower is better
            else:
                improvement[metric] = (baseline_metrics[metric] - new_metrics[metric]) / baseline_metrics[metric] * 100

    # Log improvement
    improvement_str = ", ".join([f"{k}: {v:.2f}%" for k, v in improvement.items()])
    logger.info(f"Improvement over baseline: {improvement_str}")

    return improvement
