"""
Test script to verify data loading and preprocessing.
"""

import logging
import pandas as pd
import numpy as np
from utils.data_utils import load_data, split_data, preprocess_data

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """Test data loading and preprocessing."""
    # Test loading data
    logger.info("Testing data loading...")
    try:
        X, y = load_data(
            primary_file='logS_des.csv',
            secondary_file=None,
            combine=True
        )
        logger.info(f"Successfully loaded data: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Check for infinity values
        inf_count = np.isinf(X.values).sum()
        if inf_count > 0:
            logger.warning(f"Found {inf_count} infinity values in the data")
        
        # Check for NaN values
        nan_count = np.isnan(X.values).sum()
        if nan_count > 0:
            logger.warning(f"Found {nan_count} NaN values in the data")
        
        # Split data
        logger.info("Testing data splitting...")
        X_train, X_val, X_test, y_train, y_val, y_test = split_data(
            X, y,
            test_size=0.2,
            val_size=0.2,
            random_state=42
        )
        
        # Preprocess data
        logger.info("Testing data preprocessing...")
        X_train_processed, X_val_processed, X_test_processed, scaler = preprocess_data(
            X_train, X_val, X_test,
            scale=True,
            handle_outliers=True
        )
        
        # Check for infinity values after preprocessing
        inf_count = np.isinf(X_train_processed.values).sum()
        if inf_count > 0:
            logger.error(f"Found {inf_count} infinity values after preprocessing")
        else:
            logger.info("No infinity values found after preprocessing")
        
        # Check for NaN values after preprocessing
        nan_count = np.isnan(X_train_processed.values).sum()
        if nan_count > 0:
            logger.error(f"Found {nan_count} NaN values after preprocessing")
        else:
            logger.info("No NaN values found after preprocessing")
        
        logger.info("All tests passed!")
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
